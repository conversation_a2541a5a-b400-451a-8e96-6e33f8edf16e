# WordPress 组件化开发实施计划
*硬核指南 - 组件化架构实施指南*

## 🎯 实施概览

### 开发阶段
1. **基础架构搭建** (Phase 1) - 核心系统和工具类
2. **布局组件开发** (Phase 2) - 页面框架组件
3. **内容组件开发** (Phase 3) - 功能性组件
4. **交互组件开发** (Phase 4) - 高级交互功能
5. **优化和测试** (Phase 5) - 性能优化和质量保证

### 技术栈
- **PHP 7.4+** - 组件类和业务逻辑
- **WordPress 5.8+** - 核心框架和API
- **Vanilla JavaScript (ES6+)** - 客户端交互
- **SCSS** - 样式预处理
- **Webpack** - 资源构建

---

## 📁 项目文件结构

```
yinhedaohang/
├── style.css                     # WordPress主题样式表
├── functions.php                 # 主题核心功能
├── index.php                     # 主页模板
├── header.php                    # 头部模板
├── footer.php                    # 底部模板
├── sidebar.php                   # 侧边栏模板
│
├── includes/                     # 核心功能类
│   ├── class-theme-setup.php     # 主题初始化
│   ├── class-component-loader.php # 组件加载器
│   ├── class-asset-manager.php   # 资源管理
│   ├── class-ajax-handler.php    # AJAX处理
│   ├── class-security.php        # 安全管理
│   ├── class-cache-manager.php   # 缓存管理
│   └── class-customizer.php      # 主题定制
│
├── components/                   # 组件目录
│   ├── layout/                   # 布局组件
│   │   ├── sidebar-nav.php       # 侧边栏导航
│   │   ├── header-system.php     # 头部系统
│   │   ├── main-content.php      # 主内容区
│   │   └── footer-area.php       # 底部区域
│   │
│   ├── content/                  # 内容组件
│   │   ├── website-card.php      # 网站卡片
│   │   ├── category-header.php   # 分类头部
│   │   ├── site-grid.php         # 网站网格
│   │   └── promotional-card.php  # 推广卡片
│   │
│   ├── forms/                    # 表单组件
│   │   ├── search-interface.php  # 搜索界面
│   │   ├── mini-search.php       # 迷你搜索
│   │   └── filter-form.php       # 筛选表单
│   │
│   ├── navigation/               # 导航组件
│   │   ├── breadcrumb.php        # 面包屑导航
│   │   ├── pagination.php        # 分页组件
│   │   └── filter-system.php     # 筛选系统
│   │
│   ├── overlays/                 # 覆盖层组件
│   │   ├── modal-system.php      # 模态窗口
│   │   ├── tooltip.php           # 工具提示
│   │   └── loading-overlay.php   # 加载覆盖层
│   │
│   └── utilities/                # 工具组件
│       ├── theme-switcher.php    # 主题切换器
│       ├── visit-tracker.php     # 访问跟踪
│       ├── icon-system.php       # 图标系统
│       └── responsive-helper.php # 响应式辅助
│
├── assets/                       # 静态资源
│   ├── src/                      # 源文件
│   │   ├── scss/                 # SCSS样式源文件
│   │   │   ├── base/             # 基础样式
│   │   │   ├── components/       # 组件样式
│   │   │   ├── layout/           # 布局样式
│   │   │   ├── utilities/        # 工具样式
│   │   │   └── main.scss         # 主样式文件
│   │   │
│   │   └── js/                   # JavaScript源文件
│   │       ├── components/       # 组件脚本
│   │       ├── utilities/        # 工具函数
│   │       └── main.js           # 主脚本文件
│   │
│   └── dist/                     # 构建输出
│       ├── css/                  # 编译后的CSS
│       ├── js/                   # 编译后的JS
│       └── images/               # 优化后的图片
│
├── templates/                    # 页面模板
│   ├── single-sites.php          # 单个网站页面
│   ├── archive-sites.php         # 网站归档页面
│   ├── taxonomy-site_category.php # 分类页面
│   └── taxonomy-sitetag.php      # 标签页面
│
├── admin/                        # 管理后台
│   ├── class-admin-interface.php # 管理界面
│   ├── class-site-manager.php    # 网站管理
│   └── templates/                # 管理模板
│
├── languages/                    # 国际化文件
│   ├── yinghe.pot                # 翻译模板
│   ├── zh_CN.po                  # 中文翻译
│   └── zh_CN.mo                  # 中文翻译(编译)
│
├── vendor/                       # Composer依赖
├── node_modules/                 # NPM依赖
├── webpack.config.js             # Webpack配置
├── package.json                  # NPM配置
├── composer.json                 # Composer配置
└── README.md                     # 项目说明
```

---

## 🏗️ Phase 1: 基础架构搭建

### 1.1 组件加载系统

#### 组件加载器类
```php
<?php
/**
 * 文件: includes/class-component-loader.php
 * 组件自动加载和管理系统
 */

class YingheComponentLoader {
    private static $instance = null;
    private $loaded_components = [];
    private $component_registry = [];
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->register_component_paths();
        add_action('after_setup_theme', [$this, 'load_all_components']);
    }
    
    /**
     * 注册组件路径
     */
    private function register_component_paths() {
        $this->component_registry = [
            'layout' => get_template_directory() . '/components/layout/',
            'content' => get_template_directory() . '/components/content/',
            'forms' => get_template_directory() . '/components/forms/',
            'navigation' => get_template_directory() . '/components/navigation/',
            'overlays' => get_template_directory() . '/components/overlays/',
            'utilities' => get_template_directory() . '/components/utilities/',
        ];
    }
    
    /**
     * 加载所有组件
     */
    public function load_all_components() {
        foreach ($this->component_registry as $category => $path) {
            $this->load_components_from_directory($path, $category);
        }
    }
    
    /**
     * 从目录加载组件
     */
    private function load_components_from_directory($directory, $category) {
        if (!is_dir($directory)) {
            return;
        }
        
        $files = glob($directory . '*.php');
        foreach ($files as $file) {
            $component_name = basename($file, '.php');
            $this->load_component($component_name, $file, $category);
        }
    }
    
    /**
     * 加载单个组件
     */
    public function load_component($name, $file_path, $category = '') {
        if (isset($this->loaded_components[$name])) {
            return; // 已加载，避免重复
        }
        
        if (file_exists($file_path)) {
            require_once $file_path;
            $this->loaded_components[$name] = [
                'path' => $file_path,
                'category' => $category,
                'loaded_at' => current_time('timestamp')
            ];
            
            do_action('yinghe_component_loaded', $name, $file_path, $category);
        }
    }
    
    /**
     * 获取已加载的组件列表
     */
    public function get_loaded_components() {
        return $this->loaded_components;
    }
    
    /**
     * 检查组件是否已加载
     */
    public function is_component_loaded($name) {
        return isset($this->loaded_components[$name]);
    }
}

// 初始化组件加载器
function yinghe_init_component_loader() {
    return YingheComponentLoader::get_instance();
}
add_action('init', 'yinghe_init_component_loader');
```

### 1.2 资源管理系统

#### 资源管理器类
```php
<?php
/**
 * 文件: includes/class-asset-manager.php
 * 资源文件管理和优化
 */

class YingheAssetManager {
    private $enqueued_components = [];
    private $inline_styles = [];
    private $inline_scripts = [];
    
    public function __construct() {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_theme_assets'], 10);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_component_assets'], 20);
        add_action('wp_footer', [$this, 'output_inline_assets'], 30);
    }
    
    /**
     * 注册主题核心资源
     */
    public function enqueue_theme_assets() {
        // 核心样式
        wp_enqueue_style(
            'yinghe-core',
            get_template_directory_uri() . '/assets/dist/css/main.min.css',
            [],
            $this->get_asset_version('css/main.min.css')
        );
        
        // 核心脚本
        wp_enqueue_script(
            'yinghe-core',
            get_template_directory_uri() . '/assets/dist/js/main.min.js',
            ['jquery'],
            $this->get_asset_version('js/main.min.js'),
            true
        );
        
        // 本地化脚本数据
        wp_localize_script('yinghe-core', 'yingheConfig', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yinghe_ajax_nonce'),
            'apiUrl' => rest_url('yinghe/v1/'),
            'currentUser' => get_current_user_id(),
            'translations' => $this->get_js_translations(),
            'settings' => $this->get_theme_settings()
        ]);
        
        // 条件加载第三方库
        if ($this->needs_clipboard_js()) {
            wp_enqueue_script('clipboard-js', get_template_directory_uri() . '/assets/vendor/clipboard.min.js', [], '2.0.8', true);
        }
        
        if ($this->needs_lazyload()) {
            wp_enqueue_script('lazyload', get_template_directory_uri() . '/assets/vendor/lazyload.min.js', [], '17.8.3', true);
        }
    }
    
    /**
     * 注册组件特定资源
     */
    public function register_component_assets($component_name, $assets) {
        if (!isset($this->enqueued_components[$component_name])) {
            $this->enqueued_components[$component_name] = $assets;
            
            // 注册CSS
            if (!empty($assets['styles'])) {
                foreach ($assets['styles'] as $handle => $style) {
                    wp_enqueue_style($handle, $style['src'], $style['deps'], $style['version']);
                }
            }
            
            // 注册JS
            if (!empty($assets['scripts'])) {
                foreach ($assets['scripts'] as $handle => $script) {
                    wp_enqueue_script($handle, $script['src'], $script['deps'], $script['version'], $script['in_footer']);
                }
            }
        }
    }
    
    /**
     * 添加内联样式
     */
    public function add_inline_style($component_name, $css) {
        $this->inline_styles[$component_name] = $css;
    }
    
    /**
     * 添加内联脚本
     */
    public function add_inline_script($component_name, $js) {
        $this->inline_scripts[$component_name] = $js;
    }
    
    /**
     * 输出内联资源
     */
    public function output_inline_assets() {
        // 输出内联样式
        if (!empty($this->inline_styles)) {
            echo '<style id="yinghe-component-styles">';
            foreach ($this->inline_styles as $component => $css) {
                echo "/* Component: {$component} */\n";
                echo $css . "\n";
            }
            echo '</style>';
        }
        
        // 输出内联脚本
        if (!empty($this->inline_scripts)) {
            echo '<script id="yinghe-component-scripts">';
            echo '(function($) {';
            foreach ($this->inline_scripts as $component => $js) {
                echo "/* Component: {$component} */\n";
                echo $js . "\n";
            }
            echo '})(jQuery);';
            echo '</script>';
        }
    }
    
    /**
     * 获取资源文件版本号
     */
    private function get_asset_version($asset_path) {
        $file_path = get_template_directory() . '/assets/dist/' . $asset_path;
        return file_exists($file_path) ? filemtime($file_path) : '1.0.0';
    }
    
    /**
     * 获取JavaScript翻译
     */
    private function get_js_translations() {
        return [
            'loading' => __('加载中...', 'yinghe'),
            'error' => __('发生错误', 'yinghe'),
            'success' => __('操作成功', 'yinghe'),
            'confirm' => __('确认操作', 'yinghe'),
            'cancel' => __('取消', 'yinghe'),
            'close' => __('关闭', 'yinghe'),
            'search_placeholder' => __('输入关键字搜索', 'yinghe'),
            'no_results' => __('没有找到相关结果', 'yinghe'),
        ];
    }
    
    /**
     * 获取主题设置
     */
    private function get_theme_settings() {
        return [
            'theme_mode' => get_theme_mod('default_theme_mode', 'light'),
            'animation_enabled' => get_theme_mod('enable_animations', true),
            'lazy_load_enabled' => get_theme_mod('enable_lazy_loading', true),
            'auto_theme_switch' => get_theme_mod('auto_theme_switch', true),
        ];
    }
    
    /**
     * 检查是否需要Clipboard.js
     */
    private function needs_clipboard_js() {
        // 检查页面是否包含需要复制功能的组件
        return true; // 简化实现，实际可以更精确检测
    }
    
    /**
     * 检查是否需要LazyLoad
     */
    private function needs_lazyload() {
        return get_theme_mod('enable_lazy_loading', true);
    }
}

// 初始化资源管理器
function yinghe_init_asset_manager() {
    return new YingheAssetManager();
}
add_action('init', 'yinghe_init_asset_manager');
```

### 1.3 基础组件抽象类

#### 组件基类
```php
<?php
/**
 * 文件: includes/abstract-class-component.php
 * 组件抽象基类，定义组件接口和公共功能
 */

abstract class YingheAbstractComponent {
    protected $props;
    protected $component_name;
    protected $unique_id;
    protected static $instance_count = [];
    
    /**
     * 构造函数
     */
    public function __construct($props = []) {
        $this->component_name = $this->get_component_name();
        $this->unique_id = $this->generate_unique_id();
        $this->props = $this->parse_props($props);
        
        $this->init();
        $this->register_assets();
    }
    
    /**
     * 获取组件名称
     */
    protected function get_component_name() {
        $class_name = get_class($this);
        return strtolower(str_replace(['Yinghe', 'Component'], '', $class_name));
    }
    
    /**
     * 生成唯一ID
     */
    protected function generate_unique_id() {
        if (!isset(self::$instance_count[$this->component_name])) {
            self::$instance_count[$this->component_name] = 0;
        }
        self::$instance_count[$this->component_name]++;
        
        return $this->component_name . '-' . self::$instance_count[$this->component_name];
    }
    
    /**
     * 解析组件属性
     */
    protected function parse_props($props) {
        return wp_parse_args($props, $this->get_default_props());
    }
    
    /**
     * 组件初始化（子类可重写）
     */
    protected function init() {
        // 默认为空，子类按需实现
    }
    
    /**
     * 注册组件资源
     */
    protected function register_assets() {
        $asset_manager = yinghe_get_asset_manager();
        
        if ($styles = $this->get_component_styles()) {
            $asset_manager->add_inline_style($this->component_name, $styles);
        }
        
        if ($scripts = $this->get_component_scripts()) {
            $asset_manager->add_inline_script($this->component_name, $scripts);
        }
    }
    
    /**
     * 渲染组件
     */
    public function render() {
        ob_start();
        
        echo $this->before_render();
        $this->render_component();
        echo $this->after_render();
        
        return ob_get_clean();
    }
    
    /**
     * 静态渲染方法
     */
    public static function render_static($props = []) {
        $instance = new static($props);
        return $instance->render();
    }
    
    /**
     * 渲染前钩子
     */
    protected function before_render() {
        return apply_filters("yinghe_before_render_{$this->component_name}", '', $this->props);
    }
    
    /**
     * 渲染后钩子
     */
    protected function after_render() {
        return apply_filters("yinghe_after_render_{$this->component_name}", '', $this->props);
    }
    
    /**
     * 获取组件CSS类名
     */
    protected function get_css_classes() {
        $classes = [
            'yinghe-component',
            "yinghe-{$this->component_name}",
        ];
        
        if (!empty($this->props['class'])) {
            $classes[] = $this->props['class'];
        }
        
        if (!empty($this->props['modifier'])) {
            $classes[] = "yinghe-{$this->component_name}--{$this->props['modifier']}";
        }
        
        return implode(' ', array_filter($classes));
    }
    
    /**
     * 获取组件属性字符串
     */
    protected function get_attributes() {
        $attributes = [
            'id' => $this->unique_id,
            'class' => $this->get_css_classes(),
        ];
        
        if (!empty($this->props['data'])) {
            foreach ($this->props['data'] as $key => $value) {
                $attributes["data-{$key}"] = esc_attr($value);
            }
        }
        
        $attr_string = '';
        foreach ($attributes as $name => $value) {
            $attr_string .= " {$name}=\"" . esc_attr($value) . '"';
        }
        
        return $attr_string;
    }
    
    // 抽象方法，子类必须实现
    abstract protected function get_default_props();
    abstract protected function render_component();
    
    // 可选方法，子类按需重写
    protected function get_component_styles() {
        return '';
    }
    
    protected function get_component_scripts() {
        return '';
    }
}

/**
 * 组件接口定义
 */
interface YingheComponentInterface {
    public function render();
    public static function render_static($props = []);
}

/**
 * 可缓存组件特性
 */
trait YingheCacheableComponent {
    protected $cache_duration = HOUR_IN_SECONDS;
    protected $cache_group = 'yinghe_components';
    
    /**
     * 获取缓存的渲染结果
     */
    public function get_cached_render() {
        $cache_key = $this->get_cache_key();
        $cached_content = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $cached_content) {
            $cached_content = $this->render();
            wp_cache_set($cache_key, $cached_content, $this->cache_group, $this->cache_duration);
        }
        
        return $cached_content;
    }
    
    /**
     * 生成缓存键
     */
    protected function get_cache_key() {
        return md5($this->component_name . serialize($this->props));
    }
    
    /**
     * 清除组件缓存
     */
    public function clear_cache() {
        $cache_key = $this->get_cache_key();
        wp_cache_delete($cache_key, $this->cache_group);
    }
}
```

---

## 🏗️ Phase 2: 布局组件开发

### 2.1 侧边栏导航组件

#### SidebarNav 组件实现
```php
<?php
/**
 * 文件: components/layout/sidebar-nav.php
 * 侧边栏导航组件
 */

class YingheSidebarNavComponent extends YingheAbstractComponent {
    
    protected function get_default_props() {
        return [
            'menu_location' => 'primary',
            'logo_config' => [
                'expanded' => get_theme_mod('logo_expanded', ''),
                'collapsed' => get_theme_mod('logo_collapsed', ''),
                'light' => get_theme_mod('logo_light', ''),
                'dark' => get_theme_mod('logo_dark', ''),
            ],
            'show_domain_info' => get_theme_mod('show_domain_info', true),
            'domain_links' => get_option('yinghe_domain_links', []),
            'sticky' => true,
            'collapsible' => true,
        ];
    }
    
    protected function render_component() {
        ?>
        <div<?php echo $this->get_attributes(); ?>>
            <div class="sidebar-nav-inner">
                <?php $this->render_logo(); ?>
                <?php $this->render_menu(); ?>
                <?php $this->render_footer(); ?>
                <?php if ($this->props['show_domain_info']): ?>
                    <?php $this->render_domain_info(); ?>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染Logo区域
     */
    private function render_logo() {
        $logo_config = $this->props['logo_config'];
        ?>
        <div class="sidebar-logo">
            <div class="logo overflow-hidden">
                <h1 class="text-hide position-absolute"><?php bloginfo('name'); ?></h1>
                
                <a href="<?php echo esc_url(home_url('/')); ?>" class="logo-expanded">
                    <img src="<?php echo esc_url($logo_config['light']); ?>" 
                         class="logo-light" alt="<?php bloginfo('name'); ?>">
                    <img src="<?php echo esc_url($logo_config['dark']); ?>" 
                         class="logo-dark d-none" alt="<?php bloginfo('name'); ?>">
                </a>
                
                <a href="<?php echo esc_url(home_url('/')); ?>" class="logo-collapsed">
                    <img src="<?php echo esc_url($logo_config['collapsed']); ?>" 
                         class="logo-light" alt="<?php bloginfo('name'); ?>">
                    <img src="<?php echo esc_url($logo_config['dark']); ?>" 
                         class="logo-dark d-none" alt="<?php bloginfo('name'); ?>">
                </a>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染导航菜单
     */
    private function render_menu() {
        ?>
        <div class="sidebar-menu flex-fill">
            <div class="sidebar-scroll">
                <div class="sidebar-menu-inner">
                    <?php $this->render_category_menu(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染分类菜单
     */
    private function render_category_menu() {
        $categories = get_terms([
            'taxonomy' => 'site_category',
            'hide_empty' => false,
            'parent' => 0,
            'orderby' => 'menu_order',
            'order' => 'ASC'
        ]);
        
        if (!empty($categories)) {
            echo '<ul>';
            
            foreach ($categories as $category) {
                $this->render_menu_item($category);
            }
            
            echo '</ul>';
        }
    }
    
    /**
     * 渲染单个菜单项
     */
    private function render_menu_item($category) {
        $icon_class = get_term_meta($category->term_id, 'category_icon', true) ?: 'io-folder';
        $subcategories = get_terms([
            'taxonomy' => 'site_category',
            'hide_empty' => false,
            'parent' => $category->term_id,
            'orderby' => 'menu_order',
            'order' => 'ASC'
        ]);
        
        $has_children = !empty($subcategories);
        $item_class = 'sidebar-item';
        if ($category->slug === 'cooperation') {
            $item_class .= ' term-178';
        }
        ?>
        <li class="<?php echo esc_attr($item_class); ?>">
            <a href="#term-<?php echo esc_attr($category->term_id); ?>" 
               class="smooth" 
               data-change="#term-<?php echo esc_attr($category->term_id); ?>">
                <i class="io <?php echo esc_attr($icon_class); ?> icon-fw icon-lg"></i>
                <span><?php echo esc_html($category->name); ?></span>
            </a>
            
            <?php if ($has_children): ?>
                <i class="iconfont icon-arrow-r-m sidebar-more text-sm"></i>
                <ul>
                    <?php foreach ($subcategories as $subcategory): ?>
                        <li>
                            <a href="#term-<?php echo esc_attr($category->term_id); ?>-<?php echo esc_attr($subcategory->term_id); ?>" 
                               class="smooth">
                                <span><?php echo esc_html($subcategory->name); ?></span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </li>
        <?php
    }
    
    /**
     * 渲染底部链接
     */
    private function render_footer() {
        ?>
        <div class="border-top py-2 border-color">
            <div class="sidebar-nav-footer">
                <ul>
                    <li class="call-yinghe">
                        <a href="/about-yinghe/">
                            <span><?php _e('呼叫硬核君', 'yinghe'); ?></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染域名信息
     */
    private function render_domain_info() {
        $domain_links = $this->props['domain_links'];
        ?>
        <div class="domain">
            <div class="domain-header">
                <span><?php _e('回家地址', 'yinghe'); ?></span>
            </div>
            <?php foreach ($domain_links as $domain): ?>
                <a href="<?php echo esc_url($domain['url']); ?>" 
                   title="<?php echo esc_attr($domain['title']); ?>" 
                   class="yinghe-domain" 
                   target="_blank" 
                   rel="noopener">
                    <?php echo esc_html($domain['text']); ?>
                </a>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    protected function get_component_styles() {
        return "
            .yinghe-sidebar-nav {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                width: 220px;
                background: #fff;
                z-index: 1000;
                transition: transform 0.3s ease;
            }
            
            .yinghe-sidebar-nav.collapsed {
                width: 110px;
            }
            
            .sidebar-nav-inner {
                height: 100%;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            
            .sidebar-logo {
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid #f1f2f3;
            }
            
            .sidebar-menu {
                flex: 1;
                overflow-y: auto;
                scrollbar-width: thin;
            }
            
            .sidebar-menu::-webkit-scrollbar {
                width: 4px;
            }
            
            .sidebar-menu::-webkit-scrollbar-thumb {
                background: #d1d5db;
                border-radius: 2px;
            }
            
            .sidebar-item > a {
                display: flex;
                align-items: center;
                padding: 12px 20px;
                color: #374151;
                text-decoration: none;
                transition: all 0.2s ease;
            }
            
            .sidebar-item > a:hover,
            .sidebar-item.active > a {
                background: #f3f4f6;
                color: #283593;
            }
            
            .sidebar-item i {
                margin-right: 12px;
                width: 20px;
                text-align: center;
            }
            
            .sidebar-item ul {
                display: none;
                background: #f9fafb;
                border-left: 2px solid #e5e7eb;
                margin-left: 20px;
            }
            
            .sidebar-item.expanded ul {
                display: block;
            }
            
            .sidebar-item ul a {
                padding: 8px 20px;
                font-size: 14px;
            }
            
            @media (max-width: 768px) {
                .yinghe-sidebar-nav {
                    transform: translateX(-100%);
                    transition: transform 0.3s ease;
                }
                
                .yinghe-sidebar-nav.open {
                    transform: translateX(0);
                }
            }
        ";
    }
    
    protected function get_component_scripts() {
        return "
            // 侧边栏导航交互
            (function() {
                const sidebar = document.getElementById('{$this->unique_id}');
                if (!sidebar) return;
                
                // 平滑滚动到目标区域
                sidebar.addEventListener('click', function(e) {
                    if (e.target.classList.contains('smooth')) {
                        e.preventDefault();
                        
                        const targetId = e.target.getAttribute('href').substring(1);
                        const targetElement = document.getElementById(targetId);
                        
                        if (targetElement) {
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                            
                            // 更新激活状态
                            sidebar.querySelectorAll('.sidebar-item').forEach(item => {
                                item.classList.remove('active');
                            });
                            e.target.closest('.sidebar-item').classList.add('active');
                        }
                    }
                });
                
                // 子菜单展开/收起
                sidebar.addEventListener('click', function(e) {
                    if (e.target.classList.contains('sidebar-more')) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const parentItem = e.target.closest('.sidebar-item');
                        parentItem.classList.toggle('expanded');
                    }
                });
                
                // 移动端侧边栏切换
                if (window.innerWidth <= 768) {
                    const toggleButton = document.getElementById('sidebar-switch');
                    if (toggleButton) {
                        toggleButton.addEventListener('click', function() {
                            sidebar.classList.toggle('open');
                            document.body.classList.toggle('sidebar-open');
                        });
                    }
                    
                    // 点击遮罩关闭侧边栏
                    document.addEventListener('click', function(e) {
                        if (!sidebar.contains(e.target) && !e.target.closest('#sidebar-switch')) {
                            sidebar.classList.remove('open');
                            document.body.classList.remove('sidebar-open');
                        }
                    });
                }
            })();
        ";
    }
}

// 注册helper函数
function yinghe_render_sidebar_nav($props = []) {
    echo YingheSidebarNavComponent::render_static($props);
}

function yinghe_get_sidebar_nav($props = []) {
    return YingheSidebarNavComponent::render_static($props);
}
```

### 2.2 头部系统组件

#### HeaderSystem 组件实现
```php
<?php
/**
 * 文件: components/layout/header-system.php
 * 头部系统组件（包含导航栏和搜索区域）
 */

class YingheHeaderSystemComponent extends YingheAbstractComponent {
    
    protected function get_default_props() {
        return [
            'show_mini_header' => true,
            'show_big_header' => true,
            'search_config' => [
                'enabled' => true,
                'engines' => $this->get_search_engines(),
                'placeholder' => __('输入关键字搜索', 'yinghe'),
            ],
            'announcement_config' => [
                'enabled' => get_theme_mod('show_announcements', true),
                'content' => get_option('yinghe_announcement_content', ''),
            ],
            'menu_items' => [
                'wechat' => [
                    'icon' => 'io-wechat-o',
                    'label' => __('关注硬核', 'yinghe'),
                    'image' => get_theme_mod('wechat_qr_image', ''),
                ],
                'history' => [
                    'icon' => 'io-clock1',
                    'label' => __('浏览记录', 'yinghe'),
                ],
                'filter' => [
                    'icon' => 'io-more',
                    'label' => __('快速筛选网站', 'yinghe'),
                ],
            ],
        ];
    }
    
    protected function render_component() {
        ?>
        <div<?php echo $this->get_attributes(); ?>>
            <?php if ($this->props['show_mini_header']): ?>
                <?php $this->render_mini_header(); ?>
            <?php endif; ?>
            
            <?php if ($this->props['show_big_header']): ?>
                <?php $this->render_big_header(); ?>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染迷你头部
     */
    private function render_mini_header() {
        ?>
        <div class="mini-header">
            <div class="page-header sticky">
                <div class="navbar navbar-expand-md">
                    <div class="container-fluid p-0 position-relative">
                        <?php $this->render_mobile_logo(); ?>
                        <?php $this->render_header_menu(); ?>
                        <?php $this->render_mobile_menu_toggle(); ?>
                        <?php $this->render_filter_toggle(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染大头部
     */
    private function render_big_header() {
        $search_config = $this->props['search_config'];
        $announcement_config = $this->props['announcement_config'];
        ?>
        <div class="big-header unchanged no-bg">
            <?php if ($search_config['enabled']): ?>
                <?php $this->render_search_section(); ?>
            <?php endif; ?>
            
            <?php if ($announcement_config['enabled'] && !empty($announcement_config['content'])): ?>
                <?php $this->render_announcement(); ?>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染移动端Logo
     */
    private function render_mobile_logo() {
        $logo_light = get_theme_mod('logo_light', '');
        $logo_dark = get_theme_mod('logo_dark', '');
        ?>
        <div class="position-absolute w-100 text-center">
            <a href="<?php echo esc_url(home_url('/')); ?>" 
               class="navbar-brand d-md-none m-0" 
               title="<?php bloginfo('name'); ?>">
                <img src="<?php echo esc_url($logo_light); ?>" 
                     class="logo-light" alt="<?php bloginfo('name'); ?>">
                <img src="<?php echo esc_url($logo_dark); ?>" 
                     class="logo-dark d-none" alt="<?php bloginfo('name'); ?>">
            </a>
        </div>
        <?php
    }
    
    /**
     * 渲染头部菜单
     */
    private function render_header_menu() {
        $menu_items = $this->props['menu_items'];
        ?>
        <div class="header-menu">
            <?php foreach ($menu_items as $key => $item): ?>
                <div class="header-menu-item toggle-item <?php echo esc_attr($this->get_toggle_classes($key)); ?>">
                    <div class="header-menu-toggle">
                        <i class="io <?php echo esc_attr($item['icon']); ?>"></i>
                        <span class="header-menu-name"><?php echo esc_html($item['label']); ?></span>
                    </div>
                    <?php $this->render_menu_dropdown($key, $item); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染菜单下拉内容
     */
    private function render_menu_dropdown($key, $item) {
        ?>
        <div class="toggle">
            <div class="toggle-inner">
                <div class="toggle-inner-item">
                    <?php
                    switch ($key) {
                        case 'wechat':
                            $this->render_wechat_content($item);
                            break;
                        case 'history':
                            $this->render_history_content();
                            break;
                        case 'filter':
                            $this->render_filter_content();
                            break;
                    }
                    ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染微信二维码内容
     */
    private function render_wechat_content($item) {
        if (!empty($item['image'])) {
            ?>
            <img src="<?php echo esc_url($item['image']); ?>" 
                 width="100%" height="100%" 
                 alt="<?php echo esc_attr($item['label']); ?>">
            <?php
        }
    }
    
    /**
     * 渲染浏览记录内容
     */
    private function render_history_content() {
        ?>
        <div class="toggle-inner-item-title"><?php _e('浏览记录', 'yinghe'); ?></div>
        <div class="toggle-prompt"><?php _e('还没有浏览记录', 'yinghe'); ?></div>
        <ul class="toggle-visited" id="browsing-history">
            <!-- 浏览记录将通过JavaScript动态加载 -->
        </ul>
        <?php
    }
    
    /**
     * 渲染搜索区域
     */
    private function render_search_section() {
        $search_engines = $this->props['search_config']['engines'];
        ?>
        <div class="s-search">
            <div id="search" class="s-search mx-auto">
                <?php $this->render_search_tabs($search_engines); ?>
                <?php $this->render_search_form(); ?>
                <?php $this->render_search_engines($search_engines); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染搜索标签页
     */
    private function render_search_tabs($engines) {
        $groups = $this->group_search_engines($engines);
        ?>
        <div id="search-list-menu">
            <div class="s-type text-center">
                <div class="s-type-list big tab-auto-scrollbar overflow-x-auto">
                    <div class="anchor" style="position: absolute; left: 50%; opacity: 0;"></div>
                    <?php foreach ($groups as $group_key => $group): ?>
                        <label for="type-<?php echo esc_attr($group_key); ?>" 
                               class="<?php echo $group_key === 'yingshi' ? 'active' : ''; ?>" 
                               data-page="home" 
                               data-id="group-<?php echo esc_attr($group_key); ?>">
                            <span><?php echo esc_html($group['label']); ?></span>
                        </label>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 获取搜索引擎配置
     */
    private function get_search_engines() {
        global $wpdb;
        
        $engines = $wpdb->get_results("
            SELECT * FROM {$wpdb->prefix}yinghe_search_engines 
            WHERE is_active = 1 
            ORDER BY category, sort_order ASC
        ");
        
        return $engines ?: $this->get_default_search_engines();
    }
    
    /**
     * 获取默认搜索引擎
     */
    private function get_default_search_engines() {
        return [
            (object) [
                'id' => 1,
                'name' => 'qiku',
                'display_name' => '奇库影视',
                'search_url' => 'https://qkys1.cc/vodsearch/%s%-------------.html',
                'placeholder' => '输入你想看的影片名',
                'category' => 'yingshi',
                'is_default' => 1
            ],
            (object) [
                'id' => 2,
                'name' => 'douban',
                'display_name' => '豆瓣',
                'search_url' => 'https://search.douban.com/movie/subject_search?search_text=%s%',
                'placeholder' => '输入影片、演员、导演名称',
                'category' => 'ziliao',
                'is_default' => 0
            ]
        ];
    }
    
    protected function get_component_styles() {
        return "
            .yinghe-header-system {
                position: relative;
                z-index: 999;
            }
            
            .mini-header {
                background: #fff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .big-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 60px 0;
                text-align: center;
            }
            
            .header-menu {
                position: absolute;
                right: 15px;
                top: 20px;
                display: flex;
                gap: 10px;
            }
            
            .header-menu-toggle {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                background: #fff;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .header-menu-toggle:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            
            .header-menu-toggle i {
                margin-right: 6px;
                color: #283593;
            }
            
            .s-search {
                max-width: 600px;
                margin: 0 auto;
            }
            
            #search-text {
                width: 100%;
                padding: 15px 20px;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            
            #search-text:focus {
                outline: none;
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }
            
            .search-type {
                display: flex;
                justify-content: center;
                margin: 20px 0;
                gap: 10px;
            }
            
            .search-type label {
                padding: 8px 16px;
                background: rgba(255,255,255,0.2);
                border-radius: 20px;
                color: #fff;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .search-type label.active,
            .search-type label:hover {
                background: rgba(255,255,255,0.3);
                transform: translateY(-1px);
            }
            
            @media (max-width: 768px) {
                .header-menu {
                    position: fixed;
                    right: 18px;
                    top: 10px;
                }
                
                .header-menu-name {
                    display: none;
                }
                
                .big-header {
                    padding: 40px 15px;
                }
                
                .s-search {
                    max-width: 95%;
                }
            }
        ";
    }
    
    protected function get_component_scripts() {
        return "
            // 头部系统交互逻辑
            (function() {
                const header = document.getElementById('{$this->unique_id}');
                if (!header) return;
                
                // 搜索引擎切换
                const searchTabs = header.querySelectorAll('.s-type-list label');
                const searchGroups = header.querySelectorAll('.search-group');
                
                searchTabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        const targetGroup = this.dataset.id;
                        
                        // 更新标签状态
                        searchTabs.forEach(t => t.classList.remove('active'));
                        this.classList.add('active');
                        
                        // 显示对应搜索组
                        searchGroups.forEach(group => {
                            group.classList.remove('s-current');
                            if (group.classList.contains(targetGroup)) {
                                group.classList.add('s-current');
                            }
                        });
                        
                        // 更新搜索框占位符
                        const firstEngine = group.querySelector('input[type=\"radio\"]');
                        if (firstEngine) {
                            const placeholder = firstEngine.dataset.placeholder;
                            const searchInput = header.querySelector('#search-text');
                            if (searchInput && placeholder) {
                                searchInput.setAttribute('placeholder', placeholder);
                            }
                        }
                    });
                });
                
                // 搜索引擎选择
                const searchEngines = header.querySelectorAll('input[name=\"type\"]');
                searchEngines.forEach(engine => {
                    engine.addEventListener('change', function() {
                        const placeholder = this.dataset.placeholder;
                        const searchInput = header.querySelector('#search-text');
                        if (searchInput && placeholder) {
                            searchInput.setAttribute('placeholder', placeholder);
                        }
                    });
                });
                
                // 搜索表单提交
                const searchForm = header.querySelector('.super-search-fm');
                if (searchForm) {
                    searchForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        const searchInput = this.querySelector('#search-text');
                        const selectedEngine = this.querySelector('input[name=\"type\"]:checked');
                        
                        if (searchInput && selectedEngine && searchInput.value.trim()) {
                            const searchUrl = selectedEngine.value.replace('%s%', encodeURIComponent(searchInput.value.trim()));
                            window.open(searchUrl, '_blank', 'noopener');
                        }
                    });
                }
                
                // 下拉菜单切换
                const toggleItems = header.querySelectorAll('.toggle-item');
                toggleItems.forEach(item => {
                    const toggle = item.querySelector('.header-menu-toggle');
                    const dropdown = item.querySelector('.toggle');
                    
                    if (toggle && dropdown) {
                        toggle.addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            // 关闭其他下拉菜单
                            toggleItems.forEach(otherItem => {
                                if (otherItem !== item) {
                                    otherItem.classList.remove('show');
                                }
                            });
                            
                            // 切换当前下拉菜单
                            item.classList.toggle('show');
                        });
                    }
                });
                
                // 点击外部关闭下拉菜单
                document.addEventListener('click', function() {
                    toggleItems.forEach(item => {
                        item.classList.remove('show');
                    });
                });
                
                // 浏览记录管理
                this.loadBrowsingHistory();
                
            })();
            
            // 浏览记录功能
            window.yingheBrowsingHistory = {
                maxItems: 20,
                storageKey: 'yinghe_browsing_history',
                
                addItem: function(item) {
                    let history = this.getHistory();
                    
                    // 移除重复项
                    history = history.filter(h => h.url !== item.url);
                    
                    // 添加到开头
                    history.unshift({
                        ...item,
                        timestamp: Date.now()
                    });
                    
                    // 限制数量
                    if (history.length > this.maxItems) {
                        history = history.slice(0, this.maxItems);
                    }
                    
                    localStorage.setItem(this.storageKey, JSON.stringify(history));
                    this.updateDisplay();
                },
                
                getHistory: function() {
                    try {
                        return JSON.parse(localStorage.getItem(this.storageKey) || '[]');
                    } catch (e) {
                        return [];
                    }
                },
                
                updateDisplay: function() {
                    const container = document.getElementById('browsing-history');
                    if (!container) return;
                    
                    const history = this.getHistory();
                    const prompt = container.previousElementSibling;
                    
                    if (history.length === 0) {
                        container.innerHTML = '';
                        if (prompt) prompt.style.display = 'block';
                        return;
                    }
                    
                    if (prompt) prompt.style.display = 'none';
                    
                    container.innerHTML = history.map(item => 
                        '<li><a href=\"' + this.escapeHtml(item.url) + '\" target=\"_blank\">' + 
                        this.escapeHtml(item.title) + '</a></li>'
                    ).join('');
                },
                
                escapeHtml: function(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                }
            };
        ";
    }
    
    /**
     * 获取切换类名
     */
    private function get_toggle_classes($key) {
        $classes = ['toggle-dropdown', 'top'];
        
        switch ($key) {
            case 'wechat':
                $classes[] = 'wechat';
                break;
            case 'history':
                $classes[] = 'visited';
                break;
            case 'filter':
                $classes = ['toggle-sidebar', 'left', 'filter'];
                break;
        }
        
        return implode(' ', $classes);
    }
    
    /**
     * 分组搜索引擎
     */
    private function group_search_engines($engines) {
        $groups = [
            'yingshi' => ['label' => __('影视搜索', 'yinghe'), 'engines' => []],
            'ziliao' => ['label' => __('影片资料', 'yinghe'), 'engines' => []],
        ];
        
        foreach ($engines as $engine) {
            if (isset($groups[$engine->category])) {
                $groups[$engine->category]['engines'][] = $engine;
            }
        }
        
        return $groups;
    }
}

// 注册helper函数
function yinghe_render_header_system($props = []) {
    echo YingheHeaderSystemComponent::render_static($props);
}

function yinghe_get_header_system($props = []) {
    return YingheHeaderSystemComponent::render_static($props);
}
```

---

## 🧩 Phase 3: 内容组件开发

继续开发网站卡片、分类头部等核心内容组件...

[由于篇幅限制，这里提供了核心的基础架构和前两个主要布局组件的完整实现。剩余的组件开发将遵循相同的模式和架构原则。]

---

## ✅ 开发检查清单

### Phase 1 完成标准
- [ ] 组件加载系统正常工作
- [ ] 资源管理系统有效运行
- [ ] 基础组件抽象类功能完整
- [ ] 安全验证机制到位

### Phase 2 完成标准
- [ ] 侧边栏导航组件完全实现
- [ ] 头部系统组件功能齐全
- [ ] 响应式设计正常工作
- [ ] 移动端适配完成

### 质量保证
- [ ] 所有组件遵循WordPress编码标准
- [ ] 输入验证和输出转义完整
- [ ] 国际化支持实现
- [ ] 性能优化措施到位
- [ ] 浏览器兼容性测试通过

这个实施计划提供了完整的组件化开发框架，确保WordPress主题的现代化、安全性和可维护性。