<?php
/**
 * 主题切换器组件
 * 
 * @package YingheTheme
 * @subpackage Components\Utilities
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 主题切换器组件类
 */
class YingheThemeSwitcherComponent extends YingheAbstractComponent {
    
    /**
     * 获取默认属性
     * 
     * @return array
     */
    protected function get_default_props() {
        return [
            'position' => 'fixed', // fixed, inline, relative
            'show_text' => true,
            'size' => 'default', // small, default, large
            'style' => 'button', // button, toggle, icon
            'save_preference' => true,
            'custom_class' => '',
            'show_tooltip' => true,
        ];
    }
    
    /**
     * 初始化组件
     */
    protected function init() {
        // 获取当前主题模式
        $this->current_theme = $this->get_current_theme_mode();
        
        // 添加组件特定的数据属性
        $this->component_data = [
            'current_theme' => $this->current_theme,
            'save_preference' => $this->props['save_preference'],
        ];
    }
    
    /**
     * 渲染组件
     */
    protected function render_component() {
        $position_class = $this->props['position'] === 'fixed' ? 'theme-switcher-fixed' : '';
        $size_class = 'theme-switcher-' . $this->props['size'];
        $style_class = 'theme-switcher-' . $this->props['style'];
        $custom_class = $this->props['custom_class'];
        
        ?>
        <div class="yinghe-theme-switcher <?php echo esc_attr("$position_class $size_class $style_class $custom_class"); ?>" 
             data-component="theme-switcher"
             data-current-theme="<?php echo esc_attr($this->current_theme); ?>"
             data-save-preference="<?php echo esc_attr($this->props['save_preference'] ? 'true' : 'false'); ?>">
            
            <?php $this->render_switcher_content(); ?>
            
        </div>
        <?php
    }
    
    /**
     * 渲染切换器内容
     */
    private function render_switcher_content() {
        switch ($this->props['style']) {
            case 'toggle':
                $this->render_toggle_style();
                break;
            case 'icon':
                $this->render_icon_style();
                break;
            case 'button':
            default:
                $this->render_button_style();
                break;
        }
    }
    
    /**
     * 渲染按钮样式
     */
    private function render_button_style() {
        $tooltip_attrs = '';
        if ($this->props['show_tooltip']) {
            $tooltip_text = $this->current_theme === 'dark' ? '切换到浅色模式' : '切换到深色模式';
            $tooltip_attrs = 'data-toggle="tooltip" data-placement="left" title="' . esc_attr($tooltip_text) . '"';
        }
        
        ?>
        <button class="theme-switch-btn" 
                onclick="toggleTheme()" 
                <?php echo $tooltip_attrs; ?>
                aria-label="<?php echo $this->current_theme === 'dark' ? '切换到浅色模式' : '切换到深色模式'; ?>">
            
            <span class="theme-icon theme-icon-light" style="<?php echo $this->current_theme === 'dark' ? 'display: none;' : ''; ?>">
                <i class="io io-sunny"></i>
            </span>
            
            <span class="theme-icon theme-icon-dark" style="<?php echo $this->current_theme === 'light' ? 'display: none;' : ''; ?>">
                <i class="io io-moon"></i>
            </span>
            
            <?php if ($this->props['show_text']): ?>
                <span class="theme-text theme-text-light" style="<?php echo $this->current_theme === 'dark' ? 'display: none;' : ''; ?>">
                    浅色
                </span>
                <span class="theme-text theme-text-dark" style="<?php echo $this->current_theme === 'light' ? 'display: none;' : ''; ?>">
                    深色
                </span>
            <?php endif; ?>
            
        </button>
        <?php
    }
    
    /**
     * 渲染切换开关样式
     */
    private function render_toggle_style() {
        ?>
        <div class="theme-toggle-wrapper">
            <?php if ($this->props['show_text']): ?>
                <span class="toggle-label">
                    <i class="io io-sunny toggle-icon-light"></i>
                </span>
            <?php endif; ?>
            
            <label class="theme-toggle-switch">
                <input type="checkbox" 
                       class="theme-toggle-input" 
                       <?php checked($this->current_theme, 'dark'); ?>
                       onchange="toggleTheme()"
                       aria-label="切换主题模式">
                <span class="toggle-slider">
                    <span class="toggle-handle"></span>
                </span>
            </label>
            
            <?php if ($this->props['show_text']): ?>
                <span class="toggle-label">
                    <i class="io io-moon toggle-icon-dark"></i>
                </span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染图标样式
     */
    private function render_icon_style() {
        $tooltip_attrs = '';
        if ($this->props['show_tooltip']) {
            $tooltip_text = $this->current_theme === 'dark' ? '切换到浅色模式' : '切换到深色模式';
            $tooltip_attrs = 'data-toggle="tooltip" data-placement="left" title="' . esc_attr($tooltip_text) . '"';
        }
        
        ?>
        <div class="theme-icon-switcher" 
             onclick="toggleTheme()" 
             <?php echo $tooltip_attrs; ?>
             role="button"
             tabindex="0"
             aria-label="<?php echo $this->current_theme === 'dark' ? '切换到浅色模式' : '切换到深色模式'; ?>"
             onkeydown="if(event.key==='Enter'||event.key===' ') toggleTheme()">
            
            <div class="icon-container">
                <i class="io io-sunny theme-icon-light" style="<?php echo $this->current_theme === 'dark' ? 'display: none;' : ''; ?>"></i>
                <i class="io io-moon theme-icon-dark" style="<?php echo $this->current_theme === 'light' ? 'display: none;' : ''; ?>"></i>
            </div>
            
        </div>
        <?php
    }
    
    /**
     * 获取当前主题模式
     * 
     * @return string
     */
    private function get_current_theme_mode() {
        // 检查 Cookie
        if (isset($_COOKIE['io_night_mode'])) {
            return $_COOKIE['io_night_mode'] === '0' ? 'dark' : 'light';
        }
        
        // 检查用户设置
        if (is_user_logged_in()) {
            $user_theme = get_user_meta(get_current_user_id(), 'yinghe_theme_mode', true);
            if ($user_theme) {
                return $user_theme;
            }
        }
        
        // 使用默认设置
        return get_theme_mod('default_theme_mode', 'light');
    }
    
    /**
     * 获取组件样式
     * 
     * @return string
     */
    protected function get_component_styles() {
        return "
            .yinghe-theme-switcher {
                display: inline-block;
                user-select: none;
            }
            
            .theme-switcher-fixed {
                position: fixed;
                bottom: 100px;
                right: 30px;
                z-index: 1000;
            }
            
            /* 按钮样式 */
            .theme-switch-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                padding: 10px 15px;
                background: var(--yinghe-bg-primary);
                border: 1px solid var(--yinghe-border-color);
                border-radius: 25px;
                color: var(--yinghe-text-primary);
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 14px;
                box-shadow: var(--yinghe-shadow);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }
            
            .theme-switch-btn:hover {
                background: var(--yinghe-bg-secondary);
                border-color: var(--yinghe-primary);
                transform: translateY(-2px);
                box-shadow: var(--yinghe-shadow-lg);
            }
            
            .theme-switch-btn:active {
                transform: translateY(0);
            }
            
            .theme-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                transition: all 0.3s ease;
            }
            
            .theme-text {
                font-weight: 500;
                transition: all 0.3s ease;
            }
            
            /* 切换开关样式 */
            .theme-toggle-wrapper {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .toggle-label {
                display: flex;
                align-items: center;
                color: var(--yinghe-text-muted);
                font-size: 16px;
                transition: color 0.3s ease;
            }
            
            .theme-toggle-switch {
                position: relative;
                display: inline-block;
                width: 50px;
                height: 26px;
                cursor: pointer;
            }
            
            .theme-toggle-input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            
            .toggle-slider {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: var(--yinghe-bg-secondary);
                border: 1px solid var(--yinghe-border-color);
                border-radius: 26px;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            
            .toggle-handle {
                position: absolute;
                content: '';
                height: 20px;
                width: 20px;
                left: 2px;
                top: 2px;
                background: var(--yinghe-primary);
                border-radius: 50%;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            
            .theme-toggle-input:checked + .toggle-slider {
                background: var(--yinghe-primary);
                border-color: var(--yinghe-primary);
            }
            
            .theme-toggle-input:checked + .toggle-slider .toggle-handle {
                transform: translateX(24px);
                background: white;
            }
            
            /* 图标样式 */
            .theme-icon-switcher {
                width: 50px;
                height: 50px;
                background: var(--yinghe-bg-primary);
                border: 1px solid var(--yinghe-border-color);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: var(--yinghe-shadow);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }
            
            .theme-icon-switcher:hover {
                background: var(--yinghe-bg-secondary);
                border-color: var(--yinghe-primary);
                transform: translateY(-2px);
                box-shadow: var(--yinghe-shadow-lg);
            }
            
            .icon-container {
                position: relative;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .icon-container i {
                position: absolute;
                font-size: 18px;
                color: var(--yinghe-text-primary);
                transition: all 0.3s ease;
            }
            
            /* 尺寸变化 */
            .theme-switcher-small .theme-switch-btn {
                padding: 8px 12px;
                font-size: 12px;
            }
            
            .theme-switcher-small .theme-icon {
                font-size: 14px;
            }
            
            .theme-switcher-small .theme-icon-switcher {
                width: 40px;
                height: 40px;
            }
            
            .theme-switcher-small .icon-container i {
                font-size: 16px;
            }
            
            .theme-switcher-large .theme-switch-btn {
                padding: 12px 18px;
                font-size: 16px;
            }
            
            .theme-switcher-large .theme-icon {
                font-size: 18px;
            }
            
            .theme-switcher-large .theme-icon-switcher {
                width: 60px;
                height: 60px;
            }
            
            .theme-switcher-large .icon-container i {
                font-size: 20px;
            }
            
            /* 主题切换动画 */
            @keyframes themeSwitch {
                0% { transform: scale(1) rotate(0deg); }
                50% { transform: scale(1.1) rotate(180deg); }
                100% { transform: scale(1) rotate(360deg); }
            }
            
            .theme-switching .theme-icon,
            .theme-switching .icon-container i {
                animation: themeSwitch 0.6s ease-in-out;
            }
            
            /* 响应式调整 */
            @media (max-width: 768px) {
                .theme-switcher-fixed {
                    bottom: 80px;
                    right: 20px;
                }
                
                .theme-switch-btn {
                    padding: 8px 12px;
                    font-size: 13px;
                }
                
                .theme-icon-switcher {
                    width: 45px;
                    height: 45px;
                }
                
                .toggle-label {
                    font-size: 14px;
                }
            }
            
            /* 浅色模式特定样式 */
            .io-grey-mode .theme-switch-btn,
            .io-grey-mode .theme-icon-switcher {
                background: rgba(255, 255, 255, 0.9);
                border-color: rgba(0, 0, 0, 0.1);
            }
            
            .io-grey-mode .toggle-slider {
                background: #e2e8f0;
                border-color: #cbd5e0;
            }
            
            /* 深色模式特定样式 */
            .io-black-mode .theme-switch-btn,
            .io-black-mode .theme-icon-switcher {
                background: rgba(30, 41, 59, 0.9);
                border-color: rgba(255, 255, 255, 0.1);
            }
            
            .io-black-mode .toggle-slider {
                background: #374151;
                border-color: #4b5563;
            }
            
            /* 焦点样式 */
            .theme-switch-btn:focus,
            .theme-icon-switcher:focus {
                outline: 2px solid var(--yinghe-primary);
                outline-offset: 2px;
            }
            
            .theme-toggle-input:focus + .toggle-slider {
                box-shadow: 0 0 0 2px var(--yinghe-primary);
            }
        ";
    }
    
    /**
     * 获取组件脚本
     * 
     * @return string
     */
    protected function get_component_scripts() {
        return "
            // 主题切换器增强功能
            (function() {
                // 主题切换动画
                function addSwitchingAnimation(element) {
                    element.classList.add('theme-switching');
                    setTimeout(() => {
                        element.classList.remove('theme-switching');
                    }, 600);
                }
                
                // 监听主题切换事件
                window.addEventListener('themeChanged', function(e) {
                    const switchers = document.querySelectorAll('.yinghe-theme-switcher');
                    
                    switchers.forEach(switcher => {
                        const currentTheme = e.detail.theme;
                        switcher.dataset.currentTheme = currentTheme;
                        
                        // 更新图标和文本显示
                        const lightElements = switcher.querySelectorAll('.theme-icon-light, .theme-text-light');
                        const darkElements = switcher.querySelectorAll('.theme-icon-dark, .theme-text-dark');
                        
                        if (currentTheme === 'dark') {
                            lightElements.forEach(el => el.style.display = 'none');
                            darkElements.forEach(el => el.style.display = '');
                        } else {
                            lightElements.forEach(el => el.style.display = '');
                            darkElements.forEach(el => el.style.display = 'none');
                        }
                        
                        // 更新切换开关
                        const toggleInput = switcher.querySelector('.theme-toggle-input');
                        if (toggleInput) {
                            toggleInput.checked = currentTheme === 'dark';
                        }
                        
                        // 更新工具提示
                        const tooltipElements = switcher.querySelectorAll('[data-toggle=\"tooltip\"]');
                        tooltipElements.forEach(el => {
                            const newTitle = currentTheme === 'dark' ? '切换到浅色模式' : '切换到深色模式';
                            el.setAttribute('title', newTitle);
                            el.setAttribute('aria-label', newTitle);
                        });
                        
                        // 添加切换动画
                        addSwitchingAnimation(switcher);
                        
                        // 保存用户偏好
                        if (switcher.dataset.savePreference === 'true') {
                            // 保存到 localStorage
                            localStorage.setItem('yinghe_theme_preference', currentTheme);
                            
                            // 如果用户已登录，发送 AJAX 保存到用户设置
                            if (typeof yingheConfig !== 'undefined' && yingheConfig.ajaxUrl && yingheConfig.isLoggedIn) {
                                fetch(yingheConfig.ajaxUrl, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: new URLSearchParams({
                                        action: 'yinghe_save_theme_preference',
                                        nonce: yingheConfig.nonce,
                                        theme: currentTheme
                                    })
                                });
                            }
                        }
                    });
                });
                
                // 初始化工具提示
                if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                    const tooltipElements = document.querySelectorAll('.yinghe-theme-switcher [data-toggle=\"tooltip\"]');
                    tooltipElements.forEach(el => {
                        new bootstrap.Tooltip(el);
                    });
                }
                
                // 键盘导航支持
                document.addEventListener('keydown', function(e) {
                    if (e.target.classList.contains('theme-icon-switcher') && (e.key === 'Enter' || e.key === ' ')) {
                        e.preventDefault();
                        toggleTheme();
                    }
                });
                
            })();
        ";
    }
}

/**
 * Helper 函数
 */
function yinghe_render_theme_switcher($props = []) {
    echo YingheThemeSwitcherComponent::render_static($props);
}

function yinghe_get_theme_switcher($props = []) {
    return YingheThemeSwitcherComponent::render_static($props);
}