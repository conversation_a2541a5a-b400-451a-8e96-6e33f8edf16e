<?php
/**
 * 侧边栏导航组件
 * 
 * @package YingheTheme
 * @subpackage Components\Layout
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 侧边栏导航组件类
 */
class YingheSidebarNavComponent extends YingheAbstractComponent {
    
    /**
     * 获取默认属性
     * 
     * @return array
     */
    protected function get_default_props() {
        return [
            'menu_location' => 'primary',
            'logo_config' => [
                'expanded_light' => get_theme_mod('logo_expanded_light', get_template_directory_uri() . '/static/picture/logo-white-wide.webp'),
                'expanded_dark' => get_theme_mod('logo_expanded_dark', get_template_directory_uri() . '/static/picture/logo-black-wide.webp'),
                'collapsed_light' => get_theme_mod('logo_collapsed_light', get_template_directory_uri() . '/static/picture/logo-white.webp'),
                'collapsed_dark' => get_theme_mod('logo_collapsed_dark', get_template_directory_uri() . '/static/picture/logo-black.webp'),
            ],
            'show_domain_info' => get_theme_mod('show_domain_info', true),
            'domain_links' => get_option('yinghe_domain_links', [
                ['url' => home_url('/'), 'title' => get_bloginfo('name'), 'text' => '硬核指南.com']
            ]),
            'sticky' => true,
            'collapsible' => true,
            'show_footer_links' => true,
        ];
    }
    
    /**
     * 渲染组件
     */
    protected function render_component() {
        ?>
        <div<?php echo $this->get_attributes(); ?>>
            <div class="modal-dialog h-100 sidebar-nav-inner">
                <?php $this->render_logo(); ?>
                <?php $this->render_menu(); ?>
                <?php if ($this->props['show_footer_links']): ?>
                    <?php $this->render_footer(); ?>
                <?php endif; ?>
                <?php if ($this->props['show_domain_info']): ?>
                    <?php $this->render_domain_info(); ?>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染Logo区域
     */
    private function render_logo() {
        $logo_config = $this->props['logo_config'];
        ?>
        <div class="sidebar-logo">
            <div class="logo overflow-hidden">
                <h1 class="text-hide position-absolute"><?php bloginfo('name'); ?></h1>
                
                <a href="<?php echo esc_url(home_url('/')); ?>" class="logo-expanded">
                    <img src="<?php echo esc_url($logo_config['expanded_light']); ?>" 
                         class="logo-light" alt="<?php bloginfo('name'); ?>">
                    <img src="<?php echo esc_url($logo_config['expanded_dark']); ?>" 
                         class="logo-dark d-none" alt="<?php bloginfo('name'); ?>">
                </a>
                
                <a href="<?php echo esc_url(home_url('/')); ?>" class="logo-collapsed">
                    <img src="<?php echo esc_url($logo_config['collapsed_light']); ?>" 
                         class="logo-light" alt="<?php bloginfo('name'); ?>">
                    <img src="<?php echo esc_url($logo_config['collapsed_dark']); ?>" 
                         class="logo-dark d-none" alt="<?php bloginfo('name'); ?>">
                </a>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染导航菜单
     */
    private function render_menu() {
        ?>
        <div class="sidebar-menu flex-fill">
            <div class="sidebar-scroll">
                <div class="sidebar-menu-inner">
                    <?php $this->render_category_menu(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染分类菜单
     */
    private function render_category_menu() {
        $categories = get_terms([
            'taxonomy' => 'site_category',
            'hide_empty' => false,
            'parent' => 0,
            'orderby' => 'menu_order',
            'order' => 'ASC'
        ]);
        
        if (!empty($categories)) {
            echo '<ul>';
            
            foreach ($categories as $category) {
                $this->render_menu_item($category);
            }
            
            echo '</ul>';
        }
    }
    
    /**
     * 渲染单个菜单项
     * 
     * @param WP_Term $category 分类对象
     */
    private function render_menu_item($category) {
        $icon_class = get_term_meta($category->term_id, 'category_icon', true);
        if (!$icon_class) {
            $icon_class = $this->get_default_icon($category->slug);
        }
        
        $subcategories = get_terms([
            'taxonomy' => 'site_category',
            'hide_empty' => false,
            'parent' => $category->term_id,
            'orderby' => 'menu_order',
            'order' => 'ASC'
        ]);
        
        $has_children = !empty($subcategories);
        $item_class = 'sidebar-item';
        
        // 特殊分类样式
        if ($category->slug === 'cooperation') {
            $item_class .= ' term-178';
        }
        ?>
        <li class="<?php echo esc_attr($item_class); ?>">
            <a href="#term-<?php echo esc_attr($category->term_id); ?>" 
               class="smooth" 
               data-change="#term-<?php echo esc_attr($category->term_id); ?>">
                <i class="io <?php echo esc_attr($icon_class); ?> icon-fw icon-lg"></i>
                <span><?php echo esc_html($category->name); ?></span>
            </a>
            
            <?php if ($has_children): ?>
                <i class="iconfont icon-arrow-r-m sidebar-more text-sm"></i>
                <ul>
                    <?php foreach ($subcategories as $subcategory): ?>
                        <li>
                            <a href="#term-<?php echo esc_attr($category->term_id); ?>-<?php echo esc_attr($subcategory->term_id); ?>" 
                               class="smooth">
                                <span><?php echo esc_html($subcategory->name); ?></span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </li>
        <?php
    }
    
    /**
     * 获取默认图标
     * 
     * @param string $category_slug 分类别名
     * @return string
     */
    private function get_default_icon($category_slug) {
        $icon_map = [
            'cooperation' => 'io-heart',
            'yingshi' => 'io-film',
            'erciyuan' => 'io-acg',
            'yinyue' => 'io-headphones',
            'yuedu' => 'io-read',
            'yule' => 'io-send',
            'gongju' => 'io-package',
            'shengqian' => 'io-sheng',
        ];
        
        return $icon_map[$category_slug] ?? 'io-folder';
    }
    
    /**
     * 渲染底部链接
     */
    private function render_footer() {
        ?>
        <div class="border-top py-2 border-color">
            <div class="sidebar-nav-footer">
                <ul>
                    <li class="call-yinghe">
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('about-yinghe'))); ?>">
                            <span><?php _e('呼叫硬核君', 'yinghe'); ?></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染域名信息
     */
    private function render_domain_info() {
        $domain_links = $this->props['domain_links'];
        if (empty($domain_links)) {
            return;
        }
        ?>
        <div class="domain">
            <div class="domain-header">
                <span><?php _e('回家地址', 'yinghe'); ?></span>
            </div>
            <?php foreach ($domain_links as $domain): ?>
                <a href="<?php echo esc_url($domain['url']); ?>" 
                   title="<?php echo esc_attr($domain['title']); ?>" 
                   class="yinghe-domain" 
                   target="_blank" 
                   rel="noopener">
                    <?php echo esc_html($domain['text']); ?>
                </a>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * 获取组件样式
     * 
     * @return string
     */
    protected function get_component_styles() {
        return "
            .yinghe-sidebar-nav {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                width: var(--yinghe-sidebar-width, 220px);
                background: var(--yinghe-bg-primary);
                z-index: 1000;
                transition: var(--yinghe-transition);
                border-right: 1px solid var(--yinghe-border-color);
                box-shadow: var(--yinghe-shadow);
            }
            
            .yinghe-sidebar-nav.collapsed {
                width: var(--yinghe-sidebar-collapsed-width, 110px);
            }
            
            .sidebar-nav-inner {
                height: 100%;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }
            
            .sidebar-logo {
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid var(--yinghe-border-color);
                background: var(--yinghe-bg-secondary);
            }
            
            .sidebar-logo .logo img {
                max-height: 40px;
                width: auto;
            }
            
            .sidebar-menu {
                flex: 1;
                overflow-y: auto;
                scrollbar-width: thin;
                scrollbar-color: var(--yinghe-gray-400) transparent;
            }
            
            .sidebar-menu::-webkit-scrollbar {
                width: 4px;
            }
            
            .sidebar-menu::-webkit-scrollbar-thumb {
                background: var(--yinghe-gray-400);
                border-radius: 2px;
            }
            
            .sidebar-menu::-webkit-scrollbar-track {
                background: transparent;
            }
            
            .sidebar-item > a {
                display: flex;
                align-items: center;
                padding: 12px 20px;
                color: var(--yinghe-text-secondary);
                text-decoration: none;
                transition: var(--yinghe-transition-fast);
                border-left: 3px solid transparent;
            }
            
            .sidebar-item > a:hover,
            .sidebar-item.active > a {
                background: var(--yinghe-bg-tertiary);
                color: var(--yinghe-primary);
                border-left-color: var(--yinghe-primary);
            }
            
            .sidebar-item i {
                margin-right: 12px;
                width: 20px;
                text-align: center;
                font-size: 16px;
            }
            
            .sidebar-more {
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
                transition: var(--yinghe-transition-fast);
            }
            
            .sidebar-item.expanded .sidebar-more {
                transform: translateY(-50%) rotate(90deg);
            }
            
            .sidebar-item ul {
                display: none;
                background: var(--yinghe-bg-tertiary);
                border-left: 2px solid var(--yinghe-border-color);
                margin-left: 20px;
            }
            
            .sidebar-item.expanded ul {
                display: block;
            }
            
            .sidebar-item ul a {
                padding: 8px 20px;
                font-size: 14px;
                border-left: none !important;
            }
            
            .sidebar-nav-footer {
                padding: 10px 20px;
                border-top: 1px solid var(--yinghe-border-color);
            }
            
            .sidebar-nav-footer a {
                color: var(--yinghe-text-muted);
                text-decoration: none;
                font-size: 14px;
                transition: var(--yinghe-transition-fast);
            }
            
            .sidebar-nav-footer a:hover {
                color: var(--yinghe-primary);
            }
            
            .domain {
                padding: 15px 20px;
                background: var(--yinghe-bg-secondary);
                border-top: 1px solid var(--yinghe-border-color);
            }
            
            .domain-header {
                font-size: 12px;
                color: var(--yinghe-text-muted);
                margin-bottom: 8px;
            }
            
            .yinghe-domain {
                display: block;
                padding: 6px 0;
                color: var(--yinghe-text-secondary);
                text-decoration: none;
                font-size: 13px;
                transition: var(--yinghe-transition-fast);
            }
            
            .yinghe-domain:hover {
                color: var(--yinghe-primary);
            }
            
            @media (max-width: 768px) {
                .yinghe-sidebar-nav {
                    transform: translateX(-100%);
                    transition: transform 0.3s ease;
                }
                
                .yinghe-sidebar-nav.open {
                    transform: translateX(0);
                }
                
                .sidebar-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }
                
                .sidebar-overlay.show {
                    opacity: 1;
                    visibility: visible;
                }
            }
        ";
    }
    
    /**
     * 获取组件脚本
     * 
     * @return string
     */
    protected function get_component_scripts() {
        return "
            // 侧边栏导航交互
            (function() {
                const sidebar = document.getElementById('{$this->unique_id}');
                if (!sidebar) return;
                
                // 平滑滚动到目标区域
                sidebar.addEventListener('click', function(e) {
                    if (e.target.classList.contains('smooth') || e.target.closest('.smooth')) {
                        e.preventDefault();
                        
                        const link = e.target.classList.contains('smooth') ? e.target : e.target.closest('.smooth');
                        const targetId = link.getAttribute('href').substring(1);
                        const targetElement = document.getElementById(targetId);
                        
                        if (targetElement) {
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                            
                            // 更新激活状态
                            sidebar.querySelectorAll('.sidebar-item').forEach(item => {
                                item.classList.remove('active');
                            });
                            link.closest('.sidebar-item').classList.add('active');
                            
                            // 移动端关闭侧边栏
                            if (window.innerWidth <= 768) {
                                sidebar.classList.remove('open');
                                document.body.classList.remove('sidebar-open');
                            }
                        }
                    }
                });
                
                // 子菜单展开/收起
                sidebar.addEventListener('click', function(e) {
                    if (e.target.classList.contains('sidebar-more')) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const parentItem = e.target.closest('.sidebar-item');
                        parentItem.classList.toggle('expanded');
                    }
                });
                
                // 移动端侧边栏切换
                if (window.innerWidth <= 768) {
                    // 创建遮罩层
                    const overlay = document.createElement('div');
                    overlay.className = 'sidebar-overlay';
                    document.body.appendChild(overlay);
                    
                    const toggleButton = document.getElementById('sidebar-switch');
                    if (toggleButton) {
                        toggleButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            sidebar.classList.toggle('open');
                            overlay.classList.toggle('show');
                            document.body.classList.toggle('sidebar-open');
                        });
                    }
                    
                    // 点击遮罩关闭侧边栏
                    overlay.addEventListener('click', function() {
                        sidebar.classList.remove('open');
                        overlay.classList.remove('show');
                        document.body.classList.remove('sidebar-open');
                    });
                }
                
                // 滚动时高亮对应菜单项
                window.addEventListener('scroll', function() {
                    const sections = document.querySelectorAll('[id^=\"term-\"]');
                    const scrollTop = window.pageYOffset;
                    
                    sections.forEach(section => {
                        const sectionTop = section.offsetTop - 100;
                        const sectionHeight = section.offsetHeight;
                        const sectionId = section.getAttribute('id');
                        
                        if (scrollTop >= sectionTop && scrollTop < sectionTop + sectionHeight) {
                            sidebar.querySelectorAll('.sidebar-item').forEach(item => {
                                item.classList.remove('active');
                            });
                            
                            const activeLink = sidebar.querySelector(`a[href=\"#${sectionId}\"]`);
                            if (activeLink) {
                                activeLink.closest('.sidebar-item').classList.add('active');
                            }
                        }
                    });
                });
                
            })();
        ";
    }
}

// Helper 函数已移至 includes/theme-helpers.php 文件中