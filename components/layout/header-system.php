<?php
/**
 * 头部系统组件
 * 
 * @package YingheTheme
 * @subpackage Components\Layout
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 头部系统组件类
 */
class YingheHeaderSystemComponent extends YingheAbstractComponent {
    
    /**
     * 获取默认属性
     * 
     * @return array
     */
    protected function get_default_props() {
        return [
            'show_mini_header' => true,
            'show_big_header' => true,
            'search_config' => [
                'enabled' => true,
                'engines' => $this->get_search_engines(),
                'placeholder' => __('输入关键字搜索', 'yinghe'),
            ],
            'announcement_config' => [
                'enabled' => get_theme_mod('show_announcements', true),
                'content' => get_option('yinghe_announcement_content', ''),
            ],
            'menu_items' => [
                'wechat' => [
                    'icon' => 'io-wechat-o',
                    'label' => __('关注硬核', 'yinghe'),
                    'image' => get_theme_mod('wechat_qr_image', get_template_directory_uri() . '/static/picture/IjYcrkGkj4ezaO9-lPbh4g37fac9be71fe9602ae079ef28e0cba33.png'),
                ],
                'history' => [
                    'icon' => 'io-clock1',
                    'label' => __('浏览记录', 'yinghe'),
                ],
            ],
            'mobile_logo' => [
                'light' => get_theme_mod('mobile_logo_light', get_template_directory_uri() . '/static/picture/logo-white-wide.webp'),
                'dark' => get_theme_mod('mobile_logo_dark', get_template_directory_uri() . '/static/picture/logo-black-wide.webp'),
            ],
        ];
    }
    
    /**
     * 渲染组件
     */
    protected function render_component() {
        ?>
        <div<?php echo $this->get_attributes(); ?>>
            <?php if ($this->props['show_mini_header']): ?>
                <?php $this->render_mini_header(); ?>
            <?php endif; ?>
            
            <?php if ($this->props['show_big_header']): ?>
                <?php $this->render_big_header(); ?>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染迷你头部
     */
    private function render_mini_header() {
        ?>
        <div class="no-big-header header-nav">
            <div id="header" class="page-header sticky">
                <div class="navbar navbar-expand-md">
                    <div class="container-fluid p-0 position-relative">
                        <?php $this->render_mobile_logo(); ?>
                        <?php $this->render_header_menu(); ?>
                        <?php $this->render_mobile_menu_toggle(); ?>
                        <?php $this->render_filter_toggle(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染大头部
     */
    private function render_big_header() {
        $search_config = $this->props['search_config'];
        $announcement_config = $this->props['announcement_config'];
        ?>
        <div class="header-big unchanged no-bg">
            <?php if ($search_config['enabled']): ?>
                <?php $this->render_search_section(); ?>
            <?php endif; ?>
            
            <?php if ($announcement_config['enabled'] && !empty($announcement_config['content'])): ?>
                <?php $this->render_announcement(); ?>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染移动端Logo
     */
    private function render_mobile_logo() {
        $mobile_logo = $this->props['mobile_logo'];
        ?>
        <div class="position-absolute w-100 text-center">
            <a href="<?php echo esc_url(home_url('/')); ?>" 
               class="navbar-brand d-md-none m-0" 
               title="<?php bloginfo('name'); ?>">
                <img src="<?php echo esc_url($mobile_logo['light']); ?>" 
                     class="logo-light" alt="<?php bloginfo('name'); ?>">
                <img src="<?php echo esc_url($mobile_logo['dark']); ?>" 
                     class="logo-dark d-none" alt="<?php bloginfo('name'); ?>">
            </a>
        </div>
        <?php
    }
    
    /**
     * 渲染头部菜单
     */
    private function render_header_menu() {
        $menu_items = $this->props['menu_items'];
        ?>
        <div class="header-menu">
            <?php foreach ($menu_items as $key => $item): ?>
                <div class="header-menu-item toggle-item <?php echo esc_attr($this->get_toggle_classes($key)); ?>">
                    <div class="header-menu-toggle">
                        <i class="io <?php echo esc_attr($item['icon']); ?>"></i>
                        <?php if (!empty($item['label'])): ?>
                            <span class="header-menu-name"><?php echo esc_html($item['label']); ?></span>
                        <?php endif; ?>
                    </div>
                    <?php $this->render_menu_dropdown($key, $item); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染菜单下拉内容
     * 
     * @param string $key 菜单项键名
     * @param array $item 菜单项配置
     */
    private function render_menu_dropdown($key, $item) {
        ?>
        <div class="toggle">
            <div class="toggle-inner">
                <div class="toggle-inner-item">
                    <?php
                    switch ($key) {
                        case 'wechat':
                            $this->render_wechat_content($item);
                            break;
                        case 'history':
                            $this->render_history_content();
                            break;
                    }
                    ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染微信二维码内容
     * 
     * @param array $item 菜单项配置
     */
    private function render_wechat_content($item) {
        if (!empty($item['image'])) {
            ?>
            <img src="<?php echo esc_url($item['image']); ?>" 
                 width="100%" height="100%" 
                 alt="<?php echo esc_attr($item['label']); ?>">
            <?php
        }
    }
    
    /**
     * 渲染浏览记录内容
     */
    private function render_history_content() {
        ?>
        <div class="toggle-inner-item-title"><?php _e('浏览记录', 'yinghe'); ?></div>
        <div class="toggle-prompt"><?php _e('还没有浏览记录', 'yinghe'); ?></div>
        <ul class="toggle-visited" id="browsing-history">
            <!-- 浏览记录将通过JavaScript动态加载 -->
        </ul>
        <?php
    }
    
    /**
     * 渲染移动端菜单切换按钮
     */
    private function render_mobile_menu_toggle() {
        ?>
        <div class="nav-item d-md-none mobile-menu py-2 position-relative">
            <a href="javascript:" id="sidebar-switch" data-toggle="modal" data-target="#sidebar">
                <i class="io io-classification icon-lg"></i>
            </a>
        </div>
        <?php
    }
    
    /**
     * 渲染筛选切换按钮
     */
    private function render_filter_toggle() {
        ?>
        <div class="header-navbar">
            <div class="header-menu-item toggle-item toggle-sidebar left filter">
                <div class="header-menu-toggle">
                    <i class="io io-more"></i>
                    <span class="header-menu-name"><?php _e('快速筛选网站', 'yinghe'); ?></span>
                </div>
                <div class="toggle">
                    <div class="toggle-inner">
                        <div class="toggle-inner-item">
                            <?php $this->render_filter_content(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染筛选内容
     */
    private function render_filter_content() {
        ?>
        <div class="toggle-filter">
            <div class="toggle-filter-item">
                <div class="filter-name">硬核标签<span>你的观看喜好/标签</span></div>
                <div class="filter-content">
                    <div class="filter-list">
                        <?php
                        $recommended_tags = ['硬核推荐', '高画质', '弹幕多', '4K画质', '高清线路多', '无广告', '可缓存'];
                        foreach ($recommended_tags as $tag) :
                        ?>
                            <a href="<?php echo esc_url(get_term_link($tag, 'sitetag')); ?>">
                                <i class="io io-hash"></i>
                                <span><?php echo esc_html($tag); ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="toggle-filter-item">
                <div class="filter-name">分类<span>需要哪类的网址/APP</span></div>
                <div class="filter-content">
                    <div class="filter-list">
                        <?php
                        $main_categories = get_terms([
                            'taxonomy' => 'site_category',
                            'hide_empty' => false,
                            'parent' => 0,
                            'number' => 5
                        ]);
                        
                        foreach ($main_categories as $category) :
                        ?>
                            <a href="<?php echo esc_url(get_term_link($category)); ?>">
                                <i class="io io-hash"></i>
                                <span><?php echo esc_html($category->name); ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="toggle-filter-item">
                <div class="filter-name">系统/场景<span>在什么场景/系统中使用</span></div>
                <div class="filter-content">
                    <div class="filter-list">
                        <?php
                        $platform_tags = [
                            'Android客户端' => 'Android/HarmonyOS',
                            'iOS客户端' => 'iOS',
                            'Windows软件' => 'Windows',
                            'macOS' => 'macOS',
                            'Linux' => 'Linux',
                            '网页端' => '浏览器',
                            'TV客户端' => '电视',
                            '车机客户端' => '车机'
                        ];
                        
                        foreach ($platform_tags as $tag => $label) :
                        ?>
                            <a href="<?php echo esc_url(get_term_link($tag, 'sitetag')); ?>">
                                <i class="io io-hash"></i>
                                <span><?php echo esc_html($label); ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染搜索区域
     */
    private function render_search_section() {
        $search_engines = $this->props['search_config']['engines'];
        ?>
        <div class="s-search">
            <div id="search" class="s-search mx-auto">
                <?php $this->render_search_tabs($search_engines); ?>
                <?php $this->render_search_form(); ?>
                <?php $this->render_search_engines($search_engines); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染搜索标签页
     * 
     * @param array $engines 搜索引擎列表
     */
    private function render_search_tabs($engines) {
        $groups = $this->group_search_engines($engines);
        ?>
        <div id="search-list-menu">
            <div class="s-type text-center">
                <div class="s-type-list big tab-auto-scrollbar overflow-x-auto">
                    <div class="anchor" style="position: absolute; left: 50%; opacity: 0;"></div>
                    <?php $first = true; foreach ($groups as $group_key => $group): ?>
                        <label for="type-<?php echo esc_attr($group_key); ?>" 
                               class="<?php echo $first ? 'active' : ''; ?>" 
                               data-page="home" 
                               data-id="group-<?php echo esc_attr($group_key); ?>">
                            <span><?php echo esc_html($group['label']); ?></span>
                        </label>
                        <?php $first = false; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染搜索表单
     */
    private function render_search_form() {
        ?>
        <form action="<?php echo esc_url(home_url('/')); ?>" method="get" target="_blank" class="super-search-fm">
            <input type="text" 
                   id="search-text" 
                   class="form-control smart-tips search-key" 
                   placeholder="<?php echo esc_attr($this->props['search_config']['placeholder']); ?>" 
                   style="outline:0" 
                   autocomplete="off" 
                   data-status="true">
            <button type="submit" id="btn_search">
                <i class="io io-search-b"></i>
            </button>
        </form>
        <?php
    }
    
    /**
     * 渲染搜索引擎列表
     * 
     * @param array $engines 搜索引擎列表
     */
    private function render_search_engines($engines) {
        $groups = $this->group_search_engines($engines);
        ?>
        <div id="search-list" class="hide-type-list">
            <?php $first_group = true; foreach ($groups as $group_key => $group): ?>
                <div class="search-group justify-content-center group-<?php echo esc_attr($group_key); ?> <?php echo $first_group ? 's-current' : ''; ?>">
                    <ul class="search-type tab-auto-scrollbar overflow-x-auto">
                        <?php $first_engine = true; foreach ($group['engines'] as $engine): ?>
                            <li>
                                <input <?php echo $first_group && $first_engine ? 'checked="checked"' : ''; ?> 
                                       hidden 
                                       type="radio" 
                                       name="type" 
                                       data-page="home" 
                                       id="type-<?php echo esc_attr($engine->name); ?>" 
                                       value="<?php echo esc_attr($engine->search_url); ?>" 
                                       data-placeholder="<?php echo esc_attr($engine->placeholder); ?>">
                                <label for="type-<?php echo esc_attr($engine->name); ?>">
                                    <span class="text-muted"><?php echo esc_html($engine->display_name); ?></span>
                                </label>
                            </li>
                            <?php $first_engine = false; ?>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php $first_group = false; ?>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染公告
     */
    private function render_announcement() {
        $content = $this->props['announcement_config']['content'];
        if (!empty($content)) {
            ?>
            <div class="index-top" id="gonggao<?php echo date('Ymd'); ?>">
                <?php echo wp_kses_post($content); ?>
            </div>
            <?php
        }
    }
    
    /**
     * 获取搜索引擎
     * 
     * @return array
     */
    private function get_search_engines() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'yinghe_search_engines';
        
        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            return $this->get_default_search_engines();
        }
        
        $engines = $wpdb->get_results("
            SELECT * FROM $table 
            WHERE is_active = 1 
            ORDER BY category, sort_order ASC
        ");
        
        return $engines ?: $this->get_default_search_engines();
    }
    
    /**
     * 获取默认搜索引擎
     * 
     * @return array
     */
    private function get_default_search_engines() {
        return [
            (object) [
                'id' => 1,
                'name' => 'qiku',
                'display_name' => '奇库影视',
                'search_url' => 'https://qkys1.cc/vodsearch/%s%-------------.html',
                'placeholder' => '输入你想看的影片名',
                'category' => 'yingshi',
                'is_default' => 1
            ],
            (object) [
                'id' => 2,
                'name' => 'wylg',
                'display_name' => '555电影',
                'search_url' => 'https://www.55yy6.com/vodsearch/%s%-------------.html',
                'placeholder' => '输入你想看的影片名称',
                'category' => 'yingshi',
                'is_default' => 0
            ],
            (object) [
                'id' => 3,
                'name' => 'douban',
                'display_name' => '豆瓣',
                'search_url' => 'https://search.douban.com/movie/subject_search?search_text=%s%',
                'placeholder' => '输入你想查询资料的影片、演员、导演、编剧的名称',
                'category' => 'ziliao',
                'is_default' => 0
            ],
            (object) [
                'id' => 4,
                'name' => 'imdb',
                'display_name' => 'IMDb',
                'search_url' => 'https://www.imdb.com/find/?q=%s%',
                'placeholder' => '输入你想查询资料的影片、演员、导演、编剧的名称',
                'category' => 'ziliao',
                'is_default' => 0
            ]
        ];
    }
    
    /**
     * 分组搜索引擎
     * 
     * @param array $engines 搜索引擎列表
     * @return array
     */
    private function group_search_engines($engines) {
        $groups = [
            'yingshi' => ['label' => __('影视搜索', 'yinghe'), 'engines' => []],
            'ziliao' => ['label' => __('影片资料', 'yinghe'), 'engines' => []],
        ];
        
        foreach ($engines as $engine) {
            if (isset($groups[$engine->category])) {
                $groups[$engine->category]['engines'][] = $engine;
            }
        }
        
        return array_filter($groups, function($group) {
            return !empty($group['engines']);
        });
    }
    
    /**
     * 获取切换类名
     * 
     * @param string $key 菜单键名
     * @return string
     */
    private function get_toggle_classes($key) {
        $classes = ['toggle-dropdown', 'top'];
        
        switch ($key) {
            case 'wechat':
                $classes[] = 'wechat';
                $classes[] = 'toggle-sidebar';
                $classes[] = 'right';
                break;
            case 'history':
                $classes[] = 'visited';
                break;
        }
        
        return implode(' ', $classes);
    }
    
    /**
     * 获取组件样式
     * 
     * @return string
     */
    protected function get_component_styles() {
        return "
            .yinghe-header-system {
                position: relative;
                z-index: 999;
            }
            
            .header-nav {
                background: var(--yinghe-bg-primary);
                box-shadow: var(--yinghe-shadow);
                position: relative;
                z-index: 1000;
            }
            
            .page-header {
                padding: 10px 0;
                background: var(--yinghe-bg-primary);
                border-bottom: 1px solid var(--yinghe-border-color);
            }
            
            .header-big {
                background: linear-gradient(135deg, var(--yinghe-secondary) 0%, var(--yinghe-accent) 100%);
                padding: 60px 0 40px;
                text-align: center;
                color: white;
            }
            
            .header-menu {
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                gap: 10px;
                z-index: 1001;
            }
            
            .header-menu-toggle {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                background: var(--yinghe-bg-primary);
                border: 1px solid var(--yinghe-border-color);
                border-radius: var(--yinghe-border-radius);
                cursor: pointer;
                transition: var(--yinghe-transition-fast);
                color: var(--yinghe-text-secondary);
            }
            
            .header-menu-toggle:hover {
                transform: translateY(-1px);
                box-shadow: var(--yinghe-shadow);
                color: var(--yinghe-primary);
                border-color: var(--yinghe-primary);
            }
            
            .header-menu-toggle i {
                margin-right: 6px;
                font-size: 16px;
            }
            
            .header-menu-name {
                font-size: 14px;
                white-space: nowrap;
            }
            
            .toggle-item {
                position: relative;
            }
            
            .toggle {
                position: absolute;
                top: 100%;
                right: 0;
                margin-top: 8px;
                background: var(--yinghe-bg-primary);
                border: 1px solid var(--yinghe-border-color);
                border-radius: var(--yinghe-border-radius);
                box-shadow: var(--yinghe-shadow-lg);
                min-width: 200px;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px);
                transition: all 0.3s ease;
                z-index: 1002;
            }
            
            .toggle-item.show .toggle,
            .toggle-item:hover .toggle {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
            
            .toggle-inner {
                padding: 15px;
            }
            
            .toggle-inner-item-title {
                font-weight: 600;
                margin-bottom: 10px;
                color: var(--yinghe-text-primary);
            }
            
            .toggle-prompt {
                color: var(--yinghe-text-muted);
                font-size: 14px;
            }
            
            .s-search {
                max-width: 600px;
                margin: 0 auto;
                padding: 0 20px;
            }
            
            .s-type-list {
                display: flex;
                justify-content: center;
                margin-bottom: 20px;
                gap: 10px;
            }
            
            .s-type-list label {
                padding: 8px 16px;
                background: rgba(255,255,255,0.2);
                border-radius: 20px;
                color: #fff;
                cursor: pointer;
                transition: var(--yinghe-transition-fast);
                font-size: 14px;
            }
            
            .s-type-list label.active,
            .s-type-list label:hover {
                background: rgba(255,255,255,0.3);
                transform: translateY(-1px);
            }
            
            .super-search-fm {
                position: relative;
                margin-bottom: 20px;
            }
            
            #search-text {
                width: 100%;
                padding: 15px 60px 15px 20px;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                background: var(--yinghe-bg-primary);
                color: var(--yinghe-text-primary);
            }
            
            #search-text:focus {
                outline: none;
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }
            
            #btn_search {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                width: 50px;
                height: 40px;
                border: none;
                background: var(--yinghe-primary);
                color: white;
                border-radius: 20px;
                cursor: pointer;
                transition: var(--yinghe-transition-fast);
            }
            
            #btn_search:hover {
                background: var(--yinghe-secondary);
                transform: translateY(-50%) scale(1.05);
            }
            
            .search-type {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: 10px;
            }
            
            .search-type li {
                list-style: none;
            }
            
            .search-type label {
                display: block;
                padding: 6px 12px;
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                color: rgba(255,255,255,0.8);
                cursor: pointer;
                transition: var(--yinghe-transition-fast);
                font-size: 13px;
            }
            
            .search-type input:checked + label,
            .search-type label:hover {
                background: rgba(255,255,255,0.2);
                color: white;
            }
            
            .search-group {
                display: none;
            }
            
            .search-group.s-current {
                display: block;
            }
            
            .filter-list {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin-top: 8px;
            }
            
            .filter-list a {
                display: flex;
                align-items: center;
                padding: 4px 8px;
                background: var(--yinghe-bg-secondary);
                border-radius: 12px;
                color: var(--yinghe-text-secondary);
                text-decoration: none;
                font-size: 12px;
                transition: var(--yinghe-transition-fast);
            }
            
            .filter-list a:hover {
                background: var(--yinghe-primary);
                color: white;
            }
            
            .filter-list i {
                margin-right: 4px;
                font-size: 10px;
            }
            
            .filter-name {
                font-weight: 600;
                margin-bottom: 8px;
                color: var(--yinghe-text-primary);
            }
            
            .filter-name span {
                font-weight: normal;
                font-size: 12px;
                color: var(--yinghe-text-muted);
                margin-left: 8px;
            }
            
            @media (max-width: 768px) {
                .header-menu {
                    position: fixed;
                    right: 18px;
                    top: 10px;
                }
                
                .header-menu-name {
                    display: none;
                }
                
                .header-big {
                    padding: 40px 15px 30px;
                }
                
                .s-search {
                    max-width: 95%;
                }
                
                .s-type-list {
                    flex-wrap: wrap;
                    justify-content: center;
                }
                
                .toggle {
                    right: auto;
                    left: 0;
                    min-width: 280px;
                }
                
                .toggle-filter {
                    max-height: 400px;
                    overflow-y: auto;
                }
            }
        ";
    }
    
    /**
     * 获取组件脚本
     * 
     * @return string
     */
    protected function get_component_scripts() {
        return "
            // 头部系统交互逻辑
            (function() {
                const header = document.getElementById('{$this->unique_id}');
                if (!header) return;
                
                // 搜索引擎切换
                const searchTabs = header.querySelectorAll('.s-type-list label');
                const searchGroups = header.querySelectorAll('.search-group');
                
                searchTabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        const targetGroup = this.dataset.id;
                        
                        // 更新标签状态
                        searchTabs.forEach(t => t.classList.remove('active'));
                        this.classList.add('active');
                        
                        // 显示对应搜索组
                        searchGroups.forEach(group => {
                            group.classList.remove('s-current');
                            if (group.classList.contains(targetGroup)) {
                                group.classList.add('s-current');
                                
                                // 选中第一个搜索引擎
                                const firstEngine = group.querySelector('input[type=\"radio\"]');
                                if (firstEngine) {
                                    firstEngine.checked = true;
                                    updateSearchPlaceholder(firstEngine.dataset.placeholder);
                                }
                            }
                        });
                    });
                });
                
                // 搜索引擎选择
                const searchEngines = header.querySelectorAll('input[name=\"type\"]');
                searchEngines.forEach(engine => {
                    engine.addEventListener('change', function() {
                        if (this.checked) {
                            updateSearchPlaceholder(this.dataset.placeholder);
                        }
                    });
                });
                
                // 更新搜索框占位符
                function updateSearchPlaceholder(placeholder) {
                    const searchInput = header.querySelector('#search-text');
                    if (searchInput && placeholder) {
                        searchInput.setAttribute('placeholder', placeholder);
                    }
                }
                
                // 搜索表单提交
                const searchForm = header.querySelector('.super-search-fm');
                if (searchForm) {
                    searchForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        const searchInput = this.querySelector('#search-text');
                        const selectedEngine = header.querySelector('input[name=\"type\"]:checked');
                        
                        if (searchInput && selectedEngine && searchInput.value.trim()) {
                            const searchUrl = selectedEngine.value.replace('%s%', encodeURIComponent(searchInput.value.trim()));
                            window.open(searchUrl, '_blank', 'noopener');
                            
                            // 记录搜索行为
                            if (typeof yingheConfig !== 'undefined') {
                                fetch(yingheConfig.ajaxUrl, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: new URLSearchParams({
                                        action: 'yinghe_record_search',
                                        nonce: yingheConfig.nonce,
                                        engine: selectedEngine.id.replace('type-', ''),
                                        query: searchInput.value.trim()
                                    })
                                });
                            }
                        }
                    });
                }
                
                // 下拉菜单切换
                const toggleItems = header.querySelectorAll('.toggle-item');
                toggleItems.forEach(item => {
                    const toggle = item.querySelector('.header-menu-toggle');
                    
                    if (toggle) {
                        toggle.addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            // 关闭其他下拉菜单
                            toggleItems.forEach(otherItem => {
                                if (otherItem !== item) {
                                    otherItem.classList.remove('show');
                                }
                            });
                            
                            // 切换当前下拉菜单
                            item.classList.toggle('show');
                        });
                    }
                });
                
                // 点击外部关闭下拉菜单
                document.addEventListener('click', function() {
                    toggleItems.forEach(item => {
                        item.classList.remove('show');
                    });
                });
                
                // 浏览记录管理
                const browsingHistory = {
                    maxItems: 20,
                    storageKey: 'yinghe_browsing_history',
                    
                    getHistory: function() {
                        try {
                            return JSON.parse(localStorage.getItem(this.storageKey) || '[]');
                        } catch (e) {
                            return [];
                        }
                    },
                    
                    addItem: function(item) {
                        let history = this.getHistory();
                        
                        // 移除重复项
                        history = history.filter(h => h.url !== item.url);
                        
                        // 添加到开头
                        history.unshift({
                            ...item,
                            timestamp: Date.now()
                        });
                        
                        // 限制数量
                        if (history.length > this.maxItems) {
                            history = history.slice(0, this.maxItems);
                        }
                        
                        localStorage.setItem(this.storageKey, JSON.stringify(history));
                        this.updateDisplay();
                    },
                    
                    updateDisplay: function() {
                        const container = document.getElementById('browsing-history');
                        if (!container) return;
                        
                        const history = this.getHistory();
                        const prompt = container.previousElementSibling;
                        
                        if (history.length === 0) {
                            container.innerHTML = '';
                            if (prompt) prompt.style.display = 'block';
                            return;
                        }
                        
                        if (prompt) prompt.style.display = 'none';
                        
                        container.innerHTML = history.slice(0, 10).map(item => 
                            '<li><a href=\"' + this.escapeHtml(item.url) + '\" target=\"_blank\">' + 
                            this.escapeHtml(item.title) + '</a></li>'
                        ).join('');
                    },
                    
                    escapeHtml: function(text) {
                        const div = document.createElement('div');
                        div.textContent = text;
                        return div.innerHTML;
                    }
                };
                
                // 初始化浏览记录显示
                browsingHistory.updateDisplay();
                
                // 暴露到全局
                window.yingheBrowsingHistory = browsingHistory;
                
            })();
        ";
    }
}

/**
 * Helper 函数
 */
function yinghe_render_header_system($props = []) {
    echo YingheHeaderSystemComponent::render_static($props);
}

function yinghe_get_header_system($props = []) {
    return YingheHeaderSystemComponent::render_static($props);
}