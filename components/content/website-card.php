<?php
/**
 * 网站卡片组件
 * 
 * @package YingheTheme
 * @subpackage Components\Content
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 网站卡片组件类
 */
class YingheWebsiteCardComponent extends YingheAbstractComponent {
    
    /**
     * 获取默认属性
     * 
     * @return array
     */
    protected function get_default_props() {
        return [
            'site_id' => 0,
            'display_mode' => 'mini', // mini, default, max, featured
            'show_visit_count' => true,
            'enable_tracking' => true,
            'custom_class' => '',
            'show_description' => false,
            'show_tags' => false,
            'lazy_load' => true,
            'target_blank' => true,
        ];
    }
    
    /**
     * 初始化组件
     */
    protected function init() {
        // 验证 site_id
        if (empty($this->props['site_id']) || !get_post($this->props['site_id'])) {
            return;
        }
        
        // 获取网站数据
        $this->site_data = $this->get_site_data();
    }
    
    /**
     * 渲染组件
     */
    protected function render_component() {
        if (empty($this->site_data)) {
            return;
        }
        
        $site = $this->site_data;
        $display_mode = $this->props['display_mode'];
        $custom_class = $this->props['custom_class'];
        
        ?>
        <div class="url-body <?php echo esc_attr($display_mode . ' ' . $custom_class); ?>">
            <a class="card is-views site-<?php echo esc_attr($site['id']); ?>" 
               href="<?php echo esc_url($this->get_visit_url($site['id'])); ?>" 
               <?php echo $this->props['target_blank'] ? 'target="_blank" rel="external nofollow noopener"' : ''; ?>
               data-id="<?php echo esc_attr($site['id']); ?>"
               data-url="<?php echo esc_attr($site['url']); ?>"
               title="<?php echo esc_attr($site['description']); ?>"
               data-toggle="tooltip" 
               data-placement="top" 
               data-original-title="<?php echo esc_attr($site['tooltip'] ?: $site['description']); ?>">
               
                <div class="card-body">
                    <?php $this->render_card_content($site, $display_mode); ?>
                </div>
                
                <?php if ($this->props['show_visit_count'] && !empty($site['visit_count'])): ?>
                    <div class="visit-count">
                        <small><?php echo number_format_i18n($site['visit_count']); ?> 次访问</small>
                    </div>
                <?php endif; ?>
            </a>
            
            <?php if ($this->props['show_tags'] && !empty($site['tags'])): ?>
                <div class="site-tags">
                    <?php foreach (array_slice($site['tags'], 0, 3) as $tag): ?>
                        <span class="site-tag"><?php echo esc_html($tag->name); ?></span>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染卡片内容
     * 
     * @param array $site 网站数据
     * @param string $display_mode 显示模式
     */
    private function render_card_content($site, $display_mode) {
        switch ($display_mode) {
            case 'mini':
                $this->render_mini_card($site);
                break;
            case 'default':
                $this->render_default_card($site);
                break;
            case 'max':
                $this->render_max_card($site);
                break;
            case 'featured':
                $this->render_featured_card($site);
                break;
            default:
                $this->render_mini_card($site);
        }
    }
    
    /**
     * 渲染迷你卡片
     * 
     * @param array $site 网站数据
     */
    private function render_mini_card($site) {
        ?>
        <div class="url-content d-flex align-items-center">
            <div class="url-img mr-2 d-flex align-items-center justify-content-center">
                <?php $this->render_site_icon($site); ?>
            </div>
            <div class="url-info flex-fill">
                <div class="text-sm overflowClip_1">
                    <?php echo esc_html($site['title']); ?>
                </div>
                <?php if ($this->props['show_description'] && !empty($site['description'])): ?>
                    <div class="text-xs text-muted overflowClip_1">
                        <?php echo esc_html(wp_trim_words($site['description'], 10)); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染标准卡片
     * 
     * @param array $site 网站数据
     */
    private function render_default_card($site) {
        ?>
        <div class="url-content">
            <div class="url-img-wrapper text-center mb-2">
                <?php $this->render_site_icon($site, 'large'); ?>
            </div>
            <div class="url-info text-center">
                <div class="url-title text-sm font-weight-bold mb-1">
                    <?php echo esc_html($site['title']); ?>
                </div>
                <?php if ($this->props['show_description'] && !empty($site['description'])): ?>
                    <div class="url-description text-xs text-muted overflowClip_2">
                        <?php echo esc_html(wp_trim_words($site['description'], 15)); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染大卡片
     * 
     * @param array $site 网站数据
     */
    private function render_max_card($site) {
        ?>
        <div class="url-content">
            <div class="url-img-wrapper text-center mb-3">
                <?php $this->render_site_icon($site, 'large'); ?>
            </div>
            <div class="url-info text-center">
                <div class="url-title h6 font-weight-bold mb-2">
                    <?php echo esc_html($site['title']); ?>
                </div>
                <div class="url-description text-sm text-muted mb-2 overflowClip_3">
                    <?php echo esc_html(wp_trim_words($site['description'], 25)); ?>
                </div>
                <?php if (!empty($site['features'])): ?>
                    <div class="url-features">
                        <?php foreach (array_slice($site['features'], 0, 2) as $feature): ?>
                            <span class="badge badge-primary badge-sm"><?php echo esc_html($feature); ?></span>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染特色卡片
     * 
     * @param array $site 网站数据
     */
    private function render_featured_card($site) {
        ?>
        <div class="url-content featured-content">
            <div class="row no-gutters align-items-center">
                <div class="col-auto">
                    <div class="url-img-wrapper mr-3">
                        <?php $this->render_site_icon($site, 'large'); ?>
                    </div>
                </div>
                <div class="col">
                    <div class="url-info">
                        <div class="url-title h6 font-weight-bold mb-1">
                            <?php echo esc_html($site['title']); ?>
                        </div>
                        <div class="url-description text-sm text-muted mb-2 overflowClip_2">
                            <?php echo esc_html(wp_trim_words($site['description'], 20)); ?>
                        </div>
                        <?php if (!empty($site['features'])): ?>
                            <div class="url-features">
                                <?php foreach (array_slice($site['features'], 0, 3) as $feature): ?>
                                    <span class="badge badge-outline badge-sm"><?php echo esc_html($feature); ?></span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染网站图标
     * 
     * @param array $site 网站数据
     * @param string $size 图标尺寸 (small, default, large)
     */
    private function render_site_icon($site, $size = 'default') {
        $icon_url = $site['icon'];
        $alt_text = $site['title'];
        
        // 图标尺寸映射
        $size_map = [
            'small' => 24,
            'default' => 32,
            'large' => 48
        ];
        
        $icon_size = $size_map[$size] ?? $size_map['default'];
        
        // 如果没有自定义图标，使用默认图标
        if (empty($icon_url)) {
            $icon_url = get_template_directory_uri() . '/static/picture/favicon.png';
        }
        
        // 懒加载属性
        $lazy_attrs = '';
        if ($this->props['lazy_load']) {
            $lazy_attrs = 'class="lazy unfancybox" data-src="' . esc_url($icon_url) . '" src="' . get_template_directory_uri() . '/static/picture/favicon.png"';
        } else {
            $lazy_attrs = 'class="unfancybox" src="' . esc_url($icon_url) . '"';
        }
        
        ?>
        <img <?php echo $lazy_attrs; ?> 
             height="<?php echo esc_attr($icon_size); ?>" 
             width="<?php echo esc_attr($icon_size); ?>" 
             alt="<?php echo esc_attr($alt_text); ?>"
             loading="lazy">
        <?php
    }
    
    /**
     * 获取网站数据
     * 
     * @return array|null
     */
    private function get_site_data() {
        $site_id = $this->props['site_id'];
        $post = get_post($site_id);
        
        if (!$post || $post->post_type !== 'sites') {
            return null;
        }
        
        // 获取基础数据
        $data = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'description' => $post->post_excerpt ?: $post->post_content,
            'url' => get_post_meta($post->ID, 'site_url', true),
            'icon' => get_post_meta($post->ID, 'site_icon', true),
            'tooltip' => get_post_meta($post->ID, 'tooltip_text', true),
            'visit_count' => 0,
            'tags' => [],
            'features' => [],
        ];
        
        // 获取访问统计
        if ($this->props['show_visit_count']) {
            $data['visit_count'] = yinghe_get_site_visit_count($post->ID, 'total');
        }
        
        // 获取标签
        if ($this->props['show_tags']) {
            $tags = get_the_terms($post->ID, 'sitetag');
            if ($tags && !is_wp_error($tags)) {
                $data['tags'] = $tags;
            }
        }
        
        // 获取特色标签作为features
        if (!empty($data['tags'])) {
            $feature_tags = ['硬核推荐', '高画质', '4K画质', '无广告', '高清线路多'];
            foreach ($data['tags'] as $tag) {
                if (in_array($tag->name, $feature_tags)) {
                    $data['features'][] = $tag->name;
                }
            }
        }
        
        return $data;
    }
    
    /**
     * 获取访问URL
     * 
     * @param int $site_id 网站ID
     * @return string
     */
    private function get_visit_url($site_id) {
        if (!$this->props['enable_tracking']) {
            return get_post_meta($site_id, 'site_url', true);
        }
        
        // 使用跳转页面进行访问统计
        return home_url("/go/?url=" . base64_encode(get_post_meta($site_id, 'site_url', true)));
    }
    
    /**
     * 获取组件样式
     * 
     * @return string
     */
    protected function get_component_styles() {
        return "
            .yinghe-website-card {
                margin-bottom: 15px;
            }
            
            .url-body {
                border-radius: var(--yinghe-border-radius);
                overflow: hidden;
                transition: var(--yinghe-transition);
                background: var(--yinghe-bg-primary);
                border: 1px solid var(--yinghe-border-color);
            }
            
            .url-body:hover {
                transform: translateY(-2px);
                box-shadow: var(--yinghe-shadow-lg);
                border-color: var(--yinghe-primary);
            }
            
            .url-body .card {
                display: block;
                color: inherit;
                text-decoration: none;
                height: 100%;
            }
            
            .url-body .card:hover {
                color: inherit;
                text-decoration: none;
            }
            
            .card-body {
                padding: 15px;
                position: relative;
            }
            
            .url-content {
                position: relative;
            }
            
            .url-img {
                width: 32px;
                height: 32px;
                border-radius: 6px;
                overflow: hidden;
                background: var(--yinghe-bg-secondary);
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .url-img img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: var(--yinghe-transition-fast);
            }
            
            .url-img-wrapper {
                display: inline-block;
            }
            
            .url-img-wrapper img {
                border-radius: 8px;
                transition: var(--yinghe-transition-fast);
            }
            
            .url-body:hover .url-img img,
            .url-body:hover .url-img-wrapper img {
                transform: scale(1.05);
            }
            
            .url-info {
                flex: 1;
            }
            
            .url-title {
                color: var(--yinghe-text-primary);
                font-weight: 600;
                line-height: 1.3;
            }
            
            .url-description {
                color: var(--yinghe-text-muted);
                line-height: 1.4;
                margin-top: 4px;
            }
            
            .url-features {
                margin-top: 8px;
            }
            
            .badge {
                display: inline-block;
                padding: 2px 6px;
                font-size: 10px;
                font-weight: 500;
                line-height: 1;
                text-align: center;
                white-space: nowrap;
                vertical-align: baseline;
                border-radius: 10px;
                margin-right: 4px;
                margin-bottom: 2px;
            }
            
            .badge-primary {
                color: white;
                background-color: var(--yinghe-primary);
            }
            
            .badge-outline {
                color: var(--yinghe-primary);
                background-color: transparent;
                border: 1px solid var(--yinghe-primary);
            }
            
            .badge-sm {
                font-size: 9px;
                padding: 1px 5px;
            }
            
            .visit-count {
                position: absolute;
                bottom: 5px;
                right: 8px;
                font-size: 10px;
                color: var(--yinghe-text-muted);
                opacity: 0;
                transition: var(--yinghe-transition-fast);
            }
            
            .url-body:hover .visit-count {
                opacity: 1;
            }
            
            .site-tags {
                padding: 8px 15px;
                border-top: 1px solid var(--yinghe-border-color);
                background: var(--yinghe-bg-secondary);
            }
            
            .site-tag {
                display: inline-block;
                padding: 2px 6px;
                background: var(--yinghe-bg-tertiary);
                color: var(--yinghe-text-muted);
                font-size: 10px;
                border-radius: 8px;
                margin-right: 4px;
            }
            
            /* 显示模式样式 */
            .url-body.mini .card-body {
                padding: 12px;
            }
            
            .url-body.default .card-body {
                padding: 20px 15px;
                text-align: center;
            }
            
            .url-body.max .card-body {
                padding: 25px 20px;
                text-align: center;
            }
            
            .url-body.featured {
                background: linear-gradient(135deg, var(--yinghe-bg-primary) 0%, var(--yinghe-bg-secondary) 100%);
                border: 2px solid var(--yinghe-primary);
            }
            
            .url-body.featured .card-body {
                padding: 20px;
            }
            
            /* 文本截断样式 */
            .overflowClip_1 {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            .overflowClip_2 {
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            
            .overflowClip_3 {
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
            }
            
            /* 响应式调整 */
            @media (max-width: 768px) {
                .url-body .card-body {
                    padding: 12px;
                }
                
                .url-body.default .card-body,
                .url-body.max .card-body {
                    padding: 15px 12px;
                }
                
                .url-body.featured .card-body {
                    padding: 15px;
                }
                
                .url-title {
                    font-size: 14px;
                }
                
                .url-description {
                    font-size: 12px;
                }
            }
            
            /* 加载状态 */
            .url-body.loading {
                opacity: 0.6;
                pointer-events: none;
            }
            
            .url-body.loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 20px;
                height: 20px;
                margin: -10px 0 0 -10px;
                border: 2px solid var(--yinghe-border-color);
                border-top-color: var(--yinghe-primary);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        ";
    }
    
    /**
     * 获取组件脚本
     * 
     * @return string
     */
    protected function get_component_scripts() {
        return "
            // 网站卡片交互逻辑
            (function() {
                const cards = document.querySelectorAll('.yinghe-website-card .card');
                
                cards.forEach(card => {
                    // 点击统计
                    card.addEventListener('click', function(e) {
                        if (!this.dataset.id) return;
                        
                        const siteId = this.dataset.id;
                        const siteUrl = this.dataset.url;
                        const siteTitle = this.querySelector('.url-title, .text-sm')?.textContent || '';
                        
                        // 记录访问
                        if (typeof yingheConfig !== 'undefined' && yingheConfig.ajaxUrl) {
                            fetch(yingheConfig.ajaxUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: new URLSearchParams({
                                    action: 'yinghe_record_visit',
                                    nonce: yingheConfig.nonce,
                                    site_id: siteId,
                                    site_url: siteUrl,
                                    page_url: window.location.href,
                                    referer_url: document.referrer
                                })
                            });
                        }
                        
                        // 添加到浏览记录
                        if (window.yingheBrowsingHistory) {
                            window.yingheBrowsingHistory.addItem({
                                url: siteUrl,
                                title: siteTitle,
                                icon: this.querySelector('img')?.src || ''
                            });
                        }
                        
                        // 添加点击效果
                        this.closest('.url-body').classList.add('loading');
                        
                        setTimeout(() => {
                            this.closest('.url-body')?.classList.remove('loading');
                        }, 1000);
                    });
                    
                    // 长按复制链接 (移动端)
                    let pressTimer;
                    card.addEventListener('touchstart', function(e) {
                        const siteUrl = this.dataset.url;
                        if (!siteUrl) return;
                        
                        pressTimer = setTimeout(() => {
                            e.preventDefault();
                            
                            if (navigator.clipboard) {
                                navigator.clipboard.writeText(siteUrl).then(() => {
                                    showToast('链接已复制到剪贴板');
                                });
                            }
                        }, 800);
                    });
                    
                    card.addEventListener('touchend', function() {
                        clearTimeout(pressTimer);
                    });
                    
                    card.addEventListener('touchmove', function() {
                        clearTimeout(pressTimer);
                    });
                });
                
                // 提示消息函数
                function showToast(message) {
                    const toast = document.createElement('div');
                    toast.className = 'yinghe-toast';
                    toast.textContent = message;
                    toast.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: var(--yinghe-gray-800);
                        color: white;
                        padding: 10px 20px;
                        border-radius: 8px;
                        z-index: 10000;
                        font-size: 14px;
                        box-shadow: var(--yinghe-shadow-lg);
                    `;
                    
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.remove();
                    }, 2000);
                }
                
            })();
        ";
    }
}

/**
 * Helper 函数
 */
function yinghe_render_website_card($props = []) {
    echo YingheWebsiteCardComponent::render_static($props);
}

function yinghe_get_website_card($props = []) {
    return YingheWebsiteCardComponent::render_static($props);
}