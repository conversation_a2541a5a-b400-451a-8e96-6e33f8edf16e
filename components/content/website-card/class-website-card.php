<?php
/**
 * 网站卡片组件
 * 
 * 用于显示网站信息的可复用组件，支持多种布局模式和交互功能
 * 
 * @package Yinghe
 * @subpackage Components\Content
 * @since 1.0.0
 * 
 * @example
 * ```php
 * // 基础使用
 * echo YingheWebsiteCardComponent::renderStatic([
 *     'title' => '示例网站',
 *     'url' => 'https://example.com',
 *     'description' => '这是一个示例网站'
 * ]);
 * 
 * // 完整配置
 * $card = new YingheWebsiteCardComponent([
 *     'site_id' => 123,
 *     'title' => 'WordPress 官网',
 *     'description' => 'WordPress 是世界上最受欢迎的网站建设平台',
 *     'url' => 'https://wordpress.org',
 *     'thumbnail' => '/wp-content/uploads/2023/wordpress-thumb.jpg',
 *     'favicon' => '/wp-content/uploads/2023/wordpress-favicon.ico',
 *     'category' => 'CMS平台',
 *     'tags' => ['WordPress', 'CMS', '建站'],
 *     'visit_count' => 1250,
 *     'rating' => 4.8,
 *     'is_featured' => true,
 *     'is_new' => false,
 *     'layout' => 'card',
 *     'show_description' => true,
 *     'show_stats' => true,
 *     'target' => '_blank'
 * ]);
 * echo $card->render();
 * ```
 */

defined('ABSPATH') || exit; // 防止直接访问

/**
 * 网站卡片组件类
 */
class YingheWebsiteCardComponent extends YingheAbstractComponent implements YingheComponentInterface {
    
    use YingheCacheableComponent;
    
    /**
     * 缓存时长（30分钟）
     */
    protected int $cache_duration = 30 * MINUTE_IN_SECONDS;
    
    /**
     * 组件版本
     */
    const VERSION = '1.0.0';
    
    /**
     * 支持的布局类型
     */
    const LAYOUTS = ['card', 'list', 'grid', 'compact'];
    
    /**
     * 获取默认属性
     * 
     * @since 1.0.0
     * @return array<string, mixed> 默认属性配置
     */
    protected function getDefaultProps(): array {
        return [
            // 基础信息
            'site_id' => 0,
            'title' => '',
            'description' => '',
            'url' => '',
            'thumbnail' => '',
            'favicon' => '',
            
            // 分类和标签
            'category' => '',
            'tags' => [],
            
            // 统计信息
            'visit_count' => 0,
            'rating' => 0,
            'like_count' => 0,
            'comment_count' => 0,
            
            // 状态标识
            'is_featured' => false,
            'is_new' => false,
            'is_hot' => false,
            'is_recommended' => false,
            'status' => 'active', // active, inactive, pending
            
            // 显示配置
            'layout' => 'card',
            'size' => 'medium', // small, medium, large
            'show_description' => true,
            'show_thumbnail' => true,
            'show_favicon' => true,
            'show_category' => true,
            'show_tags' => true,
            'show_stats' => true,
            'show_rating' => true,
            'show_badges' => true,
            
            // 交互配置
            'target' => '_blank',
            'rel' => 'noopener noreferrer',
            'clickable' => true,
            'hoverable' => true,
            
            // 自定义配置
            'custom_class' => '',
            'custom_attributes' => [],
            'cache_enabled' => true
        ];
    }
    
    /**
     * 获取必需属性
     * 
     * @since 1.0.0
     * @return array<string> 必需属性列表
     */
    protected function getRequiredProps(): array {
        return ['title', 'url'];
    }
    
    /**
     * 获取属性清理规则
     * 
     * @since 1.0.0
     * @return array<string, callable> 清理规则映射
     */
    protected function getSanitizationRules(): array {
        return [
            'site_id' => 'absint',
            'title' => 'sanitize_text_field',
            'description' => 'wp_kses_post',
            'url' => 'esc_url_raw',
            'thumbnail' => 'esc_url_raw',
            'favicon' => 'esc_url_raw',
            'category' => 'sanitize_text_field',
            'tags' => [$this, 'sanitizeTags'],
            'visit_count' => 'absint',
            'rating' => [$this, 'sanitizeRating'],
            'layout' => [$this, 'sanitizeLayout'],
            'size' => [$this, 'sanitizeSize'],
            'target' => 'sanitize_text_field',
            'rel' => 'sanitize_text_field'
        ];
    }
    
    /**
     * 组件初始化
     * 
     * @since 1.0.0
     */
    protected function init(): void {
        // 设置组件特定的CSS类
        $this->addClass('website-card');
        $this->addClass("layout-{$this->props['layout']}");
        $this->addClass("size-{$this->props['size']}");
        
        // 添加状态类
        if ($this->props['is_featured']) {
            $this->addClass('featured');
        }
        
        if ($this->props['is_new']) {
            $this->addClass('new');
        }
        
        if ($this->props['is_hot']) {
            $this->addClass('hot');
        }
        
        // 添加自定义类
        if (!empty($this->props['custom_class'])) {
            $this->addClass($this->props['custom_class']);
        }
        
        // 设置数据属性
        $this->addDataAttribute('site-id', (string)$this->props['site_id']);
        $this->addDataAttribute('layout', $this->props['layout']);
        $this->addDataAttribute('url', $this->props['url']);
        
        // 添加自定义属性
        foreach ($this->props['custom_attributes'] as $key => $value) {
            $this->addDataAttribute($key, $value);
        }
        
        // 注册交互事件
        if ($this->props['clickable']) {
            $this->addDataAttribute('clickable', 'true');
        }
        
        // 访问统计
        if ($this->props['site_id'] > 0) {
            $this->addDataAttribute('track-visit', 'true');
        }
    }
    
    /**
     * 渲染组件主体
     * 
     * @since 1.0.0
     */
    protected function renderComponent(): void {
        $layout = $this->props['layout'];
        
        ?>
        <article<?php echo $this->getAttributes(); ?>>
            <?php
            switch ($layout) {
                case 'list':
                    $this->renderListLayout();
                    break;
                case 'grid':
                    $this->renderGridLayout();
                    break;
                case 'compact':
                    $this->renderCompactLayout();
                    break;
                default:
                    $this->renderCardLayout();
                    break;
            }
            ?>
        </article>
        <?php
    }
    
    /**
     * 渲染卡片布局
     * 
     * @since 1.0.0
     */
    private function renderCardLayout(): void {
        ?>
        <div class="card-inner">
            <?php $this->renderThumbnail(); ?>
            
            <div class="card-content">
                <?php $this->renderHeader(); ?>
                <?php $this->renderDescription(); ?>
                <?php $this->renderMeta(); ?>
                <?php $this->renderFooter(); ?>
            </div>
            
            <?php $this->renderBadges(); ?>
        </div>
        <?php
    }
    
    /**
     * 渲染列表布局
     * 
     * @since 1.0.0
     */
    private function renderListLayout(): void {
        ?>
        <div class="list-inner">
            <div class="list-media">
                <?php $this->renderFavicon(); ?>
            </div>
            
            <div class="list-content">
                <?php $this->renderTitle(); ?>
                <?php if ($this->props['show_description']): ?>
                    <?php $this->renderDescription(); ?>
                <?php endif; ?>
                <?php $this->renderCategory(); ?>
            </div>
            
            <div class="list-actions">
                <?php $this->renderStats(); ?>
                <?php $this->renderBadges(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染网格布局
     * 
     * @since 1.0.0
     */
    private function renderGridLayout(): void {
        ?>
        <div class="grid-inner">
            <?php $this->renderThumbnail(); ?>
            
            <div class="grid-overlay">
                <div class="grid-content">
                    <?php $this->renderTitle(); ?>
                    <?php $this->renderCategory(); ?>
                </div>
                
                <div class="grid-actions">
                    <?php $this->renderStats(); ?>
                </div>
            </div>
            
            <?php $this->renderBadges(); ?>
        </div>
        <?php
    }
    
    /**
     * 渲染紧凑布局
     * 
     * @since 1.0.0
     */
    private function renderCompactLayout(): void {
        ?>
        <div class="compact-inner">
            <?php $this->renderFavicon(); ?>
            
            <div class="compact-content">
                <?php $this->renderTitle(); ?>
                <?php if ($this->props['show_stats']): ?>
                    <span class="compact-stats">
                        <?php printf(__('%d 次访问', 'yinghe'), $this->props['visit_count']); ?>
                    </span>
                <?php endif; ?>
            </div>
            
            <?php if ($this->props['is_featured']): ?>
                <span class="compact-badge"><?php _e('推荐', 'yinghe'); ?></span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染缩略图
     * 
     * @since 1.0.0
     */
    private function renderThumbnail(): void {
        if (!$this->props['show_thumbnail'] || empty($this->props['thumbnail'])) {
            return;
        }
        
        $thumbnail_url = $this->props['thumbnail'];
        $title = $this->props['title'];
        $url = $this->props['url'];
        
        ?>
        <div class="card-thumbnail">
            <?php if ($this->props['clickable']): ?>
                <a href="<?php echo esc_url($url); ?>" 
                   target="<?php echo esc_attr($this->props['target']); ?>"
                   rel="<?php echo esc_attr($this->props['rel']); ?>"
                   class="thumbnail-link"
                   title="<?php echo esc_attr($title); ?>">
            <?php endif; ?>
            
            <img src="<?php echo esc_url($thumbnail_url); ?>" 
                 alt="<?php echo esc_attr($title); ?>"
                 class="thumbnail-image"
                 loading="lazy"
                 decoding="async">
            
            <?php if ($this->props['clickable']): ?>
                </a>
            <?php endif; ?>
            
            <?php $this->renderThumbnailOverlay(); ?>
        </div>
        <?php
    }
    
    /**
     * 渲染缩略图遮罩层
     * 
     * @since 1.0.0
     */
    private function renderThumbnailOverlay(): void {
        if (!$this->props['hoverable']) {
            return;
        }
        
        ?>
        <div class="thumbnail-overlay">
            <div class="overlay-content">
                <i class="overlay-icon" aria-hidden="true">🔗</i>
                <span class="overlay-text"><?php _e('访问网站', 'yinghe'); ?></span>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染头部区域
     * 
     * @since 1.0.0
     */
    private function renderHeader(): void {
        ?>
        <div class="card-header">
            <?php $this->renderTitle(); ?>
            <?php $this->renderRating(); ?>
        </div>
        <?php
    }
    
    /**
     * 渲染标题
     * 
     * @since 1.0.0
     */
    private function renderTitle(): void {
        $title = $this->props['title'];
        $url = $this->props['url'];
        
        ?>
        <h3 class="card-title">
            <?php if ($this->props['clickable']): ?>
                <a href="<?php echo esc_url($url); ?>" 
                   target="<?php echo esc_attr($this->props['target']); ?>"
                   rel="<?php echo esc_attr($this->props['rel']); ?>"
                   class="title-link"
                   title="<?php echo esc_attr($title); ?>">
                    <?php echo esc_html($title); ?>
                </a>
            <?php else: ?>
                <?php echo esc_html($title); ?>
            <?php endif; ?>
        </h3>
        <?php
    }
    
    /**
     * 渲染网站图标
     * 
     * @since 1.0.0
     */
    private function renderFavicon(): void {
        if (!$this->props['show_favicon']) {
            return;
        }
        
        $favicon_url = $this->props['favicon'];
        $title = $this->props['title'];
        
        // 如果没有提供 favicon，尝试生成默认的
        if (empty($favicon_url)) {
            $favicon_url = $this->generateFaviconUrl();
        }
        
        ?>
        <div class="card-favicon">
            <img src="<?php echo esc_url($favicon_url); ?>" 
                 alt="<?php echo esc_attr($title); ?>" 
                 class="favicon-image"
                 width="16" 
                 height="16"
                 loading="lazy"
                 onerror="this.style.display='none';">
        </div>
        <?php
    }
    
    /**
     * 渲染描述
     * 
     * @since 1.0.0
     */
    private function renderDescription(): void {
        if (!$this->props['show_description'] || empty($this->props['description'])) {
            return;
        }
        
        $description = $this->props['description'];
        $max_length = $this->getDescriptionMaxLength();
        
        if (mb_strlen($description) > $max_length) {
            $description = mb_substr($description, 0, $max_length) . '...';
        }
        
        ?>
        <div class="card-description">
            <p><?php echo wp_kses_post($description); ?></p>
        </div>
        <?php
    }
    
    /**
     * 渲染评分
     * 
     * @since 1.0.0
     */
    private function renderRating(): void {
        if (!$this->props['show_rating'] || $this->props['rating'] <= 0) {
            return;
        }
        
        $rating = $this->props['rating'];
        $full_stars = floor($rating);
        $has_half_star = ($rating - $full_stars) >= 0.5;
        $empty_stars = 5 - $full_stars - ($has_half_star ? 1 : 0);
        
        ?>
        <div class="card-rating" title="<?php printf(__('评分: %.1f/5', 'yinghe'), $rating); ?>">
            <div class="rating-stars">
                <?php for ($i = 0; $i < $full_stars; $i++): ?>
                    <span class="star star-full" aria-hidden="true">★</span>
                <?php endfor; ?>
                
                <?php if ($has_half_star): ?>
                    <span class="star star-half" aria-hidden="true">☆</span>
                <?php endif; ?>
                
                <?php for ($i = 0; $i < $empty_stars; $i++): ?>
                    <span class="star star-empty" aria-hidden="true">☆</span>
                <?php endfor; ?>
            </div>
            
            <span class="rating-text"><?php echo number_format($rating, 1); ?></span>
        </div>
        <?php
    }
    
    /**
     * 渲染分类
     * 
     * @since 1.0.0
     */
    private function renderCategory(): void {
        if (!$this->props['show_category'] || empty($this->props['category'])) {
            return;
        }
        
        ?>
        <div class="card-category">
            <span class="category-label"><?php echo esc_html($this->props['category']); ?></span>
        </div>
        <?php
    }
    
    /**
     * 渲染标签
     * 
     * @since 1.0.0
     */
    private function renderTags(): void {
        if (!$this->props['show_tags'] || empty($this->props['tags'])) {
            return;
        }
        
        $tags = $this->props['tags'];
        $max_tags = 3; // 最多显示3个标签
        
        ?>
        <div class="card-tags">
            <?php foreach (array_slice($tags, 0, $max_tags) as $tag): ?>
                <span class="tag-item"><?php echo esc_html($tag); ?></span>
            <?php endforeach; ?>
            
            <?php if (count($tags) > $max_tags): ?>
                <span class="tag-more" title="<?php echo esc_attr(implode(', ', array_slice($tags, $max_tags))); ?>">
                    +<?php echo count($tags) - $max_tags; ?>
                </span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染元信息
     * 
     * @since 1.0.0
     */
    private function renderMeta(): void {
        ?>
        <div class="card-meta">
            <?php $this->renderCategory(); ?>
            <?php $this->renderTags(); ?>
        </div>
        <?php
    }
    
    /**
     * 渲染统计信息
     * 
     * @since 1.0.0
     */
    private function renderStats(): void {
        if (!$this->props['show_stats']) {
            return;
        }
        
        ?>
        <div class="card-stats">
            <?php if ($this->props['visit_count'] > 0): ?>
                <span class="stat-item visit-count" title="<?php _e('访问次数', 'yinghe'); ?>">
                    <i class="stat-icon" aria-hidden="true">👁</i>
                    <span class="stat-value"><?php echo $this->formatNumber($this->props['visit_count']); ?></span>
                </span>
            <?php endif; ?>
            
            <?php if ($this->props['like_count'] > 0): ?>
                <span class="stat-item like-count" title="<?php _e('点赞数', 'yinghe'); ?>">
                    <i class="stat-icon" aria-hidden="true">👍</i>
                    <span class="stat-value"><?php echo $this->formatNumber($this->props['like_count']); ?></span>
                </span>
            <?php endif; ?>
            
            <?php if ($this->props['comment_count'] > 0): ?>
                <span class="stat-item comment-count" title="<?php _e('评论数', 'yinghe'); ?>">
                    <i class="stat-icon" aria-hidden="true">💬</i>
                    <span class="stat-value"><?php echo $this->formatNumber($this->props['comment_count']); ?></span>
                </span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染徽章
     * 
     * @since 1.0.0
     */
    private function renderBadges(): void {
        if (!$this->props['show_badges']) {
            return;
        }
        
        $badges = [];
        
        if ($this->props['is_featured']) {
            $badges[] = ['type' => 'featured', 'text' => __('推荐', 'yinghe'), 'class' => 'badge-featured'];
        }
        
        if ($this->props['is_new']) {
            $badges[] = ['type' => 'new', 'text' => __('新站', 'yinghe'), 'class' => 'badge-new'];
        }
        
        if ($this->props['is_hot']) {
            $badges[] = ['type' => 'hot', 'text' => __('热门', 'yinghe'), 'class' => 'badge-hot'];
        }
        
        if ($this->props['is_recommended']) {
            $badges[] = ['type' => 'recommended', 'text' => __('官推', 'yinghe'), 'class' => 'badge-recommended'];
        }
        
        if (empty($badges)) {
            return;
        }
        
        ?>
        <div class="card-badges">
            <?php foreach ($badges as $badge): ?>
                <span class="badge <?php echo esc_attr($badge['class']); ?>" 
                      data-badge-type="<?php echo esc_attr($badge['type']); ?>">
                    <?php echo esc_html($badge['text']); ?>
                </span>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染底部区域
     * 
     * @since 1.0.0
     */
    private function renderFooter(): void {
        ?>
        <div class="card-footer">
            <?php $this->renderStats(); ?>
            
            <div class="card-actions">
                <?php $this->renderActionButtons(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染操作按钮
     * 
     * @since 1.0.0
     */
    private function renderActionButtons(): void {
        $url = $this->props['url'];
        $title = $this->props['title'];
        
        ?>
        <div class="action-buttons">
            <?php if ($this->props['clickable']): ?>
                <a href="<?php echo esc_url($url); ?>" 
                   target="<?php echo esc_attr($this->props['target']); ?>"
                   rel="<?php echo esc_attr($this->props['rel']); ?>"
                   class="action-button action-visit"
                   title="<?php printf(__('访问 %s', 'yinghe'), esc_attr($title)); ?>">
                    <i class="button-icon" aria-hidden="true">🔗</i>
                    <span class="button-text"><?php _e('访问', 'yinghe'); ?></span>
                </a>
            <?php endif; ?>
            
            <button type="button" 
                    class="action-button action-share" 
                    data-share-url="<?php echo esc_attr($url); ?>"
                    data-share-title="<?php echo esc_attr($title); ?>"
                    title="<?php _e('分享网站', 'yinghe'); ?>">
                <i class="button-icon" aria-hidden="true">📤</i>
                <span class="button-text"><?php _e('分享', 'yinghe'); ?></span>
            </button>
            
            <button type="button" 
                    class="action-button action-like" 
                    data-site-id="<?php echo esc_attr($this->props['site_id']); ?>"
                    title="<?php _e('点赞网站', 'yinghe'); ?>">
                <i class="button-icon" aria-hidden="true">👍</i>
                <span class="button-text"><?php _e('点赞', 'yinghe'); ?></span>
            </button>
        </div>
        <?php
    }
    
    /**
     * 获取组件样式
     * 
     * @since 1.0.0
     * @return string CSS 样式代码
     */
    protected function getComponentStyles(): string {
        return "
            .yinghe-website-card {
                position: relative;
                background: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                overflow: hidden;
            }
            
            .yinghe-website-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            }
            
            .yinghe-website-card.featured {
                border: 2px solid #f59e0b;
            }
            
            .yinghe-website-card.new::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 0 40px 40px 0;
                border-color: transparent #10b981 transparent transparent;
                z-index: 10;
            }
            
            .card-inner {
                display: flex;
                flex-direction: column;
                height: 100%;
            }
            
            .card-thumbnail {
                position: relative;
                width: 100%;
                height: 200px;
                overflow: hidden;
            }
            
            .thumbnail-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }
            
            .card-thumbnail:hover .thumbnail-image {
                transform: scale(1.05);
            }
            
            .thumbnail-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .card-thumbnail:hover .thumbnail-overlay {
                opacity: 1;
            }
            
            .overlay-content {
                text-align: center;
                color: white;
            }
            
            .overlay-icon {
                display: block;
                font-size: 24px;
                margin-bottom: 8px;
            }
            
            .card-content {
                padding: 16px;
                flex: 1;
                display: flex;
                flex-direction: column;
            }
            
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;
            }
            
            .card-title {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                line-height: 1.3;
                flex: 1;
            }
            
            .title-link {
                color: #1f2937;
                text-decoration: none;
                transition: color 0.2s ease;
            }
            
            .title-link:hover {
                color: #3b82f6;
            }
            
            .card-description {
                margin-bottom: 12px;
                flex: 1;
            }
            
            .card-description p {
                margin: 0;
                color: #6b7280;
                font-size: 14px;
                line-height: 1.5;
            }
            
            .card-rating {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
            }
            
            .rating-stars {
                color: #fbbf24;
            }
            
            .rating-text {
                color: #6b7280;
                font-weight: 500;
            }
            
            .card-meta {
                margin-bottom: 12px;
            }
            
            .card-category {
                margin-bottom: 8px;
            }
            
            .category-label {
                display: inline-block;
                padding: 4px 8px;
                background: #f3f4f6;
                color: #374151;
                font-size: 12px;
                border-radius: 4px;
            }
            
            .card-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
            }
            
            .tag-item {
                display: inline-block;
                padding: 2px 6px;
                background: #e5e7eb;
                color: #4b5563;
                font-size: 11px;
                border-radius: 3px;
            }
            
            .tag-more {
                display: inline-block;
                padding: 2px 6px;
                background: #d1d5db;
                color: #6b7280;
                font-size: 11px;
                border-radius: 3px;
                cursor: help;
            }
            
            .card-footer {
                margin-top: auto;
                padding-top: 12px;
                border-top: 1px solid #f3f4f6;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .card-stats {
                display: flex;
                gap: 12px;
            }
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #6b7280;
            }
            
            .stat-icon {
                font-size: 14px;
            }
            
            .action-buttons {
                display: flex;
                gap: 8px;
            }
            
            .action-button {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 6px 12px;
                background: #f9fafb;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                color: #374151;
                text-decoration: none;
                font-size: 12px;
                transition: all 0.2s ease;
                cursor: pointer;
            }
            
            .action-button:hover {
                background: #f3f4f6;
                border-color: #d1d5db;
                transform: translateY(-1px);
            }
            
            .action-visit {
                background: #3b82f6;
                border-color: #3b82f6;
                color: white;
            }
            
            .action-visit:hover {
                background: #2563eb;
                border-color: #2563eb;
            }
            
            .card-badges {
                position: absolute;
                top: 12px;
                left: 12px;
                display: flex;
                flex-direction: column;
                gap: 4px;
                z-index: 5;
            }
            
            .badge {
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                border-radius: 4px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            .badge-featured {
                background: #f59e0b;
                color: white;
            }
            
            .badge-new {
                background: #10b981;
                color: white;
            }
            
            .badge-hot {
                background: #ef4444;
                color: white;
            }
            
            .badge-recommended {
                background: #8b5cf6;
                color: white;
            }
            
            /* 列表布局样式 */
            .yinghe-website-card.layout-list {
                border-radius: 6px;
            }
            
            .list-inner {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
            }
            
            .list-media {
                flex-shrink: 0;
            }
            
            .list-content {
                flex: 1;
                min-width: 0;
            }
            
            .list-actions {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            /* 紧凑布局样式 */
            .yinghe-website-card.layout-compact {
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
            
            .compact-inner {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
            }
            
            .compact-content {
                flex: 1;
                min-width: 0;
            }
            
            .compact-content .card-title {
                font-size: 14px;
                margin-bottom: 2px;
            }
            
            .compact-stats {
                font-size: 11px;
                color: #9ca3af;
            }
            
            .compact-badge {
                font-size: 10px;
                padding: 2px 6px;
                background: #3b82f6;
                color: white;
                border-radius: 3px;
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .card-content {
                    padding: 12px;
                }
                
                .card-title {
                    font-size: 16px;
                }
                
                .action-buttons {
                    flex-direction: column;
                }
                
                .action-button {
                    justify-content: center;
                }
            }
        ";
    }
    
    /**
     * 获取组件脚本
     * 
     * @since 1.0.0
     * @return string JavaScript 代码
     */
    protected function getComponentScripts(): string {
        return "
            // 网站卡片组件交互脚本
            (function() {
                const cards = document.querySelectorAll('.yinghe-website-card');
                
                cards.forEach(card => {
                    const cardId = card.id;
                    const siteId = card.dataset.siteId;
                    const trackVisit = card.dataset.trackVisit === 'true';
                    
                    // 点击事件处理
                    if (card.dataset.clickable === 'true') {
                        card.addEventListener('click', function(e) {
                            // 如果点击的是按钮，不触发卡片点击
                            if (e.target.closest('.action-button')) {
                                return;
                            }
                            
                            const url = card.dataset.url;
                            if (url) {
                                // 发送点击事件
                                if (typeof yingheEvents !== 'undefined') {
                                    yingheEvents.emit('card:click', {
                                        siteId: siteId,
                                        url: url,
                                        title: card.querySelector('.card-title')?.textContent?.trim()
                                    });
                                }
                                
                                // 记录访问
                                if (trackVisit) {
                                    recordVisit(siteId);
                                }
                                
                                // 打开链接
                                window.open(url, '_blank', 'noopener,noreferrer');
                            }
                        });
                    }
                    
                    // 分享按钮事件
                    const shareButton = card.querySelector('.action-share');
                    if (shareButton) {
                        shareButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            const url = this.dataset.shareUrl;
                            const title = this.dataset.shareTitle;
                            
                            if (navigator.share) {
                                navigator.share({
                                    title: title,
                                    url: url
                                }).catch(console.error);
                            } else {
                                // 备用分享方法
                                copyToClipboard(url);
                                showToast('链接已复制到剪贴板');
                            }
                        });
                    }
                    
                    // 点赞按钮事件
                    const likeButton = card.querySelector('.action-like');
                    if (likeButton) {
                        likeButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            const siteId = this.dataset.siteId;
                            if (siteId) {
                                toggleLike(siteId, this);
                            }
                        });
                    }
                });
                
                // 记录访问统计
                function recordVisit(siteId) {
                    if (!siteId) return;
                    
                    fetch(yingheConfig.ajaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'yinghe_record_visit',
                            site_id: siteId,
                            nonce: yingheConfig.nonce
                        })
                    }).catch(console.error);
                }
                
                // 切换点赞状态
                function toggleLike(siteId, button) {
                    const isLiked = button.classList.contains('liked');
                    
                    fetch(yingheConfig.ajaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'yinghe_toggle_like',
                            site_id: siteId,
                            action_type: isLiked ? 'unlike' : 'like',
                            nonce: yingheConfig.nonce
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            button.classList.toggle('liked');
                            
                            const icon = button.querySelector('.button-icon');
                            const text = button.querySelector('.button-text');
                            
                            if (button.classList.contains('liked')) {
                                icon.textContent = '❤️';
                                text.textContent = '已赞';
                            } else {
                                icon.textContent = '👍';
                                text.textContent = '点赞';
                            }
                            
                            // 更新点赞数
                            const likeCountElement = button.closest('.yinghe-website-card').querySelector('.like-count .stat-value');
                            if (likeCountElement) {
                                likeCountElement.textContent = data.data.like_count || 0;
                            }
                        }
                    })
                    .catch(console.error);
                }
                
                // 复制到剪贴板
                function copyToClipboard(text) {
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(text);
                    } else {
                        // 备用方法
                        const textarea = document.createElement('textarea');
                        textarea.value = text;
                        document.body.appendChild(textarea);
                        textarea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textarea);
                    }
                }
                
                // 显示提示消息
                function showToast(message) {
                    // 创建简单的 toast 提示
                    const toast = document.createElement('div');
                    toast.className = 'yinghe-toast';
                    toast.textContent = message;
                    toast.style.cssText = 'position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#333;color:white;padding:8px 16px;border-radius:4px;z-index:9999;';
                    
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 3000);
                }
            })();
        ";
    }
    
    /**
     * 清理标签数组
     * 
     * @since 1.0.0
     * @param mixed $tags 标签数据
     * @return array 清理后的标签数组
     */
    private function sanitizeTags($tags): array {
        if (!is_array($tags)) {
            if (is_string($tags)) {
                $tags = explode(',', $tags);
            } else {
                return [];
            }
        }
        
        return array_filter(array_map('sanitize_text_field', $tags));
    }
    
    /**
     * 清理评分数据
     * 
     * @since 1.0.0
     * @param mixed $rating 评分数据
     * @return float 清理后的评分
     */
    private function sanitizeRating($rating): float {
        $rating = (float) $rating;
        return max(0, min(5, $rating));
    }
    
    /**
     * 清理布局类型
     * 
     * @since 1.0.0
     * @param string $layout 布局类型
     * @return string 清理后的布局类型
     */
    private function sanitizeLayout(string $layout): string {
        return in_array($layout, self::LAYOUTS) ? $layout : 'card';
    }
    
    /**
     * 清理尺寸类型
     * 
     * @since 1.0.0
     * @param string $size 尺寸类型
     * @return string 清理后的尺寸类型
     */
    private function sanitizeSize(string $size): string {
        $allowed_sizes = ['small', 'medium', 'large'];
        return in_array($size, $allowed_sizes) ? $size : 'medium';
    }
    
    /**
     * 生成网站图标URL
     * 
     * @since 1.0.0
     * @return string 图标URL
     */
    private function generateFaviconUrl(): string {
        $url = $this->props['url'];
        if (empty($url)) {
            return '';
        }
        
        $domain = parse_url($url, PHP_URL_HOST);
        if (!$domain) {
            return '';
        }
        
        // 使用 Google favicon 服务
        return "https://www.google.com/s2/favicons?domain={$domain}&sz=16";
    }
    
    /**
     * 获取描述最大长度
     * 
     * @since 1.0.0
     * @return int 最大长度
     */
    private function getDescriptionMaxLength(): int {
        switch ($this->props['layout']) {
            case 'list':
                return 80;
            case 'compact':
                return 0; // 紧凑模式不显示描述
            case 'grid':
                return 60;
            default:
                return 120;
        }
    }
    
    /**
     * 格式化数字显示
     * 
     * @since 1.0.0
     * @param int $number 数字
     * @return string 格式化后的数字
     */
    private function formatNumber(int $number): string {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }
        
        return (string) $number;
    }
    
    /**
     * 获取缓存键
     * 
     * @since 1.0.0
     * @return string 缓存键
     */
    protected function getCacheKey(): string {
        $key_data = [
            'site_id' => $this->props['site_id'],
            'layout' => $this->props['layout'],
            'size' => $this->props['size'],
            'version' => self::VERSION
        ];
        
        return 'yinghe_website_card_' . md5(serialize($key_data));
    }
}

/**
 * 助手函数：渲染网站卡片组件
 * 
 * @since 1.0.0
 * @param array $props 组件属性
 */
function yinghe_render_website_card(array $props = []): void {
    echo YingheWebsiteCardComponent::renderStatic($props);
}

/**
 * 助手函数：获取网站卡片组件HTML
 * 
 * @since 1.0.0
 * @param array $props 组件属性
 * @return string 组件HTML
 */
function yinghe_get_website_card(array $props = []): string {
    return YingheWebsiteCardComponent::renderStatic($props);
}

/**
 * 助手函数：从文章数据创建网站卡片
 * 
 * @since 1.0.0
 * @param WP_Post|int $post 文章对象或ID
 * @param array $override_props 覆盖属性
 * @return string 组件HTML
 */
function yinghe_website_card_from_post($post, array $override_props = []): string {
    $post = get_post($post);
    if (!$post) {
        return '';
    }
    
    $props = array_merge([
        'site_id' => $post->ID,
        'title' => $post->post_title,
        'description' => $post->post_excerpt ?: wp_trim_words($post->post_content, 20),
        'url' => get_post_meta($post->ID, 'site_url', true),
        'thumbnail' => get_the_post_thumbnail_url($post->ID, 'medium'),
        'favicon' => get_post_meta($post->ID, 'site_favicon', true),
        'visit_count' => (int) get_post_meta($post->ID, 'visit_count', true),
        'rating' => (float) get_post_meta($post->ID, 'site_rating', true),
        'like_count' => (int) get_post_meta($post->ID, 'like_count', true),
        'is_featured' => (bool) get_post_meta($post->ID, 'is_featured', true),
        'is_new' => strtotime($post->post_date) > strtotime('-7 days'),
        'category' => yinghe_get_post_primary_category($post->ID),
        'tags' => yinghe_get_post_tags($post->ID)
    ], $override_props);
    
    return YingheWebsiteCardComponent::renderStatic($props);
}

/**
 * 获取文章主要分类
 * 
 * @since 1.0.0
 * @param int $post_id 文章ID
 * @return string 主要分类名称
 */
function yinghe_get_post_primary_category(int $post_id): string {
    $categories = get_the_terms($post_id, 'site_category');
    if (!$categories || is_wp_error($categories)) {
        return '';
    }
    
    return $categories[0]->name;
}

/**
 * 获取文章标签
 * 
 * @since 1.0.0
 * @param int $post_id 文章ID
 * @return array 标签数组
 */
function yinghe_get_post_tags(int $post_id): array {
    $tags = get_the_terms($post_id, 'site_tag');
    if (!$tags || is_wp_error($tags)) {
        return [];
    }
    
    return wp_list_pluck($tags, 'name');
}