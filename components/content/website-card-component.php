<?php
/**
 * 网站卡片组件
 * 
 * 用于显示网站信息的卡片组件，支持多种布局和交互功能
 * 
 * @package YingheTheme
 * @subpackage Components\Content
 * @since 1.0.0
 * 
 * @example
 * ```php
 * // 基本使用
 * echo YingheWebsiteCardComponent::render_static([
 *     'title' => '网站标题',
 *     'url' => 'https://example.com',
 *     'description' => '网站描述'
 * ]);
 * 
 * // 高级使用
 * $card = new YingheWebsiteCardComponent([
 *     'title' => 'WordPress 官网',
 *     'url' => 'https://wordpress.org',
 *     'description' => 'WordPress 是世界上最受欢迎的网站建设工具',
 *     'thumbnail' => '/wp-content/uploads/wordpress-thumb.jpg',
 *     'category' => 'CMS',
 *     'layout' => 'featured',
 *     'show_stats' => true,
 *     'visit_count' => 12580
 * ]);
 * echo $card->render();
 * ```
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class YingheWebsiteCardComponent extends YingheAbstractComponent {
    
    /**
     * 获取默认属性
     * 
     * @return array 默认属性数组
     */
    protected function get_default_props() {
        return [
            // 必需属性
            'title' => '',
            'url' => '',
            
            // 可选内容属性
            'description' => '',
            'thumbnail' => '',
            'favicon' => '',
            'category' => '',
            'tags' => [],
            
            // 统计信息
            'visit_count' => 0,
            'rating' => 0,
            'update_time' => '',
            
            // 显示选项
            'layout' => 'default',          // default, featured, compact, list
            'show_description' => true,
            'show_stats' => true,
            'show_category' => true,
            'show_thumbnail' => true,
            'show_favicon' => true,
            
            // 交互属性
            'target' => '_blank',
            'clickable' => true,
            'track_visit' => true,
            
            // 状态标记
            'is_featured' => false,
            'is_new' => false,
            'is_hot' => false,
            'is_offline' => false,
            
            // 样式属性
            'border_color' => '',
            'background_color' => '',
            'text_color' => '',
            
            // 自定义属性
            'custom_data' => [],
        ];
    }
    
    /**
     * 组件初始化
     */
    protected function init() {
        // 验证必需属性
        if (empty($this->props['title'])) {
            throw new InvalidArgumentException('网站卡片组件缺少必需属性: title');
        }
        
        if (empty($this->props['url'])) {
            throw new InvalidArgumentException('网站卡片组件缺少必需属性: url');
        }
        
        // 清理和验证URL
        $this->props['url'] = esc_url_raw($this->props['url']);
        if (!filter_var($this->props['url'], FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException('无效的URL地址: ' . $this->props['url']);
        }
        
        // 清理其他属性
        $this->props['title'] = sanitize_text_field($this->props['title']);
        $this->props['description'] = wp_kses_post($this->props['description']);
        $this->props['category'] = sanitize_text_field($this->props['category']);
        
        // 处理标签
        if (is_string($this->props['tags'])) {
            $this->props['tags'] = array_map('trim', explode(',', $this->props['tags']));
        }
        $this->props['tags'] = array_map('sanitize_text_field', (array) $this->props['tags']);
        
        // 数值验证
        $this->props['visit_count'] = max(0, (int) $this->props['visit_count']);
        $this->props['rating'] = max(0, min(5, (float) $this->props['rating']));
        
        // 如果启用访问跟踪，注册相关功能
        if ($this->props['track_visit']) {
            $this->register_visit_tracking();
        }
    }
    
    /**
     * 注册访问跟踪功能
     */
    private function register_visit_tracking() {
        // 添加数据属性用于JavaScript跟踪
        if (!isset($this->props['data'])) {
            $this->props['data'] = [];
        }
        
        $this->props['data']['track-visit'] = 'true';
        $this->props['data']['site-url'] = $this->props['url'];
        $this->props['data']['site-title'] = $this->props['title'];
    }
    
    /**
     * 渲染组件主体
     */
    protected function render_component() {
        $layout_class = "layout-{$this->props['layout']}";
        $state_classes = $this->get_state_classes();
        ?>
        <div<?php echo $this->get_attributes(); ?> class="<?php echo esc_attr($layout_class . ' ' . $state_classes); ?>">
            <?php $this->render_card_content(); ?>
        </div>
        <?php
    }
    
    /**
     * 获取状态CSS类名
     */
    private function get_state_classes() {
        $classes = [];
        
        if ($this->props['is_featured']) $classes[] = 'is-featured';
        if ($this->props['is_new']) $classes[] = 'is-new';
        if ($this->props['is_hot']) $classes[] = 'is-hot';
        if ($this->props['is_offline']) $classes[] = 'is-offline';
        if (!$this->props['clickable']) $classes[] = 'non-clickable';
        
        return implode(' ', $classes);
    }
    
    /**
     * 渲染卡片内容
     */
    private function render_card_content() {
        switch ($this->props['layout']) {
            case 'featured':
                $this->render_featured_layout();
                break;
            case 'compact':
                $this->render_compact_layout();
                break;
            case 'list':
                $this->render_list_layout();
                break;
            default:
                $this->render_default_layout();
        }
    }
    
    /**
     * 渲染默认布局
     */
    private function render_default_layout() {
        ?>
        <?php if ($this->props['clickable']): ?>
            <a href="<?php echo $this->esc_url($this->props['url']); ?>" 
               target="<?php echo esc_attr($this->props['target']); ?>"
               class="card-link"
               rel="noopener noreferrer">
        <?php endif; ?>
        
        <div class="card-inner">
            <?php $this->render_thumbnail(); ?>
            <?php $this->render_badges(); ?>
            
            <div class="card-content">
                <?php $this->render_header(); ?>
                <?php $this->render_description(); ?>
                <?php $this->render_footer(); ?>
            </div>
        </div>
        
        <?php if ($this->props['clickable']): ?>
            </a>
        <?php endif; ?>
        <?php
    }
    
    /**
     * 渲染特色布局
     */
    private function render_featured_layout() {
        ?>
        <div class="featured-card">
            <?php $this->render_large_thumbnail(); ?>
            <div class="featured-overlay">
                <?php $this->render_badges(); ?>
                <div class="featured-content">
                    <?php $this->render_header(); ?>
                    <?php $this->render_description(); ?>
                    <?php $this->render_stats(); ?>
                    <?php $this->render_action_button(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染紧凑布局
     */
    private function render_compact_layout() {
        ?>
        <?php if ($this->props['clickable']): ?>
            <a href="<?php echo $this->esc_url($this->props['url']); ?>" 
               target="<?php echo esc_attr($this->props['target']); ?>"
               class="compact-link">
        <?php endif; ?>
        
        <div class="compact-inner">
            <?php $this->render_favicon(); ?>
            <div class="compact-content">
                <h3 class="compact-title"><?php echo $this->esc_html($this->props['title']); ?></h3>
                <?php if ($this->props['show_category'] && !empty($this->props['category'])): ?>
                    <span class="compact-category"><?php echo $this->esc_html($this->props['category']); ?></span>
                <?php endif; ?>
            </div>
            <?php $this->render_compact_stats(); ?>
        </div>
        
        <?php if ($this->props['clickable']): ?>
            </a>
        <?php endif; ?>
        <?php
    }
    
    /**
     * 渲染列表布局
     */
    private function render_list_layout() {
        ?>
        <div class="list-item">
            <?php $this->render_favicon(); ?>
            <div class="list-content">
                <div class="list-header">
                    <h3 class="list-title">
                        <?php if ($this->props['clickable']): ?>
                            <a href="<?php echo $this->esc_url($this->props['url']); ?>" 
                               target="<?php echo esc_attr($this->props['target']); ?>">
                                <?php echo $this->esc_html($this->props['title']); ?>
                            </a>
                        <?php else: ?>
                            <?php echo $this->esc_html($this->props['title']); ?>
                        <?php endif; ?>
                    </h3>
                    <?php $this->render_badges(); ?>
                </div>
                <?php $this->render_description(); ?>
                <?php $this->render_tags(); ?>
            </div>
            <div class="list-meta">
                <?php $this->render_stats(); ?>
                <?php $this->render_action_button(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * 渲染缩略图
     */
    private function render_thumbnail() {
        if (!$this->props['show_thumbnail'] || empty($this->props['thumbnail'])) {
            return;
        }
        ?>
        <div class="card-thumbnail">
            <img src="<?php echo $this->esc_url($this->props['thumbnail']); ?>" 
                 alt="<?php echo esc_attr($this->props['title']); ?>"
                 loading="lazy"
                 onerror="this.style.display='none'">
        </div>
        <?php
    }
    
    /**
     * 渲染大缩略图（用于特色布局）
     */
    private function render_large_thumbnail() {
        if (empty($this->props['thumbnail'])) {
            return;
        }
        ?>
        <div class="large-thumbnail">
            <img src="<?php echo $this->esc_url($this->props['thumbnail']); ?>" 
                 alt="<?php echo esc_attr($this->props['title']); ?>"
                 loading="lazy">
        </div>
        <?php
    }
    
    /**
     * 渲染网站图标
     */
    private function render_favicon() {
        if (!$this->props['show_favicon']) {
            return;
        }
        
        $favicon_url = $this->props['favicon'];
        if (empty($favicon_url)) {
            // 尝试生成favicon URL
            $parsed_url = parse_url($this->props['url']);
            if (isset($parsed_url['host'])) {
                $favicon_url = "https://www.google.com/s2/favicons?domain={$parsed_url['host']}&sz=32";
            }
        }
        
        if (!empty($favicon_url)) {
            ?>
            <div class="card-favicon">
                <img src="<?php echo $this->esc_url($favicon_url); ?>" 
                     alt="<?php echo esc_attr($this->props['title']); ?>"
                     width="16" height="16"
                     loading="lazy"
                     onerror="this.style.display='none'">
            </div>
            <?php
        }
    }
    
    /**
     * 渲染头部（标题和分类）
     */
    private function render_header() {
        ?>
        <div class="card-header">
            <h3 class="card-title"><?php echo $this->esc_html($this->props['title']); ?></h3>
            <?php if ($this->props['show_category'] && !empty($this->props['category'])): ?>
                <span class="card-category"><?php echo $this->esc_html($this->props['category']); ?></span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染描述
     */
    private function render_description() {
        if (!$this->props['show_description'] || empty($this->props['description'])) {
            return;
        }
        ?>
        <div class="card-description">
            <p><?php echo $this->wp_kses_post($this->props['description']); ?></p>
        </div>
        <?php
    }
    
    /**
     * 渲染标签
     */
    private function render_tags() {
        if (empty($this->props['tags'])) {
            return;
        }
        ?>
        <div class="card-tags">
            <?php foreach ($this->props['tags'] as $tag): ?>
                <span class="tag"><?php echo $this->esc_html($tag); ?></span>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染徽章
     */
    private function render_badges() {
        ?>
        <div class="card-badges">
            <?php if ($this->props['is_new']): ?>
                <span class="badge badge-new"><?php _e('新站', 'yinghe'); ?></span>
            <?php endif; ?>
            
            <?php if ($this->props['is_hot']): ?>
                <span class="badge badge-hot"><?php _e('热门', 'yinghe'); ?></span>
            <?php endif; ?>
            
            <?php if ($this->props['is_featured']): ?>
                <span class="badge badge-featured"><?php _e('推荐', 'yinghe'); ?></span>
            <?php endif; ?>
            
            <?php if ($this->props['is_offline']): ?>
                <span class="badge badge-offline"><?php _e('离线', 'yinghe'); ?></span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染统计信息
     */
    private function render_stats() {
        if (!$this->props['show_stats']) {
            return;
        }
        ?>
        <div class="card-stats">
            <?php if ($this->props['visit_count'] > 0): ?>
                <span class="stat-item visits">
                    <i class="icon-eye"></i>
                    <?php echo number_format($this->props['visit_count']); ?>
                </span>
            <?php endif; ?>
            
            <?php if ($this->props['rating'] > 0): ?>
                <span class="stat-item rating">
                    <i class="icon-star"></i>
                    <?php echo number_format($this->props['rating'], 1); ?>
                </span>
            <?php endif; ?>
            
            <?php if (!empty($this->props['update_time'])): ?>
                <span class="stat-item update-time">
                    <i class="icon-time"></i>
                    <?php echo esc_html($this->props['update_time']); ?>
                </span>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * 渲染紧凑统计信息
     */
    private function render_compact_stats() {
        if (!$this->props['show_stats'] || $this->props['visit_count'] <= 0) {
            return;
        }
        ?>
        <div class="compact-stats">
            <span class="visit-count"><?php echo $this->format_number($this->props['visit_count']); ?></span>
        </div>
        <?php
    }
    
    /**
     * 渲染底部（操作按钮等）
     */
    private function render_footer() {
        ?>
        <div class="card-footer">
            <?php $this->render_stats(); ?>
            <?php $this->render_tags(); ?>
        </div>
        <?php
    }
    
    /**
     * 渲染操作按钮
     */
    private function render_action_button() {
        if (!$this->props['clickable']) {
            return;
        }
        ?>
        <div class="card-actions">
            <a href="<?php echo $this->esc_url($this->props['url']); ?>" 
               target="<?php echo esc_attr($this->props['target']); ?>"
               class="btn btn-primary btn-sm"
               rel="noopener noreferrer">
                <?php _e('访问', 'yinghe'); ?>
                <i class="icon-external-link"></i>
            </a>
        </div>
        <?php
    }
    
    /**
     * 格式化数字显示
     */
    private function format_number($number) {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }
        return number_format($number);
    }
    
    /**
     * 获取组件样式
     */
    protected function get_component_styles() {
        return "
            .yinghe-website-card {
                position: relative;
                border-radius: 12px;
                background: #fff;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                overflow: hidden;
            }
            
            .yinghe-website-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
            
            .yinghe-website-card.is-featured {
                border: 2px solid var(--color-primary, #3b82f6);
            }
            
            .card-link {
                display: block;
                text-decoration: none;
                color: inherit;
            }
            
            .card-inner {
                padding: 20px;
            }
            
            .card-thumbnail {
                width: 100%;
                height: 160px;
                margin-bottom: 16px;
                border-radius: 8px;
                overflow: hidden;
            }
            
            .card-thumbnail img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            
            .card-header {
                margin-bottom: 12px;
            }
            
            .card-title {
                font-size: 18px;
                font-weight: 600;
                margin: 0 0 8px 0;
                color: #1f2937;
                line-height: 1.4;
            }
            
            .card-category {
                display: inline-block;
                padding: 4px 8px;
                background: #f3f4f6;
                color: #6b7280;
                font-size: 12px;
                border-radius: 4px;
            }
            
            .card-description {
                margin-bottom: 16px;
            }
            
            .card-description p {
                color: #6b7280;
                font-size: 14px;
                line-height: 1.6;
                margin: 0;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
            
            .card-badges {
                position: absolute;
                top: 12px;
                right: 12px;
                display: flex;
                gap: 6px;
                flex-wrap: wrap;
            }
            
            .badge {
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                border-radius: 12px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            .badge-new { background: #10b981; color: white; }
            .badge-hot { background: #f59e0b; color: white; }
            .badge-featured { background: #8b5cf6; color: white; }
            .badge-offline { background: #ef4444; color: white; }
            
            .card-stats {
                display: flex;
                gap: 16px;
                align-items: center;
            }
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 13px;
                color: #6b7280;
            }
            
            .card-tags {
                display: flex;
                gap: 6px;
                flex-wrap: wrap;
                margin-top: 12px;
            }
            
            .tag {
                padding: 2px 6px;
                background: #f3f4f6;
                color: #6b7280;
                font-size: 11px;
                border-radius: 3px;
            }
            
            .card-favicon {
                display: inline-block;
                margin-right: 8px;
            }
            
            .card-favicon img {
                width: 16px;
                height: 16px;
            }
            
            /* 紧凑布局 */
            .yinghe-website-card.layout-compact {
                padding: 12px;
            }
            
            .compact-inner {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .compact-content {
                flex: 1;
            }
            
            .compact-title {
                font-size: 14px;
                font-weight: 500;
                margin: 0;
                line-height: 1.3;
            }
            
            .compact-category {
                font-size: 11px;
                color: #6b7280;
            }
            
            .compact-stats {
                font-size: 12px;
                color: #6b7280;
            }
            
            /* 列表布局 */
            .yinghe-website-card.layout-list {
                border-radius: 0;
                border-bottom: 1px solid #e5e7eb;
                box-shadow: none;
            }
            
            .list-item {
                display: flex;
                align-items: flex-start;
                gap: 16px;
                padding: 16px 0;
            }
            
            .list-content {
                flex: 1;
            }
            
            .list-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 8px;
            }
            
            .list-title {
                font-size: 16px;
                font-weight: 500;
                margin: 0;
            }
            
            .list-title a {
                text-decoration: none;
                color: #1f2937;
            }
            
            .list-title a:hover {
                color: var(--color-primary, #3b82f6);
            }
            
            .list-meta {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 8px;
            }
            
            /* 特色布局 */
            .featured-card {
                position: relative;
                height: 300px;
                border-radius: 16px;
                overflow: hidden;
            }
            
            .large-thumbnail {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            
            .large-thumbnail img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            
            .featured-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(to bottom, transparent 40%, rgba(0,0,0,0.8));
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding: 24px;
            }
            
            .featured-content {
                color: white;
            }
            
            .featured-content .card-title {
                color: white;
                font-size: 24px;
                margin-bottom: 8px;
            }
            
            .featured-content .card-description p {
                color: rgba(255, 255, 255, 0.9);
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .card-inner {
                    padding: 16px;
                }
                
                .card-thumbnail {
                    height: 120px;
                }
                
                .list-item {
                    flex-direction: column;
                    gap: 12px;
                }
                
                .list-meta {
                    align-items: flex-start;
                    flex-direction: row;
                    justify-content: space-between;
                    width: 100%;
                }
            }
            
            /* 暗色主题支持 */
            [data-theme='dark'] .yinghe-website-card {
                background: #1f2937;
                color: #f9fafb;
            }
            
            [data-theme='dark'] .card-title {
                color: #f9fafb;
            }
            
            [data-theme='dark'] .card-description p {
                color: #d1d5db;
            }
            
            [data-theme='dark'] .list-title a {
                color: #f9fafb;
            }
        ";
    }
    
    /**
     * 获取组件脚本
     */
    protected function get_component_scripts() {
        return "
            // 网站卡片组件交互脚本
            (function() {
                const cards = document.querySelectorAll('.yinghe-website-card[data-track-visit=\"true\"]');
                
                cards.forEach(card => {
                    card.addEventListener('click', function() {
                        const siteUrl = this.dataset.siteUrl;
                        const siteTitle = this.dataset.siteTitle;
                        
                        // 发送访问统计
                        if (window.yingheAnalytics) {
                            window.yingheAnalytics.trackSiteVisit({
                                url: siteUrl,
                                title: siteTitle,
                                timestamp: Date.now()
                            });
                        }
                        
                        // 添加到浏览历史
                        if (window.yingheBrowsingHistory) {
                            window.yingheBrowsingHistory.addItem({
                                url: siteUrl,
                                title: siteTitle,
                                timestamp: Date.now()
                            });
                        }
                        
                        // 触发自定义事件
                        const event = new CustomEvent('yinghe:card:click', {
                            detail: {
                                url: siteUrl,
                                title: siteTitle,
                                element: this
                            }
                        });
                        document.dispatchEvent(event);
                    });
                    
                    // 悬停效果增强
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-6px)';
                    });
                    
                    card.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                    });
                });
                
                // 图片加载错误处理
                const cardImages = document.querySelectorAll('.yinghe-website-card img');
                cardImages.forEach(img => {
                    img.addEventListener('error', function() {
                        this.style.display = 'none';
                        
                        // 如果是缩略图，显示占位符
                        if (this.closest('.card-thumbnail')) {
                            const placeholder = document.createElement('div');
                            placeholder.className = 'thumbnail-placeholder';
                            placeholder.innerHTML = '<i class=\"icon-image\"></i>';
                            this.parentNode.appendChild(placeholder);
                        }
                    });
                });
            })();
        ";
    }
}

/**
 * 辅助函数：渲染网站卡片
 * 
 * @param array $props 组件属性
 * @return void
 */
function yinghe_render_website_card($props = []) {
    echo YingheWebsiteCardComponent::render_static($props);
}

/**
 * 辅助函数：获取网站卡片HTML
 * 
 * @param array $props 组件属性
 * @return string 组件HTML
 */
function yinghe_get_website_card($props = []) {
    return YingheWebsiteCardComponent::render_static($props);
}