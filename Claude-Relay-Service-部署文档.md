# Claude Relay Service 自定义部署文档

## 📋 概述

本文档记录了如何部署和配置 Claude Relay Service，使其支持自定义 Claude API 端点和 JWT Token 认证。

## 🎯 目标

- 部署 Claude Relay Service
- 配置自定义 Claude API 端点
- 支持 JWT Token 认证
- 实现无需手动配置 Claude 账户的自动回退机制

## 📦 环境要求

- Docker & Docker Compose
- Git
- 自定义 Claude API 端点
- JWT Token

## 🚀 部署步骤

### 1. 克隆源码

```bash
git clone https://github.com/Wei-Shaw/claude-relay-service.git
cd claude-relay-service
```

### 2. 修改配置文件

#### 2.1 修改 `config/config.example.js`

在 Claude API 配置部分添加默认 API Key 支持：

```javascript
// 🎯 Claude API配置
claude: {
  apiUrl: process.env.CLAUDE_API_URL || 'https://api.anthropic.com/v1/messages',
  apiVersion: process.env.CLAUDE_API_VERSION || '2023-06-01',
  betaHeader: process.env.CLAUDE_BETA_HEADER || 'claude-code-********,oauth-2025-04-20,interleaved-thinking-2025-05-14,fine-grained-tool-streaming-2025-05-14',
  defaultApiKey: process.env.CLAUDE_API_KEY || null  // 新增这一行
},
```

### 3. 修改账户服务

#### 3.1 修改 `src/services/claudeAccountService.js`

**在 `selectAccountForApiKey` 方法中添加默认 API Key 支持：**

找到第 500-502 行：
```javascript
if (sharedAccounts.length === 0) {
  throw new Error('No active shared Claude accounts available');
}
```

替换为：
```javascript
if (sharedAccounts.length === 0) {
  // 检查是否有默认API Key配置
  const config = require('../../config/config');
  if (config.claude.defaultApiKey) {
    logger.info('🔑 No Claude accounts available, will use default API Key');
    return 'default-api-key';
  }
  throw new Error('No active shared Claude accounts available');
}
```

**在 `getValidAccessToken` 方法开头添加：**

找到第 237 行，在方法开头添加：
```javascript
// 如果是默认API Key标识，直接返回配置的默认API Key
if (accountId === 'default-api-key') {
  const config = require('../../config/config');
  if (config.claude.defaultApiKey) {
    logger.info('🔑 Using configured default Claude API Key');
    return config.claude.defaultApiKey;
  }
  throw new Error('Default API Key not configured');
}
```

**在 catch 块中添加回退逻辑：**

找到第 278-281 行的 catch 块：
```javascript
} catch (error) {
  logger.error(`❌ Failed to get valid access token for account ${accountId}:`, error);
  throw error;
}
```

替换为：
```javascript
} catch (error) {
  logger.error(`❌ Failed to get valid access token for account ${accountId}:`, error);
  
  // 如果获取账户token失败，尝试使用默认的API Key
  const config = require('../../config/config');
  if (config.claude.defaultApiKey) {
    logger.info(`🔑 Using default Claude API Key as fallback`);
    return config.claude.defaultApiKey;
  }
  
  throw error;
}
```

### 4. 构建自定义 Docker 镜像

```bash
docker build -t claude-relay-service-custom .
```

### 5. 创建配置文件

#### 5.1 创建 `.env` 文件

```bash
# 必填：安全密钥（已生成随机值）
JWT_SECRET=8f9e2a7b4c6d1e3f5a8b9c2d4e6f7a1b3c5d8e9f2a4b6c8d1e3f5a7b9c2d4e6f8a
ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234ab

# 可选：管理员凭据
ADMIN_USERNAME=cr_admin
ADMIN_PASSWORD=ClaudeRelay2024!@#$%

# Claude API 配置
CLAUDE_API_URL=https://api.duckcode.top/api/claude/v1/messages
CLAUDE_API_KEY=your-jwt-token-here
```

#### 5.2 创建 `docker-compose.yml` 文件

```yaml
version: '3.8'
services:
  claude-relay:
    image: claude-relay-service-custom
    container_name: claude-relay-service
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - REDIS_HOST=redis
      - ADMIN_USERNAME=${ADMIN_USERNAME:-}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
      - CLAUDE_API_URL=https://api.duckcode.top/api/claude/v1/messages
      - CLAUDE_API_KEY=your-jwt-token-here
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: claude-relay-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

### 6. 启动服务

```bash
docker-compose up -d
```

### 7. 验证部署

#### 7.1 检查服务状态

```bash
docker-compose ps
```

#### 7.2 查看日志

```bash
docker-compose logs --tail=20 claude-relay
```

#### 7.3 测试 API

```bash
curl -X POST http://localhost:3001/api/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "claude-3-5-haiku-20241022",
    "max_tokens": 50,
    "messages": [
      {
        "role": "user",
        "content": "Hello, test message"
      }
    ]
  }'
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `JWT_SECRET` | JWT 签名密钥 | 至少32字符的随机字符串 |
| `ENCRYPTION_KEY` | 数据加密密钥 | 32字符的加密密钥 |
| `ADMIN_USERNAME` | 管理员用户名 | `cr_admin` |
| `ADMIN_PASSWORD` | 管理员密码 | 强密码 |
| `CLAUDE_API_URL` | Claude API 端点 | `https://api.duckcode.top/api/claude/v1/messages` |
| `CLAUDE_API_KEY` | JWT Token | 您的 JWT Token |

### 访问地址

- **API 端点：** `http://localhost:3001/api/v1/messages`
- **Web 管理界面：** `http://localhost:3001/web`
- **健康检查：** `http://localhost:3001/health`

### 管理员登录

- **用户名：** `cr_admin`
- **密码：** `ClaudeRelay2024!@#$%`

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   - 修改 `docker-compose.yml` 中的端口映射
   - 例如：`"3002:3000"`

2. **环境变量未生效**
   - 确保 `.env` 文件在正确位置
   - 重新启动服务：`docker-compose restart`

3. **API 认证失败**
   - 检查 JWT Token 是否正确
   - 确认 API 端点 URL 是否正确

### 日志查看

```bash
# 查看实时日志
docker-compose logs -f claude-relay

# 查看最近日志
docker-compose logs --tail=50 claude-relay

# 查看错误日志
docker-compose logs claude-relay | grep -i error
```

## 📊 使用统计

部署成功后，可以通过管理界面查看：

- API 调用统计
- Token 使用量
- 成本分析
- 性能监控

## 🔄 更新和维护

### 更新服务

```bash
# 停止服务
docker-compose down

# 重新构建镜像
docker build -t claude-relay-service-custom .

# 启动服务
docker-compose up -d
```

### 备份数据

```bash
# 备份数据目录
tar -czf claude-relay-backup-$(date +%Y%m%d).tar.gz data/ logs/
```

## 📝 注意事项

1. **安全性**
   - 定期更换 JWT Token
   - 使用强密码
   - 限制网络访问

2. **性能**
   - 监控 API 响应时间
   - 定期清理日志文件
   - 根据需要调整资源配置

3. **维护**
   - 定期备份数据
   - 监控服务状态
   - 及时更新依赖

## ✅ 部署完成

恭喜！Claude Relay Service 已成功部署并配置完成。现在您可以：

- 使用自定义 API 端点
- 通过 JWT Token 认证
- 享受完整的 Claude Relay Service 功能
- 通过 Web 界面管理和监控服务

---

**文档版本：** v1.0  
**最后更新：** 2025-07-27  
**适用版本：** Claude Relay Service (自定义构建)
