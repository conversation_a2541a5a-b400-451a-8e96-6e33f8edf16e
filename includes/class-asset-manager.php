<?php
/**
 * 资源管理器类
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 资源文件管理和优化
 */
class YingheAssetManager {
    private $enqueued_components = [];
    private $inline_styles = [];
    private $inline_scripts = [];
    private $critical_css = '';
    
    /**
     * 构造函数
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_theme_assets'], 10);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_component_assets'], 20);
        add_action('wp_head', [$this, 'output_critical_css'], 1);
        add_action('wp_footer', [$this, 'output_inline_assets'], 30);
        
        // 存储实例到全局变量供组件使用
        $GLOBALS['yinghe_asset_manager'] = $this;
    }
    
    /**
     * 注册主题核心资源
     */
    public function enqueue_theme_assets() {
        // Bootstrap CSS (从原型复制)
        wp_enqueue_style(
            'bootstrap',
            YINGHE_ASSETS_URI . '/vendor/bootstrap.min.css',
            [],
            '5.3.2'
        );
        
        // 图标字体 (从原型复制)
        wp_enqueue_style(
            'yinghe-icons',
            YINGHE_ASSETS_URI . '/fonts/font_4152982_f00lvbu68l.css',
            [],
            YINGHE_THEME_VERSION
        );
        
        // Swiper CSS (从原型复制)
        wp_enqueue_style(
            'swiper',
            YINGHE_ASSETS_URI . '/vendor/swiper-bundle.min.css',
            [],
            '8.4.7'
        );
        
        // 主题样式 (从原型复制和优化)
        wp_enqueue_style(
            'yinghe-main',
            YINGHE_ASSETS_URI . '/css/style.min.css',
            ['bootstrap', 'yinghe-icons'],
            $this->get_asset_version('css/style.min.css')
        );
        
        // 自定义样式
        wp_enqueue_style(
            'yinghe-custom',
            YINGHE_ASSETS_URI . '/css/style.css',
            ['yinghe-main'],
            $this->get_asset_version('css/style.css')
        );
        
        // jQuery (WordPress内置)
        wp_enqueue_script('jquery');
        
        // Bootstrap JS
        wp_enqueue_script(
            'bootstrap',
            YINGHE_ASSETS_URI . '/vendor/bootstrap.min.js',
            ['jquery'],
            '5.3.2',
            true
        );
        
        // Swiper JS
        wp_enqueue_script(
            'swiper',
            YINGHE_ASSETS_URI . '/vendor/swiper-bundle.min.js',
            [],
            '8.4.7',
            true
        );
        
        // 懒加载
        if (get_theme_mod('enable_lazy_loading', true)) {
            wp_enqueue_script(
                'lazyload',
                YINGHE_ASSETS_URI . '/vendor/lazyload.min.js',
                [],
                '17.8.3',
                true
            );
        }
        
        // 剪贴板功能
        wp_enqueue_script(
            'clipboard',
            YINGHE_ASSETS_URI . '/vendor/clipboard.min.js',
            [],
            '2.0.8',
            true
        );
        
        // 侧边栏插件
        wp_enqueue_script(
            'sticky-sidebar',
            YINGHE_ASSETS_URI . '/vendor/theia-sticky-sidebar.js',
            ['jquery'],
            '1.7.0',
            true
        );
        
        // 主题主脚本
        wp_enqueue_script(
            'yinghe-main',
            YINGHE_ASSETS_URI . '/js/yinghe.js',
            ['jquery', 'bootstrap'],
            $this->get_asset_version('js/yinghe.js'),
            true
        );
        
        // 应用脚本 (从原型移植)
        wp_enqueue_script(
            'yinghe-app',
            YINGHE_ASSETS_URI . '/js/app.min.js',
            ['yinghe-main'],
            $this->get_asset_version('js/app.min.js'),
            true
        );
        
        // 本地化脚本数据
        wp_localize_script('yinghe-main', 'yingheConfig', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yinghe_ajax_nonce'),
            'apiUrl' => rest_url('yinghe/v1/'),
            'currentUser' => get_current_user_id(),
            'homeUrl' => home_url('/'),
            'themeUrl' => get_template_directory_uri(),
            'translations' => $this->get_js_translations(),
            'settings' => $this->get_theme_settings()
        ]);
    }
    
    /**
     * 注册组件特定资源
     * 
     * @param string $component_name 组件名称
     * @param array $assets 资源配置
     */
    public function register_component_assets($component_name, $assets) {
        if (isset($this->enqueued_components[$component_name])) {
            return; // 避免重复注册
        }
        
        $this->enqueued_components[$component_name] = $assets;
        
        // 注册CSS
        if (!empty($assets['styles'])) {
            foreach ($assets['styles'] as $handle => $style) {
                wp_enqueue_style(
                    $handle,
                    $style['src'],
                    $style['deps'] ?? [],
                    $style['version'] ?? YINGHE_THEME_VERSION,
                    $style['media'] ?? 'all'
                );
            }
        }
        
        // 注册JS
        if (!empty($assets['scripts'])) {
            foreach ($assets['scripts'] as $handle => $script) {
                wp_enqueue_script(
                    $handle,
                    $script['src'],
                    $script['deps'] ?? [],
                    $script['version'] ?? YINGHE_THEME_VERSION,
                    $script['in_footer'] ?? true
                );
            }
        }
    }
    
    /**
     * 注册组件资源（简化版）
     */
    public function enqueue_component_assets() {
        // 这里可以根据当前页面需要加载特定组件的资源
        // 目前先保持简单，所有资源都在主题中统一加载
    }
    
    /**
     * 添加内联样式
     * 
     * @param string $component_name 组件名称
     * @param string $css CSS代码
     */
    public function add_inline_style($component_name, $css) {
        if (!empty($css)) {
            $this->inline_styles[$component_name] = $css;
        }
    }
    
    /**
     * 添加内联脚本
     * 
     * @param string $component_name 组件名称
     * @param string $js JavaScript代码
     */
    public function add_inline_script($component_name, $js) {
        if (!empty($js)) {
            $this->inline_scripts[$component_name] = $js;
        }
    }
    
    /**
     * 设置关键CSS
     * 
     * @param string $css 关键CSS代码
     */
    public function set_critical_css($css) {
        $this->critical_css = $css;
    }
    
    /**
     * 输出关键CSS
     */
    public function output_critical_css() {
        if (!empty($this->critical_css)) {
            echo '<style id="yinghe-critical-css">' . $this->critical_css . '</style>';
        }
        
        // 输出自定义CSS变量
        $this->output_css_variables();
    }
    
    /**
     * 输出CSS变量
     */
    private function output_css_variables() {
        $sidebar_width = get_theme_mod('sidebar_width', 220);
        $custom_colors = get_theme_mod('custom_colors', []);
        
        echo '<style id="yinghe-css-variables">';
        echo ':root {';
        echo "--yinghe-sidebar-width: {$sidebar_width}px;";
        
        if (!empty($custom_colors)) {
            foreach ($custom_colors as $key => $color) {
                echo "--yinghe-{$key}: {$color};";
            }
        }
        
        echo '}';
        echo '</style>';
    }
    
    /**
     * 输出内联资源
     */
    public function output_inline_assets() {
        // 输出内联样式
        if (!empty($this->inline_styles)) {
            echo '<style id="yinghe-component-styles">';
            foreach ($this->inline_styles as $component => $css) {
                echo "/* Component: {$component} */\n";
                echo $this->minify_css($css) . "\n";
            }
            echo '</style>';
        }
        
        // 输出内联脚本
        if (!empty($this->inline_scripts)) {
            echo '<script id="yinghe-component-scripts">';
            echo '(function($) {';
            echo '"use strict";';
            foreach ($this->inline_scripts as $component => $js) {
                echo "/* Component: {$component} */\n";
                echo str_replace('{unique_id}', 'yinghe-' . $component, $js) . "\n";
            }
            echo '})(jQuery);';
            echo '</script>';
        }
    }
    
    /**
     * 获取资源文件版本号
     * 
     * @param string $asset_path 资源文件相对路径
     * @return string
     */
    private function get_asset_version($asset_path) {
        // 开发模式下使用文件修改时间作为版本号
        if (WP_DEBUG) {
            $file_path = YINGHE_THEME_DIR . '/static/' . $asset_path;
            if (file_exists($file_path)) {
                return filemtime($file_path);
            }
        }
        
        return YINGHE_THEME_VERSION;
    }
    
    /**
     * 获取JavaScript翻译
     * 
     * @return array
     */
    private function get_js_translations() {
        return [
            'loading' => __('加载中...', 'yinghe'),
            'error' => __('发生错误', 'yinghe'),
            'success' => __('操作成功', 'yinghe'),
            'confirm' => __('确认操作', 'yinghe'),
            'cancel' => __('取消', 'yinghe'),
            'close' => __('关闭', 'yinghe'),
            'search_placeholder' => __('输入关键字搜索', 'yinghe'),
            'no_results' => __('没有找到相关结果', 'yinghe'),
            'visit_site' => __('访问网站', 'yinghe'),
            'copy_link' => __('复制链接', 'yinghe'),
            'copied' => __('已复制', 'yinghe'),
            'share' => __('分享', 'yinghe'),
        ];
    }
    
    /**
     * 获取主题设置
     * 
     * @return array
     */
    private function get_theme_settings() {
        return [
            'theme_mode' => get_theme_mod('default_theme_mode', 'light'),
            'animation_enabled' => get_theme_mod('enable_animations', true),
            'lazy_load_enabled' => get_theme_mod('enable_lazy_loading', true),
            'auto_theme_switch' => get_theme_mod('auto_theme_switch', true),
            'visit_tracking' => get_theme_mod('enable_visit_tracking', true),
            'show_visit_count' => get_theme_mod('show_visit_count', true),
        ];
    }
    
    /**
     * 简单的CSS压缩
     * 
     * @param string $css CSS代码
     * @return string
     */
    private function minify_css($css) {
        // 移除注释
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // 移除多余的空白
        $css = str_replace(["\r\n", "\r", "\n", "\t"], ' ', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        
        // 移除分号前的空格
        $css = str_replace(' ;', ';', $css);
        
        // 移除大括号周围的空格
        $css = str_replace([' {', '{ ', ' }', '} '], ['{', '{', '}', '}'], $css);
        
        return trim($css);
    }
    
    /**
     * 预加载关键资源
     */
    public function preload_assets() {
        // 预加载关键字体
        echo '<link rel="preload" href="' . YINGHE_ASSETS_URI . '/fonts/font_4152982_f00lvbu68l.woff2" as="font" type="font/woff2" crossorigin>';
        
        // 预加载关键图片
        $logo_url = get_theme_mod('logo_light');
        if ($logo_url) {
            echo '<link rel="preload" href="' . esc_url($logo_url) . '" as="image">';
        }
    }
}

/**
 * 获取资源管理器实例
 * 
 * @return YingheAssetManager
 */
function yinghe_get_asset_manager() {
    return $GLOBALS['yinghe_asset_manager'] ?? new YingheAssetManager();
}