<?php
/**
 * 硬核指南主题 - 助手函数
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 获取网站访问统计数据
 * 
 * @param int $site_id 网站ID
 * @param string $period 统计周期 (total, today, week, month)
 * @return int
 */
if (!function_exists('yinghe_get_site_visit_count')) {
    function yinghe_get_site_visit_count($site_id, $period = 'total') {
        if (!$site_id) {
            return 0;
        }
        
        // 首先尝试从缓存获取
        $cache_key = "visit_count_{$site_id}_{$period}";
        $cached_count = get_transient($cache_key);
        
        if ($cached_count !== false) {
            return intval($cached_count);
        }
        
        // 从数据库获取
        global $wpdb;
        $table = $wpdb->prefix . 'yinghe_site_visits';
        
        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            return 0;
        }
        
        $where_clause = "WHERE site_id = %d";
        
        switch ($period) {
            case 'today':
                $where_clause .= " AND DATE(visit_time) = CURDATE()";
                break;
            case 'week':
                $where_clause .= " AND visit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $where_clause .= " AND visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
            case 'total':
            default:
                // 无额外条件
                break;
        }
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table $where_clause",
            $site_id
        ));
        
        // 缓存结果（5分钟）
        set_transient($cache_key, $count, 5 * MINUTE_IN_SECONDS);
        
        return intval($count);
    }
}

/**
 * 记录网站访问
 * 
 * @param int $site_id 网站ID
 * @param array $additional_data 额外数据
 * @return bool
 */
if (!function_exists('yinghe_record_site_visit')) {
    function yinghe_record_site_visit($site_id, $additional_data = []) {
        global $wpdb;
        
        if (!get_post($site_id) || get_post_type($site_id) !== 'sites') {
            return false;
        }
        
        // 基本访问数据
        $default_data = [
            'site_id' => intval($site_id),
            'user_id' => get_current_user_id(),
            'user_ip' => sanitize_text_field($_SERVER['REMOTE_ADDR'] ?? ''),
            'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
            'page_url' => sanitize_text_field($_SERVER['REQUEST_URI'] ?? ''),
            'referer_url' => sanitize_text_field($_SERVER['HTTP_REFERER'] ?? ''),
            'device_type' => wp_is_mobile() ? 'mobile' : 'desktop',
            'visit_time' => current_time('mysql'),
        ];
        
        $data = array_merge($default_data, $additional_data);
        
        $table = $wpdb->prefix . 'yinghe_site_visits';
        
        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            return false;
        }
        
        $result = $wpdb->insert($table, $data);
        
        // 清除相关缓存
        if ($result) {
            $cache_keys = [
                "visit_count_{$site_id}_total",
                "visit_count_{$site_id}_today",
                "visit_count_{$site_id}_week",
                "visit_count_{$site_id}_month"
            ];
            
            foreach ($cache_keys as $key) {
                delete_transient($key);
            }
        }
        
        return $result !== false;
    }
}

/**
 * 获取热门网站列表
 * 
 * @param int $limit 限制数量
 * @param string $period 统计周期
 * @return array
 */
if (!function_exists('yinghe_get_popular_sites')) {
    function yinghe_get_popular_sites($limit = 10, $period = 'week') {
        global $wpdb;
        
        $cache_key = "popular_sites_{$period}_{$limit}";
        $cached_sites = get_transient($cache_key);
        
        if ($cached_sites !== false) {
            return $cached_sites;
        }
        
        $table = $wpdb->prefix . 'yinghe_site_visits';
        
        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            return [];
        }
        
        $where_clause = '';
        
        switch ($period) {
            case 'today':
                $where_clause = "WHERE DATE(visit_time) = CURDATE()";
                break;
            case 'week':
                $where_clause = "WHERE visit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $where_clause = "WHERE visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
            case 'total':
            default:
                // 无额外条件
                break;
        }
        
        $sql = "
            SELECT site_id, COUNT(*) as visit_count
            FROM $table
            $where_clause
            GROUP BY site_id
            ORDER BY visit_count DESC
            LIMIT %d
        ";
        
        $results = $wpdb->get_results($wpdb->prepare($sql, $limit));
        
        $sites = [];
        foreach ($results as $result) {
            $post = get_post($result->site_id);
            if ($post && $post->post_status === 'publish') {
                $sites[] = [
                    'site_id' => $result->site_id,
                    'visit_count' => $result->visit_count,
                    'title' => $post->post_title,
                    'url' => get_post_meta($result->site_id, 'site_url', true)
                ];
            }
        }
        
        // 缓存结果（30分钟）
        set_transient($cache_key, $sites, 30 * MINUTE_IN_SECONDS);
        
        return $sites;
    }
}

/**
 * 格式化访问次数显示
 * 
 * @param int $count 访问次数
 * @return string
 */
if (!function_exists('yinghe_format_visit_count')) {
    function yinghe_format_visit_count($count) {
        if ($count >= 1000000) {
            return round($count / 1000000, 1) . 'M';
        } elseif ($count >= 1000) {
            return round($count / 1000, 1) . 'K';
        }
        return number_format($count);
    }
}

/**
 * 获取网站图标
 * 
 * @param int $site_id 网站ID
 * @param string $size 图标尺寸
 * @return string
 */
if (!function_exists('yinghe_get_site_icon')) {
    function yinghe_get_site_icon($site_id, $size = 'medium') {
        $icon_url = get_post_meta($site_id, 'site_icon', true);
        
        if (!$icon_url) {
            // 返回默认图标
            return get_template_directory_uri() . '/static/picture/favicon.png';
        }
        
        return esc_url($icon_url);
    }
}

/**
 * 检查是否为推荐网站
 * 
 * @param int $site_id 网站ID
 * @return bool
 */
if (!function_exists('yinghe_is_featured_site')) {
    function yinghe_is_featured_site($site_id) {
        return get_post_meta($site_id, 'is_featured', true) === '1';
    }
}

/**
 * 获取网站标签
 * 
 * @param int $site_id 网站ID
 * @param string $field 返回字段类型
 * @return array
 */
if (!function_exists('yinghe_get_site_tags')) {
    function yinghe_get_site_tags($site_id, $field = 'names') {
        $tags = get_the_terms($site_id, 'sitetag');
        
        if (!$tags || is_wp_error($tags)) {
            return [];
        }
        
        if ($field === 'names') {
            return wp_list_pluck($tags, 'name');
        }
        
        return $tags;
    }
}

/**
 * 获取网站链接（带跟踪）
 * 
 * @param int $site_id 网站ID
 * @param bool $enable_tracking 是否启用跟踪
 * @return string
 */
if (!function_exists('yinghe_get_site_link')) {
    function yinghe_get_site_link($site_id, $enable_tracking = true) {
        $site_url = get_post_meta($site_id, 'site_url', true);
        
        if (!$site_url) {
            return '#';
        }
        
        if ($enable_tracking) {
            // 生成跟踪链接
            $tracking_url = home_url('/go/?url=' . base64_encode($site_url));
            return esc_url($tracking_url);
        }
        
        return esc_url($site_url);
    }
}

/**
 * 获取主题版本
 * 
 * @return string
 */
if (!function_exists('yinghe_get_theme_version')) {
    function yinghe_get_theme_version() {
        $theme = wp_get_theme();
        return $theme->get('Version') ?: '1.0.0';
    }
}

/**
 * 渲染侧边栏导航组件
 * 
 * @param array $props 组件属性
 */
if (!function_exists('yinghe_render_sidebar_nav')) {
    function yinghe_render_sidebar_nav($props = []) {
        // 临时的简化版本，直接输出HTML而不依赖组件类
        $default_props = [
            'logo_config' => [
                'expanded_light' => get_template_directory_uri() . '/static/picture/logo-white-wide.webp',
                'expanded_dark' => get_template_directory_uri() . '/static/picture/logo-black-wide.webp',
                'collapsed_light' => get_template_directory_uri() . '/static/picture/logo-white.webp',
                'collapsed_dark' => get_template_directory_uri() . '/static/picture/logo-black.webp',
            ],
            'show_domain_info' => true,
            'domain_links' => [
                ['url' => home_url('/'), 'title' => get_bloginfo('name'), 'text' => '硬核指南.com']
            ],
        ];
        
        $props = array_merge($default_props, $props);
        
        // 输出侧边栏导航HTML
        echo '<div class="modal-dialog h-100 sidebar-nav-inner">';
        echo '<div class="sidebar-logo">';
        echo '<div class="logo overflow-hidden">';
        echo '<h1 class="text-hide position-absolute">' . esc_html(get_bloginfo('name')) . '</h1>';
        echo '<a href="' . esc_url(home_url('/')) . '" class="logo-expanded">';
        echo '<img src="' . esc_url($props['logo_config']['expanded_light']) . '" class="logo-light" alt="' . esc_attr(get_bloginfo('name')) . '">';
        echo '<img src="' . esc_url($props['logo_config']['expanded_dark']) . '" class="logo-dark d-none" alt="' . esc_attr(get_bloginfo('name')) . '">';
        echo '</a>';
        echo '<a href="' . esc_url(home_url('/')) . '" class="logo-collapsed">';
        echo '<img src="' . esc_url($props['logo_config']['collapsed_light']) . '" class="logo-light" alt="' . esc_attr(get_bloginfo('name')) . '">';
        echo '<img src="' . esc_url($props['logo_config']['collapsed_dark']) . '" class="logo-dark d-none" alt="' . esc_attr(get_bloginfo('name')) . '">';
        echo '</a>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="sidebar-menu flex-fill">';
        echo '<div class="sidebar-scroll">';
        echo '<div class="sidebar-menu-inner">';
        
        // 渲染菜单
        if (has_nav_menu('primary')) {
            wp_nav_menu([
                'theme_location' => 'primary',
                'container' => 'ul',
                'menu_class' => 'sidebar-menu-list'
            ]);
        } else {
            // 默认菜单结构
            echo '<ul>';
            echo '<li class="sidebar-item"><a href="#"><i class="io io-heart icon-fw icon-lg"></i><span>合作推荐</span></a></li>';
            echo '<li class="sidebar-item"><a href="#"><i class="io io-film icon-fw icon-lg"></i><span>影视</span></a></li>';
            echo '<li class="sidebar-item"><a href="#"><i class="io io-acg icon-fw icon-lg"></i><span>二次元</span></a></li>';
            echo '<li class="sidebar-item"><a href="#"><i class="io io-headphones icon-fw icon-lg"></i><span>音乐</span></a></li>';
            echo '<li class="sidebar-item"><a href="#"><i class="io io-read icon-fw icon-lg"></i><span>阅读</span></a></li>';
            echo '<li class="sidebar-item"><a href="#"><i class="io io-send icon-fw icon-lg"></i><span>娱乐</span></a></li>';
            echo '</ul>';
        }
        
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        if ($props['show_domain_info']) {
            echo '<div class="domain">';
            echo '<div class="domain-header"><span>回家地址</span></div>';
            foreach ($props['domain_links'] as $link) {
                echo '<a href="' . esc_url($link['url']) . '" title="' . esc_attr($link['title']) . '" target="_blank">' . esc_html($link['text']) . '</a>';
            }
            echo '</div>';
        }
        
        echo '</div>';
    }
}