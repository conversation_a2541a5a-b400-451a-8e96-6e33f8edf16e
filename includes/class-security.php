<?php
/**
 * 安全管理器类
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 安全管理器
 */
class YingheSecurity {
    
    /**
     * 构造函数
     */
    public function __construct() {
        add_action('init', [$this, 'init_security_headers']);
        add_filter('wp_kses_allowed_html', [$this, 'extend_allowed_html'], 10, 2);
    }
    
    /**
     * 初始化安全头
     */
    public function init_security_headers() {
        // 添加安全头
        if (!headers_sent()) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    }
    
    /**
     * 扩展允许的HTML标签
     * 
     * @param array $allowed_html 允许的HTML标签
     * @param string $context 上下文
     * @return array
     */
    public function extend_allowed_html($allowed_html, $context) {
        if ($context === 'post') {
            // 为组件添加允许的HTML标签和属性
            $allowed_html['div']['data-*'] = true;
            $allowed_html['a']['data-*'] = true;
            $allowed_html['img']['data-src'] = true;
            $allowed_html['img']['loading'] = true;
            $allowed_html['i']['class'] = true;
            $allowed_html['span']['class'] = true;
        }
        
        return $allowed_html;
    }
    
    /**
     * 获取用户IP地址
     * 
     * @return string
     */
    public static function get_user_ip() {
        // 检查代理和负载均衡器的头部
        $ip_headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // 代理
            'HTTP_X_FORWARDED_FOR',      // 负载均衡器/代理
            'HTTP_X_FORWARDED',          // 代理
            'HTTP_X_CLUSTER_CLIENT_IP',  // 集群
            'HTTP_FORWARDED_FOR',        // 代理
            'HTTP_FORWARDED',            // 代理
            'REMOTE_ADDR'                // 标准
        ];
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip_list = explode(',', $_SERVER[$header]);
                $ip = trim($ip_list[0]);
                
                // 验证IP地址
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        // 回退到 REMOTE_ADDR
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 清理网站URL
     * 
     * @param string $url URL地址
     * @return string|false
     */
    public static function sanitize_site_url($url) {
        $url = esc_url_raw($url);
        
        // 验证URL格式
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // 检查协议
        $parsed = parse_url($url);
        if (!in_array($parsed['scheme'] ?? '', ['http', 'https'])) {
            return false;
        }
        
        // 检查域名
        if (empty($parsed['host'])) {
            return false;
        }
        
        return $url;
    }
    
    /**
     * 清理搜索查询
     * 
     * @param string $query 搜索查询
     * @return string
     */
    public static function sanitize_search_query($query) {
        $query = sanitize_text_field($query);
        
        // 移除潜在的恶意字符
        $query = preg_replace('/[<>"\']/', '', $query);
        
        // 限制长度
        $query = substr($query, 0, 200);
        
        return $query;
    }
    
    /**
     * 清理用户代理字符串
     * 
     * @param string $user_agent 用户代理
     * @return string
     */
    public static function sanitize_user_agent($user_agent) {
        $user_agent = sanitize_text_field($user_agent);
        
        // 限制长度
        $user_agent = substr($user_agent, 0, 500);
        
        return $user_agent;
    }
    
    /**
     * 生成安全的nonce
     * 
     * @param string $action 动作名称
     * @return string
     */
    public static function create_nonce($action) {
        return wp_create_nonce($action);
    }
    
    /**
     * 验证nonce
     * 
     * @param string $nonce nonce值
     * @param string $action 动作名称
     * @return bool
     */
    public static function verify_nonce($nonce, $action) {
        return wp_verify_nonce($nonce, $action);
    }
    
    /**
     * 检查用户权限
     * 
     * @param string $capability 权限名称
     * @return bool
     */
    public static function check_capability($capability) {
        return current_user_can($capability);
    }
    
    /**
     * 防止SQL注入的查询准备
     * 
     * @param string $query SQL查询
     * @param array $args 参数
     * @return string
     */
    public static function prepare_query($query, $args) {
        global $wpdb;
        return $wpdb->prepare($query, $args);
    }
    
    /**
     * 清理文件名
     * 
     * @param string $filename 文件名
     * @return string
     */
    public static function sanitize_filename($filename) {
        $filename = sanitize_file_name($filename);
        
        // 移除潜在的危险字符
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        return $filename;
    }
    
    /**
     * 验证上传文件类型
     * 
     * @param string $filename 文件名
     * @param array $allowed_types 允许的文件类型
     * @return bool
     */
    public static function validate_file_type($filename, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp']) {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($ext, $allowed_types);
    }
    
    /**
     * 限制请求频率
     * 
     * @param string $key 限制键
     * @param int $limit 限制次数
     * @param int $window 时间窗口（秒）
     * @return bool
     */
    public static function rate_limit($key, $limit = 60, $window = 3600) {
        $transient_key = 'yinghe_rate_limit_' . md5($key . self::get_user_ip());
        $requests = get_transient($transient_key) ?: 0;
        
        if ($requests >= $limit) {
            return false;
        }
        
        set_transient($transient_key, $requests + 1, $window);
        return true;
    }
    
    /**
     * 记录安全事件
     * 
     * @param string $event 事件类型
     * @param string $description 事件描述
     * @param array $data 附加数据
     */
    public static function log_security_event($event, $description, $data = []) {
        $log_data = [
            'timestamp' => current_time('mysql'),
            'event' => $event,
            'description' => $description,
            'user_ip' => self::get_user_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'user_id' => get_current_user_id(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'data' => $data
        ];
        
        // 记录到 WordPress 日志
        error_log('YingheSecurity: ' . json_encode($log_data));
        
        // 如果启用了数据库日志记录
        if (get_option('yinghe_enable_security_log', false)) {
            // 这里可以添加数据库记录逻辑
        }
    }
    
    /**
     * 检查恶意请求
     * 
     * @param string $input 输入内容
     * @return bool
     */
    public static function is_malicious_request($input) {
        $malicious_patterns = [
            '/\<script[\s\S]*?\>[\s\S]*?\<\/script\>/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/on\w+\s*=/i',
            '/\<iframe/i',
            '/\<object/i',
            '/\<embed/i',
            '/eval\s*\(/i',
            '/expression\s*\(/i',
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
        ];
        
        foreach ($malicious_patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 清理输出数据
     * 
     * @param mixed $data 数据
     * @param string $context 上下文
     * @return mixed
     */
    public static function sanitize_output($data, $context = 'html') {
        if (is_array($data)) {
            return array_map(function($item) use ($context) {
                return self::sanitize_output($item, $context);
            }, $data);
        }
        
        if (!is_string($data)) {
            return $data;
        }
        
        switch ($context) {
            case 'html':
                return esc_html($data);
            case 'attr':
                return esc_attr($data);
            case 'url':
                return esc_url($data);
            case 'js':
                return esc_js($data);
            case 'textarea':
                return esc_textarea($data);
            default:
                return esc_html($data);
        }
    }
}