<?php
/**
 * 组件抽象基类
 * 
 * @package YingheTheme
 * @subpackage Components
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 组件抽象基类
 * 
 * 定义所有组件的基础接口和公共功能
 */
abstract class YingheAbstractComponent {
    protected $props;
    protected $component_name;
    protected $unique_id;
    protected static $instance_count = [];
    
    /**
     * 构造函数
     * 
     * @param array $props 组件属性
     */
    public function __construct($props = []) {
        $this->component_name = $this->get_component_name();
        $this->unique_id = $this->generate_unique_id();
        $this->props = $this->parse_props($props);
        
        $this->init();
        $this->register_assets();
    }
    
    /**
     * 获取组件名称
     * 
     * @return string
     */
    protected function get_component_name() {
        $class_name = get_class($this);
        $name = str_replace(['Yinghe', 'Component'], '', $class_name);
        return strtolower(preg_replace('/([A-Z])/', '-$1', lcfirst($name)));
    }
    
    /**
     * 生成唯一ID
     * 
     * @return string
     */
    protected function generate_unique_id() {
        if (!isset(self::$instance_count[$this->component_name])) {
            self::$instance_count[$this->component_name] = 0;
        }
        self::$instance_count[$this->component_name]++;
        
        return $this->component_name . '-' . self::$instance_count[$this->component_name];
    }
    
    /**
     * 解析组件属性
     * 
     * @param array $props 传入的属性
     * @return array 解析后的属性
     */
    protected function parse_props($props) {
        $defaults = $this->get_default_props();
        return wp_parse_args($props, $defaults);
    }
    
    /**
     * 组件初始化（子类可重写）
     */
    protected function init() {
        // 默认为空，子类按需实现
    }
    
    /**
     * 注册组件资源
     */
    protected function register_assets() {
        // 获取全局资源管理器实例
        $asset_manager = $GLOBALS['yinghe_asset_manager'] ?? null;
        
        if (!$asset_manager) {
            return;
        }
        
        // 注册组件样式
        if ($styles = $this->get_component_styles()) {
            $asset_manager->add_inline_style($this->component_name, $styles);
        }
        
        // 注册组件脚本
        if ($scripts = $this->get_component_scripts()) {
            $asset_manager->add_inline_script($this->component_name, $scripts);
        }
    }
    
    /**
     * 渲染组件
     * 
     * @return string 组件HTML
     */
    public function render() {
        ob_start();
        
        echo $this->before_render();
        $this->render_component();
        echo $this->after_render();
        
        return ob_get_clean();
    }
    
    /**
     * 静态渲染方法
     * 
     * @param array $props 组件属性
     * @return string 组件HTML
     */
    public static function render_static($props = []) {
        $instance = new static($props);
        return $instance->render();
    }
    
    /**
     * 渲染前钩子
     * 
     * @return string
     */
    protected function before_render() {
        return apply_filters("yinghe_before_render_{$this->component_name}", '', $this->props);
    }
    
    /**
     * 渲染后钩子
     * 
     * @return string
     */
    protected function after_render() {
        return apply_filters("yinghe_after_render_{$this->component_name}", '', $this->props);
    }
    
    /**
     * 获取组件CSS类名
     * 
     * @return string
     */
    protected function get_css_classes() {
        $classes = [
            'yinghe-component',
            "yinghe-{$this->component_name}",
        ];
        
        if (!empty($this->props['class'])) {
            $classes[] = $this->props['class'];
        }
        
        if (!empty($this->props['modifier'])) {
            $classes[] = "yinghe-{$this->component_name}--{$this->props['modifier']}";
        }
        
        return implode(' ', array_filter($classes));
    }
    
    /**
     * 获取组件属性字符串
     * 
     * @return string
     */
    protected function get_attributes() {
        $attributes = [
            'id' => $this->unique_id,
            'class' => $this->get_css_classes(),
        ];
        
        if (!empty($this->props['data'])) {
            foreach ($this->props['data'] as $key => $value) {
                $attributes["data-{$key}"] = esc_attr($value);
            }
        }
        
        $attr_string = '';
        foreach ($attributes as $name => $value) {
            $attr_string .= " {$name}=\"" . esc_attr($value) . '"';
        }
        
        return $attr_string;
    }
    
    /**
     * 安全输出HTML属性值
     * 
     * @param mixed $value 属性值
     * @return string
     */
    protected function esc_attr($value) {
        if (is_array($value) || is_object($value)) {
            return esc_attr(json_encode($value));
        }
        return esc_attr($value);
    }
    
    /**
     * 安全输出URL
     * 
     * @param string $url URL地址
     * @return string
     */
    protected function esc_url($url) {
        return esc_url($url);
    }
    
    /**
     * 安全输出HTML内容
     * 
     * @param string $content HTML内容
     * @return string
     */
    protected function esc_html($content) {
        return esc_html($content);
    }
    
    /**
     * 安全输出富文本内容
     * 
     * @param string $content 富文本内容
     * @return string
     */
    protected function wp_kses_post($content) {
        return wp_kses_post($content);
    }
    
    // 抽象方法，子类必须实现
    abstract protected function get_default_props();
    abstract protected function render_component();
    
    // 可选方法，子类按需重写
    protected function get_component_styles() {
        return '';
    }
    
    protected function get_component_scripts() {
        return '';
    }
}

/**
 * 组件接口定义
 */
interface YingheComponentInterface {
    public function render();
    public static function render_static($props = []);
}

/**
 * 可缓存组件特性
 */
trait YingheCacheableComponent {
    protected $cache_duration = HOUR_IN_SECONDS;
    protected $cache_group = 'yinghe_components';
    
    /**
     * 获取缓存的渲染结果
     * 
     * @return string
     */
    public function get_cached_render() {
        $cache_key = $this->get_cache_key();
        $cached_content = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $cached_content) {
            $cached_content = $this->render();
            wp_cache_set($cache_key, $cached_content, $this->cache_group, $this->cache_duration);
        }
        
        return $cached_content;
    }
    
    /**
     * 生成缓存键
     * 
     * @return string
     */
    protected function get_cache_key() {
        return md5($this->component_name . serialize($this->props));
    }
    
    /**
     * 清除组件缓存
     */
    public function clear_cache() {
        $cache_key = $this->get_cache_key();
        wp_cache_delete($cache_key, $this->cache_group);
    }
}

/**
 * AJAX 组件特性
 */
trait YingheAjaxComponent {
    
    /**
     * 注册AJAX处理器
     */
    protected function register_ajax_handlers() {
        $handlers = $this->get_ajax_handlers();
        
        foreach ($handlers as $action => $callback) {
            add_action("wp_ajax_{$action}", $callback);
            add_action("wp_ajax_nopriv_{$action}", $callback);
        }
    }
    
    /**
     * 获取AJAX处理器列表
     * 
     * @return array
     */
    protected function get_ajax_handlers() {
        return [];
    }
    
    /**
     * 验证AJAX nonce
     * 
     * @param string $nonce_action nonce动作名
     * @return bool
     */
    protected function verify_ajax_nonce($nonce_action = 'yinghe_ajax_nonce') {
        return wp_verify_nonce($_POST['nonce'] ?? '', $nonce_action);
    }
    
    /**
     * 发送AJAX成功响应
     * 
     * @param mixed $data 响应数据
     */
    protected function ajax_success($data = null) {
        wp_send_json_success($data);
    }
    
    /**
     * 发送AJAX错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 错误代码
     */
    protected function ajax_error($message, $code = 400) {
        wp_send_json_error(['message' => $message, 'code' => $code]);
    }
}