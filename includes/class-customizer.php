<?php
/**
 * 主题定制器类
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 主题定制器
 */
class YingheCustomizer {
    
    /**
     * 构造函数
     */
    public function __construct() {
        add_action('customize_register', [$this, 'register_customizer_options']);
        add_action('wp_head', [$this, 'output_customizer_css'], 999);
    }
    
    /**
     * 注册定制器选项
     * 
     * @param WP_Customize_Manager $wp_customize 定制器实例
     */
    public function register_customizer_options($wp_customize) {
        // 基础设置面板
        $wp_customize->add_panel('yinghe_basic', [
            'title' => __('硬核指南基础设置', 'yinghe'),
            'priority' => 10,
        ]);
        
        // Logo 设置
        $this->add_logo_section($wp_customize);
        
        // 布局设置
        $this->add_layout_section($wp_customize);
        
        // 主题设置
        $this->add_theme_section($wp_customize);
        
        // SEO 设置
        $this->add_seo_section($wp_customize);
        
        // 高级设置
        $this->add_advanced_section($wp_customize);
    }
    
    /**
     * Logo 设置
     * 
     * @param WP_Customize_Manager $wp_customize
     */
    private function add_logo_section($wp_customize) {
        $wp_customize->add_section('yinghe_logo', [
            'title' => __('Logo 设置', 'yinghe'),
            'panel' => 'yinghe_basic',
            'priority' => 10,
        ]);
        
        // 展开状态 Logo（浅色）
        $wp_customize->add_setting('logo_expanded_light', [
            'default' => get_template_directory_uri() . '/static/picture/logo-white-wide.webp',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'logo_expanded_light',
            [
                'label' => __('展开状态 Logo（浅色主题）', 'yinghe'),
                'section' => 'yinghe_logo',
                'settings' => 'logo_expanded_light',
            ]
        ));
        
        // 展开状态 Logo（深色）
        $wp_customize->add_setting('logo_expanded_dark', [
            'default' => get_template_directory_uri() . '/static/picture/logo-black-wide.webp',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'logo_expanded_dark',
            [
                'label' => __('展开状态 Logo（深色主题）', 'yinghe'),
                'section' => 'yinghe_logo',
                'settings' => 'logo_expanded_dark',
            ]
        ));
        
        // 收起状态 Logo（浅色）
        $wp_customize->add_setting('logo_collapsed_light', [
            'default' => get_template_directory_uri() . '/static/picture/logo-white.webp',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'logo_collapsed_light',
            [
                'label' => __('收起状态 Logo（浅色主题）', 'yinghe'),
                'section' => 'yinghe_logo',
                'settings' => 'logo_collapsed_light',
            ]
        ));
        
        // 收起状态 Logo（深色）
        $wp_customize->add_setting('logo_collapsed_dark', [
            'default' => get_template_directory_uri() . '/static/picture/logo-black.webp',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'logo_collapsed_dark',
            [
                'label' => __('收起状态 Logo（深色主题）', 'yinghe'),
                'section' => 'yinghe_logo',
                'settings' => 'logo_collapsed_dark',
            ]
        ));
        
        // 移动端 Logo（浅色）
        $wp_customize->add_setting('mobile_logo_light', [
            'default' => get_template_directory_uri() . '/static/picture/logo-white-wide.webp',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'mobile_logo_light',
            [
                'label' => __('移动端 Logo（浅色主题）', 'yinghe'),
                'section' => 'yinghe_logo',
                'settings' => 'mobile_logo_light',
            ]
        ));
        
        // 移动端 Logo（深色）
        $wp_customize->add_setting('mobile_logo_dark', [
            'default' => get_template_directory_uri() . '/static/picture/logo-black-wide.webp',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'mobile_logo_dark',
            [
                'label' => __('移动端 Logo（深色主题）', 'yinghe'),
                'section' => 'yinghe_logo',
                'settings' => 'mobile_logo_dark',
            ]
        ));
    }
    
    /**
     * 布局设置
     * 
     * @param WP_Customize_Manager $wp_customize
     */
    private function add_layout_section($wp_customize) {
        $wp_customize->add_section('yinghe_layout', [
            'title' => __('布局设置', 'yinghe'),
            'panel' => 'yinghe_basic',
            'priority' => 20,
        ]);
        
        // 侧边栏宽度
        $wp_customize->add_setting('sidebar_width', [
            'default' => 220,
            'sanitize_callback' => 'absint',
        ]);
        
        $wp_customize->add_control('sidebar_width', [
            'label' => __('侧边栏宽度（像素）', 'yinghe'),
            'section' => 'yinghe_layout',
            'type' => 'number',
            'input_attrs' => [
                'min' => 180,
                'max' => 300,
                'step' => 10,
            ],
        ]);
        
        // 容器最大宽度
        $wp_customize->add_setting('container_max_width', [
            'default' => 1900,
            'sanitize_callback' => 'absint',
        ]);
        
        $wp_customize->add_control('container_max_width', [
            'label' => __('容器最大宽度（像素）', 'yinghe'),
            'section' => 'yinghe_layout',
            'type' => 'number',
            'input_attrs' => [
                'min' => 1200,
                'max' => 2400,
                'step' => 100,
            ],
        ]);
        
        // 是否显示域名信息
        $wp_customize->add_setting('show_domain_info', [
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ]);
        
        $wp_customize->add_control('show_domain_info', [
            'label' => __('显示域名信息', 'yinghe'),
            'section' => 'yinghe_layout',
            'type' => 'checkbox',
        ]);
    }
    
    /**
     * 主题设置
     * 
     * @param WP_Customize_Manager $wp_customize
     */
    private function add_theme_section($wp_customize) {
        $wp_customize->add_section('yinghe_theme', [
            'title' => __('主题设置', 'yinghe'),
            'panel' => 'yinghe_basic',
            'priority' => 30,
        ]);
        
        // 默认主题模式
        $wp_customize->add_setting('default_theme_mode', [
            'default' => 'light',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        
        $wp_customize->add_control('default_theme_mode', [
            'label' => __('默认主题模式', 'yinghe'),
            'section' => 'yinghe_theme',
            'type' => 'select',
            'choices' => [
                'light' => __('浅色模式', 'yinghe'),
                'dark' => __('深色模式', 'yinghe'),
                'auto' => __('跟随系统', 'yinghe'),
            ],
        ]);
        
        // 启用动画
        $wp_customize->add_setting('enable_animations', [
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ]);
        
        $wp_customize->add_control('enable_animations', [
            'label' => __('启用动画效果', 'yinghe'),
            'section' => 'yinghe_theme',
            'type' => 'checkbox',
        ]);
        
        // 启用懒加载
        $wp_customize->add_setting('enable_lazy_loading', [
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ]);
        
        $wp_customize->add_control('enable_lazy_loading', [
            'label' => __('启用图片懒加载', 'yinghe'),
            'section' => 'yinghe_theme',
            'type' => 'checkbox',
        ]);
        
        // 启用访问统计
        $wp_customize->add_setting('enable_visit_tracking', [
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ]);
        
        $wp_customize->add_control('enable_visit_tracking', [
            'label' => __('启用访问统计', 'yinghe'),
            'section' => 'yinghe_theme',
            'type' => 'checkbox',
        ]);
        
        // 显示访问数量
        $wp_customize->add_setting('show_visit_count', [
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ]);
        
        $wp_customize->add_control('show_visit_count', [
            'label' => __('显示访问数量', 'yinghe'),
            'section' => 'yinghe_theme',
            'type' => 'checkbox',
        ]);
        
        // 微信二维码图片
        $wp_customize->add_setting('wechat_qr_image', [
            'default' => get_template_directory_uri() . '/static/picture/IjYcrkGkj4ezaO9-lPbh4g37fac9be71fe9602ae079ef28e0cba33.png',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'wechat_qr_image',
            [
                'label' => __('微信二维码图片', 'yinghe'),
                'section' => 'yinghe_theme',
                'settings' => 'wechat_qr_image',
            ]
        ));
    }
    
    /**
     * SEO 设置
     * 
     * @param WP_Customize_Manager $wp_customize
     */
    private function add_seo_section($wp_customize) {
        $wp_customize->add_section('yinghe_seo', [
            'title' => __('SEO 设置', 'yinghe'),
            'panel' => 'yinghe_basic',
            'priority' => 40,
        ]);
        
        // 网站关键词
        $wp_customize->add_setting('site_keywords', [
            'default' => '硬核指南,影视导航,免费影音',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        
        $wp_customize->add_control('site_keywords', [
            'label' => __('网站关键词', 'yinghe'),
            'section' => 'yinghe_seo',
            'type' => 'text',
        ]);
        
        // 网站图标
        $wp_customize->add_setting('favicon', [
            'default' => get_template_directory_uri() . '/static/picture/favicon.png',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'favicon',
            [
                'label' => __('网站图标', 'yinghe'),
                'section' => 'yinghe_seo',
                'settings' => 'favicon',
            ]
        ));
    }
    
    /**
     * 高级设置
     * 
     * @param WP_Customize_Manager $wp_customize
     */
    private function add_advanced_section($wp_customize) {
        $wp_customize->add_section('yinghe_advanced', [
            'title' => __('高级设置', 'yinghe'),
            'panel' => 'yinghe_basic',
            'priority' => 50,
        ]);
        
        // 自定义头部代码
        $wp_customize->add_setting('custom_head_code', [
            'default' => '',
            'sanitize_callback' => 'wp_kses_post',
        ]);
        
        $wp_customize->add_control('custom_head_code', [
            'label' => __('自定义头部代码', 'yinghe'),
            'section' => 'yinghe_advanced',
            'type' => 'textarea',
            'description' => __('在 &lt;head&gt; 标签中添加自定义代码', 'yinghe'),
        ]);
        
        // 自定义底部代码
        $wp_customize->add_setting('custom_footer_code', [
            'default' => '',
            'sanitize_callback' => 'wp_kses_post',
        ]);
        
        $wp_customize->add_control('custom_footer_code', [
            'label' => __('自定义底部代码', 'yinghe'),
            'section' => 'yinghe_advanced',
            'type' => 'textarea',
            'description' => __('在 &lt;/body&gt; 标签前添加自定义代码', 'yinghe'),
        ]);
        
        // 统计代码
        $wp_customize->add_setting('analytics_code', [
            'default' => '',
            'sanitize_callback' => 'wp_kses_post',
        ]);
        
        $wp_customize->add_control('analytics_code', [
            'label' => __('统计代码', 'yinghe'),
            'section' => 'yinghe_advanced',
            'type' => 'textarea',
            'description' => __('添加 Google Analytics 或其他统计代码', 'yinghe'),
        ]);
        
        // 版权信息
        $wp_customize->add_setting('copyright_text', [
            'default' => __('保留所有权利。', 'yinghe'),
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        
        $wp_customize->add_control('copyright_text', [
            'label' => __('版权信息', 'yinghe'),
            'section' => 'yinghe_advanced',
            'type' => 'text',
        ]);
        
        // ICP备案号
        $wp_customize->add_setting('icp_number', [
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        
        $wp_customize->add_control('icp_number', [
            'label' => __('ICP备案号', 'yinghe'),
            'section' => 'yinghe_advanced',
            'type' => 'text',
        ]);
        
        // 显示主题署名
        $wp_customize->add_setting('show_theme_credit', [
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ]);
        
        $wp_customize->add_control('show_theme_credit', [
            'label' => __('显示主题署名', 'yinghe'),
            'section' => 'yinghe_advanced',
            'type' => 'checkbox',
        ]);
    }
    
    /**
     * 输出定制器CSS
     */
    public function output_customizer_css() {
        $sidebar_width = get_theme_mod('sidebar_width', 220);
        $container_max_width = get_theme_mod('container_max_width', 1900);
        
        echo "<style type='text/css'>\n";
        echo ":root {\n";
        echo "  --yinghe-sidebar-width: {$sidebar_width}px;\n";
        echo "  --yinghe-container-max-width: {$container_max_width}px;\n";
        echo "}\n";
        echo "</style>\n";
    }
}