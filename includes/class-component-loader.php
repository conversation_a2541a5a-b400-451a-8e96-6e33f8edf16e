<?php
/**
 * 组件加载器类
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 组件加载和管理系统
 */
class YingheComponentLoader {
    private static $instance = null;
    private $loaded_components = [];
    private $component_registry = [];
    
    /**
     * 获取单例实例
     * 
     * @return YingheComponentLoader
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->register_component_paths();
        $this->load_all_components(); // 立即加载组件
    }
    
    /**
     * 注册组件路径
     */
    private function register_component_paths() {
        $this->component_registry = [
            'layout' => YINGHE_COMPONENTS_DIR . '/layout/',
            'content' => YINGHE_COMPONENTS_DIR . '/content/',
            'forms' => YINGHE_COMPONENTS_DIR . '/forms/',
            'navigation' => YINGHE_COMPONENTS_DIR . '/navigation/',
            'overlays' => YINGHE_COMPONENTS_DIR . '/overlays/',
            'utilities' => YINGHE_COMPONENTS_DIR . '/utilities/',
        ];
        
        // 允许主题或插件修改组件路径
        $this->component_registry = apply_filters('yinghe_component_registry', $this->component_registry);
    }
    
    /**
     * 加载所有组件
     */
    public function load_all_components() {
        foreach ($this->component_registry as $category => $path) {
            $this->load_components_from_directory($path, $category);
        }
        
        do_action('yinghe_components_loaded');
    }
    
    /**
     * 从目录加载组件
     * 
     * @param string $directory 目录路径
     * @param string $category 组件分类
     */
    private function load_components_from_directory($directory, $category) {
        if (!is_dir($directory)) {
            return;
        }
        
        $files = glob($directory . '*.php');
        foreach ($files as $file) {
            $component_name = basename($file, '.php');
            $this->load_component($component_name, $file, $category);
        }
    }
    
    /**
     * 加载单个组件
     * 
     * @param string $name 组件名称
     * @param string $file_path 文件路径
     * @param string $category 组件分类
     * @return bool
     */
    public function load_component($name, $file_path, $category = '') {
        if (isset($this->loaded_components[$name])) {
            return true; // 已加载，返回成功
        }
        
        if (!file_exists($file_path)) {
            error_log("Component file not found: {$file_path}");
            return false;
        }
        
        try {
            require_once $file_path;
            
            $this->loaded_components[$name] = [
                'path' => $file_path,
                'category' => $category,
                'loaded_at' => current_time('timestamp')
            ];
            
            do_action('yinghe_component_loaded', $name, $file_path, $category);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error loading component {$name}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取已加载的组件列表
     * 
     * @return array
     */
    public function get_loaded_components() {
        return $this->loaded_components;
    }
    
    /**
     * 检查组件是否已加载
     * 
     * @param string $name 组件名称
     * @return bool
     */
    public function is_component_loaded($name) {
        return isset($this->loaded_components[$name]);
    }
    
    /**
     * 重新加载组件
     * 
     * @param string $name 组件名称
     * @return bool
     */
    public function reload_component($name) {
        if (!isset($this->loaded_components[$name])) {
            return false;
        }
        
        $component_info = $this->loaded_components[$name];
        unset($this->loaded_components[$name]);
        
        return $this->load_component($name, $component_info['path'], $component_info['category']);
    }
    
    /**
     * 获取组件信息
     * 
     * @param string $name 组件名称
     * @return array|null
     */
    public function get_component_info($name) {
        return $this->loaded_components[$name] ?? null;
    }
    
    /**
     * 按分类获取组件
     * 
     * @param string $category 分类名称
     * @return array
     */
    public function get_components_by_category($category) {
        $components = [];
        
        foreach ($this->loaded_components as $name => $info) {
            if ($info['category'] === $category) {
                $components[$name] = $info;
            }
        }
        
        return $components;
    }
    
    /**
     * 注册组件别名
     * 
     * @param string $alias 别名
     * @param string $component_name 组件名称
     */
    public function register_component_alias($alias, $component_name) {
        $this->component_aliases[$alias] = $component_name;
    }
    
    /**
     * 解析组件别名
     * 
     * @param string $name 组件名称或别名
     * @return string
     */
    public function resolve_component_name($name) {
        return $this->component_aliases[$name] ?? $name;
    }
}

/**
 * 获取组件加载器实例
 * 
 * @return YingheComponentLoader
 */
function yinghe_get_component_loader() {
    return YingheComponentLoader::get_instance();
}

/**
 * 检查组件是否可用
 * 
 * @param string $component_name 组件名称
 * @return bool
 */
function yinghe_component_exists($component_name) {
    $loader = YingheComponentLoader::get_instance();
    return $loader->is_component_loaded($component_name);
}

/**
 * 安全渲染组件
 * 
 * @param string $component_class 组件类名
 * @param array $props 组件属性
 * @return string
 */
function yinghe_safe_render_component($component_class, $props = []) {
    if (!class_exists($component_class)) {
        if (WP_DEBUG) {
            return "<!-- Component class not found: {$component_class} -->";
        }
        return '';
    }
    
    try {
        return call_user_func([$component_class, 'render_static'], $props);
    } catch (Exception $e) {
        if (WP_DEBUG) {
            return "<!-- Component error: " . esc_html($e->getMessage()) . " -->";
        }
        error_log("Component rendering error: " . $e->getMessage());
        return '';
    }
}