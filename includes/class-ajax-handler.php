<?php
/**
 * AJAX 处理器类
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX 请求处理器
 */
class YingheAjaxHandler {
    
    /**
     * 构造函数
     */
    public function __construct() {
        // 注册 AJAX 处理器
        add_action('wp_ajax_yinghe_record_visit', [$this, 'record_site_visit']);
        add_action('wp_ajax_nopriv_yinghe_record_visit', [$this, 'record_site_visit']);
        
        add_action('wp_ajax_yinghe_record_search', [$this, 'record_search']);
        add_action('wp_ajax_nopriv_yinghe_record_search', [$this, 'record_search']);
        
        add_action('wp_ajax_yinghe_get_site_info', [$this, 'get_site_info']);
        add_action('wp_ajax_nopriv_yinghe_get_site_info', [$this, 'get_site_info']);
        
        add_action('wp_ajax_yinghe_save_theme_preference', [$this, 'save_theme_preference']);
        
        // 跳转页面处理
        add_action('init', [$this, 'handle_go_redirect']);
    }
    
    /**
     * 记录网站访问
     */
    public function record_site_visit() {
        // 验证 nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'yinghe_ajax_nonce')) {
            wp_send_json_error('安全验证失败');
            return;
        }
        
        // 验证必要参数
        $site_id = intval($_POST['site_id'] ?? 0);
        $site_url = sanitize_url($_POST['site_url'] ?? '');
        
        if (!$site_id || !$site_url) {
            wp_send_json_error('参数不完整');
            return;
        }
        
        // 验证站点ID
        $post = get_post($site_id);
        if (!$post || $post->post_type !== 'sites') {
            wp_send_json_error('无效的站点ID');
            return;
        }
        
        // 收集访问数据
        $visit_data = [
            'site_id' => $site_id,
            'user_id' => get_current_user_id(),
            'user_ip' => YingheSecurity::get_user_ip(),
            'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
            'page_url' => sanitize_url($_POST['page_url'] ?? ''),
            'referer_url' => sanitize_url($_POST['referer_url'] ?? ''),
            'device_type' => wp_is_mobile() ? 'mobile' : 'desktop',
            'visit_time' => current_time('mysql'),
        ];
        
        // 记录访问
        $result = $this->log_visit($visit_data);
        
        if ($result) {
            wp_send_json_success('访问记录成功');
        } else {
            wp_send_json_error('记录访问失败');
        }
    }
    
    /**
     * 记录搜索行为
     */
    public function record_search() {
        // 验证 nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'yinghe_ajax_nonce')) {
            wp_send_json_error('安全验证失败');
            return;
        }
        
        $engine = sanitize_text_field($_POST['engine'] ?? '');
        $query = sanitize_text_field($_POST['query'] ?? '');
        
        if (!$engine || !$query) {
            wp_send_json_error('参数不完整');
            return;
        }
        
        // 更新搜索引擎使用统计
        global $wpdb;
        $table = $wpdb->prefix . 'yinghe_search_engines';
        
        $wpdb->query($wpdb->prepare("
            UPDATE $table 
            SET usage_count = usage_count + 1, 
                last_used = NOW() 
            WHERE name = %s
        ", $engine));
        
        wp_send_json_success('搜索记录成功');
    }
    
    /**
     * 获取网站信息
     */
    public function get_site_info() {
        $site_id = intval($_GET['site_id'] ?? 0);
        
        if (!$site_id) {
            wp_send_json_error('站点ID不能为空');
            return;
        }
        
        $post = get_post($site_id);
        if (!$post || $post->post_type !== 'sites') {
            wp_send_json_error('站点不存在');
            return;
        }
        
        $site_info = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'description' => $post->post_excerpt ?: $post->post_content,
            'url' => get_post_meta($post->ID, 'site_url', true),
            'icon' => get_post_meta($post->ID, 'site_icon', true),
            'visit_count' => yinghe_get_site_visit_count($post->ID, 'total'),
            'visit_count_today' => yinghe_get_site_visit_count($post->ID, 'today'),
            'tags' => wp_get_post_terms($post->ID, 'sitetag', ['fields' => 'names']),
            'categories' => wp_get_post_terms($post->ID, 'site_category', ['fields' => 'names']),
        ];
        
        wp_send_json_success($site_info);
    }
    
    /**
     * 处理跳转页面
     */
    public function handle_go_redirect() {
        // 检查是否是跳转请求
        if (!isset($_GET['url'])) {
            return;
        }
        
        $encoded_url = $_GET['url'];
        $target_url = base64_decode($encoded_url);
        
        // 验证URL有效性
        if (!filter_var($target_url, FILTER_VALIDATE_URL)) {
            wp_die('无效的URL地址');
        }
        
        // 查找对应的网站记录
        global $wpdb;
        $site_id = $wpdb->get_var($wpdb->prepare("
            SELECT post_id FROM {$wpdb->postmeta} 
            WHERE meta_key = 'site_url' 
            AND meta_value = %s 
            LIMIT 1
        ", $target_url));
        
        if ($site_id) {
            // 记录访问
            $visit_data = [
                'site_id' => $site_id,
                'user_id' => get_current_user_id(),
                'user_ip' => YingheSecurity::get_user_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'page_url' => home_url($_SERVER['REQUEST_URI']),
                'referer_url' => sanitize_url($_SERVER['HTTP_REFERER'] ?? ''),
                'device_type' => wp_is_mobile() ? 'mobile' : 'desktop',
                'visit_time' => current_time('mysql'),
            ];
            
            $this->log_visit($visit_data);
        }
        
        // 执行跳转
        wp_redirect($target_url);
        exit;
    }
    
    /**
     * 记录访问到数据库
     * 
     * @param array $visit_data 访问数据
     * @return bool
     */
    private function log_visit($visit_data) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'yinghe_site_visits';
        
        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
            return false;
        }
        
        // 防止重复记录（同一IP在5分钟内访问同一站点）
        $recent_visit = $wpdb->get_var($wpdb->prepare("
            SELECT id FROM $table 
            WHERE site_id = %d 
            AND user_ip = %s 
            AND visit_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            LIMIT 1
        ", $visit_data['site_id'], $visit_data['user_ip']));
        
        if ($recent_visit) {
            return true; // 不记录重复访问，但返回成功
        }
        
        // 插入访问记录
        $result = $wpdb->insert($table, $visit_data);
        
        if ($result) {
            // 更新文章的访问统计缓存
            $this->update_visit_count_cache($visit_data['site_id']);
        }
        
        return $result !== false;
    }
    
    /**
     * 更新访问统计缓存
     * 
     * @param int $site_id 站点ID
     */
    private function update_visit_count_cache($site_id) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'yinghe_site_visits';
        
        // 计算各种时期的访问量
        $counts = $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN DATE(visit_time) = CURDATE() THEN 1 ELSE 0 END) as today,
                SUM(CASE WHEN visit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as week,
                SUM(CASE WHEN visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as month
            FROM $table 
            WHERE site_id = %d
        ", $site_id));
        
        if ($counts) {
            update_post_meta($site_id, 'visit_count_total', $counts->total);
            update_post_meta($site_id, 'visit_count_today', $counts->today);
            update_post_meta($site_id, 'visit_count_week', $counts->week);
            update_post_meta($site_id, 'visit_count_month', $counts->month);
            update_post_meta($site_id, 'last_visit_time', current_time('mysql'));
        }
    }
    
    /**
     * 验证请求频率限制
     * 
     * @param string $action 动作名称
     * @param int $limit 限制次数
     * @param int $window 时间窗口（秒）
     * @return bool
     */
    private function check_rate_limit($action, $limit = 10, $window = 60) {
        $ip = YingheSecurity::get_user_ip();
        $key = "yinghe_rate_limit_{$action}_{$ip}";
        
        $requests = get_transient($key) ?: 0;
        
        if ($requests >= $limit) {
            return false;
        }
        
        set_transient($key, $requests + 1, $window);
        return true;
    }
    
    /**
     * 保存主题偏好设置
     */
    public function save_theme_preference() {
        // 验证用户是否登录
        if (!is_user_logged_in()) {
            wp_send_json_error('用户未登录');
            return;
        }
        
        // 验证 nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'yinghe_ajax_nonce')) {
            wp_send_json_error('安全验证失败');
            return;
        }
        
        $theme = sanitize_text_field($_POST['theme'] ?? '');
        
        if (!in_array($theme, ['light', 'dark', 'auto'])) {
            wp_send_json_error('无效的主题选项');
            return;
        }
        
        // 保存用户偏好
        $user_id = get_current_user_id();
        $result = update_user_meta($user_id, 'yinghe_theme_mode', $theme);
        
        if ($result !== false) {
            wp_send_json_success('主题偏好已保存');
        } else {
            wp_send_json_error('保存失败');
        }
    }
}