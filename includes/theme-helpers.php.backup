<?php
/**
 * 硬核指南主题 - 助手函数
 * 
 * @package YingheTheme
 * @subpackage Includes
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 获取网站访问统计数据
 * 
 * @param int $site_id 网站ID
 * @param string $period 统计周期 (total, today, week, month)
 * @return int
 */
if (!function_exists('yinghe_get_site_visit_count')) {
function yinghe_get_site_visit_count($site_id, $period = 'total') {
    if (!$site_id) {
        return 0;
    }
    
    // 首先尝试从缓存获取
    $cache_key = "visit_count_{$period}";
    $cached_count = get_post_meta($site_id, $cache_key, true);
    
    if ($cached_count !== '') {
        return intval($cached_count);
    }
    
    // 从数据库获取
    global $wpdb;
    $table = $wpdb->prefix . 'yinghe_site_visits';
    
    // 检查表是否存在
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
        return 0;
    }
    
    $where_clause = "WHERE site_id = %d";
    $params = [$site_id];
    
    switch ($period) {
        case 'today':
            $where_clause .= " AND DATE(visit_time) = CURDATE()";
            break;
        case 'week':
            $where_clause .= " AND visit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
        case 'month':
            $where_clause .= " AND visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
        case 'total':
        default:
            // 无额外条件
            break;
    }
    
    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table $where_clause",
        $params
    ));
    
    // 缓存结果（30分钟）
    update_post_meta($site_id, $cache_key, $count);
    
    return intval($count);
}
}

/**
 * 获取热门网站列表
 * 
 * @param int $limit 限制数量
 * @param string $period 统计周期
 * @return array
 */
if (!function_exists('yinghe_get_popular_sites')) {
function yinghe_get_popular_sites($limit = 10, $period = 'week') {
    global $wpdb;
    
    $cache_key = "popular_sites_{$period}_{$limit}";
    $cached_sites = get_transient($cache_key);
    
    if ($cached_sites !== false) {
        return $cached_sites;
    }
    
    $visit_table = $wpdb->prefix . 'yinghe_site_visits';
    
    // 检查访问表是否存在
    if ($wpdb->get_var("SHOW TABLES LIKE '$visit_table'") != $visit_table) {
        return [];
    }
    
    $date_condition = '';
    switch ($period) {
        case 'today':
            $date_condition = "AND DATE(v.visit_time) = CURDATE()";
            break;
        case 'week':
            $date_condition = "AND v.visit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
        case 'month':
            $date_condition = "AND v.visit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
    }
    
    $query = $wpdb->prepare("
        SELECT p.ID, p.post_title, COUNT(v.id) as visit_count
        FROM {$wpdb->posts} p
        LEFT JOIN $visit_table v ON p.ID = v.site_id
        WHERE p.post_type = 'sites' 
        AND p.post_status = 'publish'
        $date_condition
        GROUP BY p.ID
        ORDER BY visit_count DESC
        LIMIT %d
    ", $limit);
    
    $sites = $wpdb->get_results($query);
    
    // 缓存结果（1小时）
    set_transient($cache_key, $sites, HOUR_IN_SECONDS);
    
    return $sites;
}

/**
 * 获取分类下的网站数量
 * 
 * @param int $category_id 分类ID
 * @return int
 */
function yinghe_get_category_site_count($category_id) {
    if (!$category_id) {
        return 0;
    }
    
    $args = [
        'post_type' => 'sites',
        'post_status' => 'publish',
        'tax_query' => [
            [
                'taxonomy' => 'site_category',
                'field' => 'term_id',
                'terms' => $category_id,
            ],
        ],
        'fields' => 'ids',
        'posts_per_page' => -1,
    ];
    
    $sites = get_posts($args);
    return count($sites);
}

/**
 * 获取搜索引擎列表
 * 
 * @param bool $active_only 是否只获取激活的
 * @return array
 */
function yinghe_get_search_engines($active_only = true) {
    global $wpdb;
    
    $cache_key = $active_only ? 'search_engines_active' : 'search_engines_all';
    $cached_engines = get_transient($cache_key);
    
    if ($cached_engines !== false) {
        return $cached_engines;
    }
    
    $table = $wpdb->prefix . 'yinghe_search_engines';
    
    // 检查表是否存在
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
        return yinghe_get_default_search_engines();
    }
    
    $where_clause = $active_only ? "WHERE is_active = 1" : "";
    $engines = $wpdb->get_results("
        SELECT * FROM $table 
        $where_clause
        ORDER BY sort_order ASC, name ASC
    ");
    
    // 缓存结果（6小时）
    set_transient($cache_key, $engines, 6 * HOUR_IN_SECONDS);
    
    return $engines;
}

/**
 * 获取默认搜索引擎列表
 * 
 * @return array
 */
function yinghe_get_default_search_engines() {
    return [
        (object) [
            'name' => '百度',
            'url' => 'https://www.baidu.com/s?wd={query}',
            'icon' => get_template_directory_uri() . '/static/image/baidupc.png',
            'is_active' => 1,
            'sort_order' => 1,
        ],
        (object) [
            'name' => '360搜索',
            'url' => 'https://www.so.com/s?q={query}',
            'icon' => get_template_directory_uri() . '/static/image/360.png',
            'is_active' => 1,
            'sort_order' => 2,
        ],
        (object) [
            'name' => '搜狗',
            'url' => 'https://www.sogou.com/web?query={query}',
            'icon' => get_template_directory_uri() . '/static/image/sm.png',
            'is_active' => 1,
            'sort_order' => 3,
        ],
        (object) [
            'name' => '头条搜索',
            'url' => 'https://so.toutiao.com/search?keyword={query}',
            'icon' => get_template_directory_uri() . '/static/image/toutiao.png',
            'is_active' => 1,
            'sort_order' => 4,
        ],
    ];
}

/**
 * 格式化访问数量
 * 
 * @param int $count 数量
 * @return string
 */
function yinghe_format_visit_count($count) {
    if ($count < 1000) {
        return $count;
    } elseif ($count < 10000) {
        return round($count / 1000, 1) . 'K';
    } elseif ($count < 100000) {
        return round($count / 10000, 1) . '万';
    } else {
        return round($count / 10000) . '万';
    }
}

/**
 * 获取站点图标URL
 * 
 * @param int $site_id 站点ID
 * @param string $size 尺寸 (small, medium, large)
 * @return string
 */
function yinghe_get_site_icon($site_id, $size = 'medium') {
    if (!$site_id) {
        return get_template_directory_uri() . '/static/picture/favicon.png';
    }
    
    $icon_url = get_post_meta($site_id, 'site_icon', true);
    
    if (empty($icon_url)) {
        // 尝试从站点URL获取favicon
        $site_url = get_post_meta($site_id, 'site_url', true);
        if ($site_url) {
            $parsed_url = parse_url($site_url);
            if (isset($parsed_url['host'])) {
                $icon_url = "https://www.google.com/s2/favicons?domain={$parsed_url['host']}&sz=64";
            }
        }
    }
    
    // 如果仍然没有图标，使用默认图标
    if (empty($icon_url)) {
        $icon_url = get_template_directory_uri() . '/static/picture/favicon.png';
    }
    
    return $icon_url;
}

/**
 * 检查网站是否为特色推荐
 * 
 * @param int $site_id 站点ID
 * @return bool
 */
function yinghe_is_featured_site($site_id) {
    if (!$site_id) {
        return false;
    }
    
    $featured = get_post_meta($site_id, 'is_featured', true);
    return !empty($featured);
}

/**
 * 获取网站标签
 * 
 * @param int $site_id 站点ID
 * @param string $field 返回字段 (names, objects, ids)
 * @return array
 */
function yinghe_get_site_tags($site_id, $field = 'names') {
    if (!$site_id) {
        return [];
    }
    
    $terms = get_the_terms($site_id, 'sitetag');
    
    if (!$terms || is_wp_error($terms)) {
        return [];
    }
    
    switch ($field) {
        case 'names':
            return wp_list_pluck($terms, 'name');
        case 'ids':
            return wp_list_pluck($terms, 'term_id');
        case 'objects':
        default:
            return $terms;
    }
}

/**
 * 生成网站跳转链接
 * 
 * @param int $site_id 站点ID
 * @param bool $enable_tracking 是否启用统计
 * @return string
 */
function yinghe_get_site_link($site_id, $enable_tracking = true) {
    if (!$site_id) {
        return '#';
    }
    
    $site_url = get_post_meta($site_id, 'site_url', true);
    
    if (empty($site_url)) {
        return '#';
    }
    
    if (!$enable_tracking) {
        return $site_url;
    }
    
    // 使用跳转页面进行访问统计
    return home_url("/go/?url=" . base64_encode($site_url));
}

/**
 * 获取面包屑导航
 * 
 * @return array
 */
function yinghe_get_breadcrumbs() {
    $breadcrumbs = [];
    
    // 首页
    $breadcrumbs[] = [
        'title' => '首页',
        'url' => home_url('/'),
        'is_current' => is_front_page(),
    ];
    
    if (is_tax()) {
        $term = get_queried_object();
        $breadcrumbs[] = [
            'title' => $term->name,
            'url' => get_term_link($term),
            'is_current' => true,
        ];
    } elseif (is_single() && get_post_type() === 'sites') {
        $categories = get_the_terms(get_the_ID(), 'site_category');
        if ($categories && !is_wp_error($categories)) {
            $category = current($categories);
            $breadcrumbs[] = [
                'title' => $category->name,
                'url' => get_term_link($category),
                'is_current' => false,
            ];
        }
        
        $breadcrumbs[] = [
            'title' => get_the_title(),
            'url' => get_permalink(),
            'is_current' => true,
        ];
    }
    
    return $breadcrumbs;
}

/**
 * 获取相关网站
 * 
 * @param int $site_id 当前站点ID
 * @param int $limit 限制数量
 * @return array
 */
function yinghe_get_related_sites($site_id, $limit = 6) {
    if (!$site_id) {
        return [];
    }
    
    // 获取当前站点的分类
    $categories = get_the_terms($site_id, 'site_category');
    
    if (!$categories || is_wp_error($categories)) {
        return [];
    }
    
    $category_ids = wp_list_pluck($categories, 'term_id');
    
    $args = [
        'post_type' => 'sites',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'post__not_in' => [$site_id],
        'tax_query' => [
            [
                'taxonomy' => 'site_category',
                'field' => 'term_id',
                'terms' => $category_ids,
                'operator' => 'IN',
            ],
        ],
        'meta_query' => [
            [
                'key' => 'site_url',
                'compare' => 'EXISTS',
            ],
        ],
        'orderby' => 'rand',
    ];
    
    return get_posts($args);
}

/**
 * 清理过期的缓存数据
 */
function yinghe_cleanup_expired_cache() {
    global $wpdb;
    
    // 清理过期的访问统计缓存
    $wpdb->query("
        DELETE FROM {$wpdb->postmeta} 
        WHERE meta_key LIKE 'visit_count_%' 
        AND meta_value = ''
    ");
    
    // 清理过期的 transients
    $wpdb->query("
        DELETE FROM {$wpdb->options} 
        WHERE option_name LIKE '_transient_timeout_yinghe_%' 
        AND option_value < UNIX_TIMESTAMP()
    ");
    
    $wpdb->query("
        DELETE FROM {$wpdb->options} 
        WHERE option_name LIKE '_transient_yinghe_%' 
        AND option_name NOT IN (
            SELECT CONCAT('_transient_', SUBSTRING(option_name, 19))
            FROM {$wpdb->options} t2 
            WHERE t2.option_name LIKE '_transient_timeout_yinghe_%'
        )
    ");
}

/**
 * 注册清理任务
 */
function yinghe_schedule_cleanup() {
    if (!wp_next_scheduled('yinghe_cleanup_cache')) {
        wp_schedule_event(time(), 'daily', 'yinghe_cleanup_cache');
    }
}
add_action('wp', 'yinghe_schedule_cleanup');
add_action('yinghe_cleanup_cache', 'yinghe_cleanup_expired_cache');

/**
 * 获取主题版本号
 * 
 * @return string
 */
function yinghe_get_theme_version() {
    $theme = wp_get_theme();
    return $theme->get('Version');
}

/**
 * 输出内联 CSS 变量
 */
function yinghe_output_css_vars() {
    $vars = [
        '--yinghe-sidebar-width' => get_theme_mod('sidebar_width', 220) . 'px',
        '--yinghe-container-max-width' => get_theme_mod('container_max_width', 1900) . 'px',
        '--yinghe-border-radius' => '8px',
        '--yinghe-transition' => 'all 0.3s ease',
        '--yinghe-transition-fast' => 'all 0.15s ease',
        '--yinghe-shadow' => '0 2px 8px rgba(0,0,0,0.1)',
        '--yinghe-shadow-lg' => '0 8px 25px rgba(0,0,0,0.15)',
    ];
    
    echo "<style id='yinghe-css-vars'>\n:root {\n";
    foreach ($vars as $property => $value) {
        echo "  {$property}: {$value};\n";
    }
    echo "}\n</style>\n";
}
add_action('wp_head', 'yinghe_output_css_vars', 1);

/**
 * 添加主题支持的功能
 */
function yinghe_theme_support() {
    // 添加主题特色图像支持
    add_theme_support('post-thumbnails');
    
    // 添加菜单支持
    add_theme_support('menus');
    
    // 添加标题标签支持
    add_theme_support('title-tag');
    
    // 添加自定义logo支持
    add_theme_support('custom-logo', [
        'height' => 100,
        'width' => 400,
        'flex-height' => true,
        'flex-width' => true,
    ]);
    
    // 添加HTML5支持
    add_theme_support('html5', [
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ]);
    
    // 添加自定义背景支持
    add_theme_support('custom-background');
}
add_action('after_setup_theme', 'yinghe_theme_support');

/**
 * 注册小工具区域
 */
function yinghe_register_widget_areas() {
    register_sidebar([
        'name' => __('底部小工具', 'yinghe'),
        'id' => 'footer-widgets',
        'description' => __('显示在页面底部的小工具区域', 'yinghe'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ]);
    
    register_sidebar([
        'name' => __('侧边栏小工具', 'yinghe'),
        'id' => 'sidebar-widgets',
        'description' => __('显示在侧边栏的小工具区域', 'yinghe'),
        'before_widget' => '<div id="%1$s" class="sidebar-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="sidebar-widget-title">',
        'after_title' => '</h4>',
    ]);
}
add_action('widgets_init', 'yinghe_register_widget_areas');