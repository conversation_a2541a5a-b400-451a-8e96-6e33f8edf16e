ℹ️  [2025-08-02 01:56:10] INFO: 🚀 Logger initialized | {"metadata":{"type":"startup","directory":"/app/logs","maxSize":"10m","maxFiles":5,"envOverride":false}}
ℹ️  [2025-08-02 01:56:10] INFO: ✅ Model pricing data loaded successfully | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 🔄 Connecting to Redis... | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 🔗 Redis connected successfully | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: ✅ ✅ Redis connected successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 01:56:10] INFO: 🔄 Initializing pricing service... | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 💰 Loaded pricing data for 1257 models from cache | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: ✅ 💰 Pricing service initialized successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 01:56:10] INFO: 🔄 Initializing admin credentials... | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: ✅ ✅ Admin credentials loaded from init.json (single source of truth) | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 01:56:10] INFO: 📋 Admin username: cr_admin | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: ✅ ✅ Application initialized successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 01:56:10] INFO: ⏱️  Server timeout set to 600000ms (600s) | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 🔄 Cleanup tasks scheduled every 60 minutes | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 🚀 🚀 Claude Relay Service started on 0.0.0.0:3000 | {"metadata":{"type":"startup"}}
ℹ️  [2025-08-02 01:56:10] INFO: 🌐 Web interface: http://0.0.0.0:3000/web | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 🔗 API endpoint: http://0.0.0.0:3000/api/v1/messages | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: ⚙️  Admin API: http://0.0.0.0:3000/admin | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 🏥 Health check: http://0.0.0.0:3000/health | {"metadata":{}}
ℹ️  [2025-08-02 01:56:10] INFO: 📊 Metrics: http://0.0.0.0:3000/metrics | {"metadata":{}}
ℹ️  [2025-08-02 01:56:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 01:57:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 01:57:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 01:58:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 01:58:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 01:59:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 01:59:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:00:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:00:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:01:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:01:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:02:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:02:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:03:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:03:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:04:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:04:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:05:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:05:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:06:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:06:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:07:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:22:13] INFO: 🚀 Logger initialized | {"metadata":{"type":"startup","directory":"/app/logs","maxSize":"10m","maxFiles":5,"envOverride":false}}
ℹ️  [2025-08-02 02:22:14] INFO: ✅ Model pricing data loaded successfully | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 🔄 Connecting to Redis... | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 🔗 Redis connected successfully | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: ✅ ✅ Redis connected successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 02:22:14] INFO: 🔄 Initializing pricing service... | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 💰 Loaded pricing data for 1257 models from cache | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: ✅ 💰 Pricing service initialized successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 02:22:14] INFO: 🔄 Initializing admin credentials... | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: ✅ ✅ Admin credentials loaded from init.json (single source of truth) | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 02:22:14] INFO: 📋 Admin username: cr_admin | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: ✅ ✅ Application initialized successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 02:22:14] INFO: ⏱️  Server timeout set to 600000ms (600s) | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 🔄 Cleanup tasks scheduled every 60 minutes | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 🚀 🚀 Claude Relay Service started on 0.0.0.0:3000 | {"metadata":{"type":"startup"}}
ℹ️  [2025-08-02 02:22:14] INFO: 🌐 Web interface: http://0.0.0.0:3000/web | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 🔗 API endpoint: http://0.0.0.0:3000/api/v1/messages | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: ⚙️  Admin API: http://0.0.0.0:3000/admin | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 🏥 Health check: http://0.0.0.0:3000/health | {"metadata":{}}
ℹ️  [2025-08-02 02:22:14] INFO: 📊 Metrics: http://0.0.0.0:3000/metrics | {"metadata":{}}
ℹ️  [2025-08-02 02:22:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 02:23:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:23:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:24:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:24:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:25:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:25:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:26:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:26:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:27:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:27:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:28:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:28:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:29:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:29:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:30:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 02:30:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:31:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:31:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:32:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:32:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 02:33:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:33:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:34:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:34:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:35:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:35:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:36:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:36:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:37:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:37:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:38:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:38:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:39:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:39:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:40:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:40:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:41:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:41:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:42:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:42:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:43:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:43:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:44:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:44:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:45:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:45:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:46:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 02:46:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:47:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:47:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 02:48:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:48:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:49:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:49:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:50:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:50:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:51:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:51:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:52:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:52:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:53:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:53:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:54:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:54:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:55:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:55:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:56:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:56:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:57:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:57:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:58:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:58:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 02:59:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 02:59:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:00:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:00:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:01:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:01:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:02:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:02:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:03:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:03:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:04:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:04:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:05:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:05:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:06:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:06:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:07:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:07:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:08:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:08:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:09:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:09:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:10:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:10:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:11:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:11:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:12:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:12:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:13:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:13:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:14:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:14:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:15:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:15:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:16:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:16:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:17:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:17:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:18:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:18:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:19:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:19:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:20:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:20:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:21:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:21:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 03:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 03:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 03:22:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:22:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:23:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:23:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:24:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:24:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:25:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:25:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:26:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:26:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:27:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:27:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:28:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:28:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:29:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:29:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:30:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:30:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:31:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:31:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:32:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:32:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:33:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:33:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:34:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:34:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:35:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:35:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:36:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:36:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:37:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:37:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:38:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:38:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:39:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:39:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:40:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:40:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:41:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:41:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:42:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:42:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:43:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:43:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:44:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:44:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:45:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:45:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:46:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:46:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:47:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:47:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:48:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:48:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:49:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:49:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:50:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:50:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:51:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:51:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:52:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:52:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:53:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:53:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:54:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:54:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:55:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:55:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:56:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:56:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:57:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:57:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:58:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:58:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 03:59:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 03:59:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:00:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:00:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:01:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:01:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:02:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:02:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:03:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:03:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:04:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:04:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:05:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:05:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:06:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:06:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:07:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:07:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:08:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:08:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:09:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:09:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:10:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:10:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:11:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:11:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:12:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:12:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:13:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:13:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:14:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:14:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:15:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:15:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:16:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:16:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:17:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:17:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":4}}
ℹ️  [2025-08-02 04:18:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:18:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:19:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:19:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:20:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:20:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:21:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:21:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 04:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 04:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 04:22:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:22:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:23:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:23:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:24:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:24:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:25:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:25:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:26:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:26:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:27:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:27:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:28:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:28:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:29:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:29:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:30:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:30:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:31:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:31:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:32:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:32:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:33:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:33:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:34:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:34:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:35:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:35:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:36:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:36:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:37:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:37:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:38:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:38:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:39:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:39:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:40:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:40:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:41:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:41:50] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:42:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:42:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:43:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:43:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:44:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:44:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:45:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:45:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:46:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:46:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:47:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:47:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:48:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:48:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:49:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:49:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:50:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:50:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:51:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:51:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:52:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:52:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:53:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:53:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:54:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:54:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:55:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:55:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:56:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:56:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:57:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:57:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 04:58:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:58:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:59:21] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 04:59:51] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:00:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:00:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:01:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:01:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:02:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:02:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:03:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:03:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:04:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:04:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:05:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:05:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:06:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:06:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:07:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:07:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:08:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:08:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:09:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:09:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:10:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:10:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:11:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:11:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:12:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:12:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:13:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:13:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:14:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:14:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:15:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:15:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:16:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:16:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:17:22] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:17:52] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:18:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:18:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:19:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:19:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:20:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:20:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:21:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:21:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 05:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 05:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 05:22:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:22:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:23:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:23:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:24:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:24:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:25:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:25:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:26:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:26:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:27:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:27:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:28:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:28:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:29:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:29:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:30:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:30:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:31:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:31:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:32:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:32:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:33:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:33:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:34:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:34:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:35:23] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:35:53] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:36:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:36:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:37:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:37:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:38:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:38:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:39:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:39:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:40:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:40:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:41:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:41:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:42:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:42:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:43:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:43:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:44:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:44:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:45:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:45:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:46:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:46:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:47:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:47:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:48:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:48:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:49:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:49:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:50:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:50:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:51:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:51:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:52:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:52:54] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:53:24] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:53:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:54:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:54:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:55:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:55:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:56:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:56:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:57:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:57:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:58:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 05:58:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:59:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 05:59:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:00:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 06:00:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:01:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:01:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:02:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:02:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:03:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:03:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:04:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:04:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:05:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:05:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:06:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:06:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:07:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:07:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:08:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:08:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:09:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:09:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:10:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:10:55] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:11:25] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:11:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:12:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:12:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:13:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:13:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:14:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:14:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:15:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:15:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:16:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:16:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:17:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:17:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:18:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:18:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:19:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:19:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:20:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:20:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:21:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:21:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 06:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 06:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 06:22:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:22:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:23:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:23:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:24:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:24:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:25:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:25:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:26:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:26:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:27:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:27:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:28:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:28:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:29:26] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:29:56] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:30:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:30:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:31:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:31:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:32:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:32:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:33:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:33:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:34:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:34:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:35:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:35:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:36:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:36:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:37:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:37:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:38:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:38:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:39:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:39:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:40:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:40:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:41:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:41:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:42:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:42:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:43:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:43:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:44:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:44:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:45:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:45:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:46:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:46:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:47:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:47:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:48:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:48:57] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:49:27] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:49:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:50:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:50:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:51:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:51:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:52:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:52:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:53:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:53:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:54:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:54:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:55:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:55:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:56:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:56:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:57:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:57:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:58:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:58:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 06:59:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 06:59:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:00:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:00:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:01:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:01:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:02:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:02:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:03:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:03:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:04:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:04:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:05:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:05:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:06:28] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:06:58] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:07:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:07:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:08:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:08:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:09:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:09:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:10:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:10:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:11:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:11:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:12:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:12:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:13:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:13:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:14:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:14:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:15:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:15:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:16:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:16:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:17:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:17:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:18:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:18:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:19:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:19:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:20:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:20:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:21:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:21:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 07:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 07:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 07:22:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:22:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:23:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 07:23:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:24:29] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:24:59] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:25:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:26:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:26:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:27:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:27:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:28:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:28:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:29:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:29:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:30:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:30:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:31:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:31:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:32:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:32:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:33:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:33:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:34:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 07:34:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:35:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:35:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:36:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:36:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:37:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:37:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:38:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:38:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:39:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:39:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:40:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:40:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:41:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:41:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:42:00] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:42:30] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:43:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:43:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:44:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:44:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:45:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:45:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:46:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:46:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:47:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:47:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:48:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:48:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":3}}
ℹ️  [2025-08-02 07:49:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:49:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:50:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:50:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:51:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:51:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:52:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:52:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:53:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:53:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:54:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:54:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:55:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:55:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:56:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:56:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:57:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:57:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 07:58:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:58:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:59:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 07:59:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:00:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:00:31] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:01:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:01:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:02:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:02:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:03:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:03:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:04:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:04:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:05:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:05:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:06:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:06:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:07:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:07:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:08:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:08:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:09:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:09:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:10:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:10:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:11:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:11:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:12:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:12:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:13:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:13:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:14:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:14:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:15:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:15:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:16:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:16:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:17:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:17:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:18:02] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:18:32] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:19:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:19:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:20:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:20:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:21:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:21:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:22:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 08:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 08:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 08:22:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:23:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:23:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:24:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:24:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:25:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:25:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:26:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:26:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:27:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:27:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:28:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:28:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:29:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:29:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:30:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:30:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:31:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:31:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:32:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:32:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:33:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:33:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:34:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:34:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:35:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:35:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:36:03] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:36:33] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:37:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:37:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:38:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:38:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:39:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:39:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:40:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:40:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:41:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:41:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:42:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:42:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:43:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:43:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:44:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:44:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:45:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:45:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:46:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:46:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:47:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:47:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:48:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:48:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:49:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:49:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:50:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:50:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:51:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:51:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:52:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:52:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:53:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:53:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:54:04] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:54:34] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:55:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:55:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:56:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:56:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:57:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:57:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:58:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:58:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 08:59:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 08:59:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:00:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:00:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:01:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:01:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:02:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:02:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:03:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:03:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:04:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:04:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:05:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:05:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:06:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:06:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:07:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:07:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:08:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:08:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:09:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:09:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:10:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:10:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:11:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:11:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:12:05] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:12:35] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:13:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:13:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:14:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:14:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:15:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:15:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:16:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:16:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:17:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:17:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:18:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:18:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:19:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:19:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:20:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:20:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:21:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:21:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:22:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 09:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 09:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 09:22:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:23:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:23:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:24:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:24:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:25:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:25:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:26:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:26:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:27:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:27:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:28:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:28:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:29:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:29:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:30:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:30:36] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:31:06] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:31:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:32:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:32:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:33:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:33:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:34:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:34:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:35:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:35:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:36:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:36:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:37:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:37:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:38:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:38:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:39:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:39:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:40:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:40:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:41:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:41:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:42:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:42:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:43:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:43:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:44:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:44:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:45:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:45:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:46:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:46:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:47:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:47:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:48:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:48:37] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:49:07] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:49:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:50:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:50:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:51:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:51:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:52:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:52:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:53:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:53:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:54:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:54:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:55:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:55:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:56:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:56:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 09:57:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:57:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:58:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:58:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:59:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 09:59:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:00:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:00:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:01:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:01:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:02:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:02:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:03:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:03:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:04:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:04:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:05:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:05:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:06:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:06:38] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:07:08] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:07:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:08:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:08:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:09:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:09:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:10:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:10:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:11:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:11:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:12:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:12:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:13:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:13:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:14:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:14:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:15:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:15:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:16:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:16:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:17:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:17:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:18:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:18:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:19:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:19:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:20:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:20:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:21:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:21:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:22:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 10:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 10:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 10:22:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:23:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:23:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:24:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:24:39] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:25:09] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:25:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:26:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:26:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:27:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:27:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:28:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:28:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:29:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:29:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:30:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:30:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:31:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:31:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:32:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:32:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:32:48] INFO: 🟢 ▶️ [kovxg5rojxe] GET /api | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kovxg5rojxe] GET /api | IP: *************"}}
⚠️  [2025-08-02 10:32:48] WARN: ◀️ [kovxg5rojxe] GET /api | 404 | 4ms | 93B | {"metadata":{"requestId":"kovxg5rojxe","method":"GET","url":"/api","status":404,"duration":4,"contentLength":"93","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"none"}}
ℹ️  [2025-08-02 10:32:48] INFO: 🟢 ▶️ [5i1fhrvlsgh] GET /favicon.ico | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [5i1fhrvlsgh] GET /favicon.ico | IP: *************"}}
⚠️  [2025-08-02 10:32:48] WARN: ◀️ [5i1fhrvlsgh] GET /favicon.ico | 404 | 2ms | 101B | {"metadata":{"requestId":"5i1fhrvlsgh","method":"GET","url":"/favicon.ico","status":404,"duration":2,"contentLength":"101","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🟢 ▶️ [yg9hz9fzyh] POST /api/v1/messages?beta=true | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [yg9hz9fzyh] POST /api/v1/messages?beta=true | IP: *************"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗 🔓 API key validated successfully: 0ee312f9-6bed-4ac8-a968-5402668529cc | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗 🔓 Authenticated request from key: 3 (0ee312f9-6bed-4ac8-a968-5402668529cc) in 3ms | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗    User-Agent: "claude-cli/1.0.59 (external, cli)" | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗 🚀 Processing stream request for key: 3 | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔍 [Stream] API Key data received: | {"metadata":{"apiKeyName":"3","enableModelRestriction":false,"restrictedModels":[],"requestedModel":"claude-3-5-haiku-********"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🎯 Created new sticky session mapping for shared account: 1 (07e0785a-e0ca-40f8-962d-ae1a064a1e8a) for session d8b7fdaf2bea164479d7d93e749ef40b | {"metadata":{}}
ℹ️  [2025-08-02 10:33:01] INFO: 🎯 Selected shared account: 1 (07e0785a-e0ca-40f8-962d-ae1a064a1e8a) for API key 3 | {"metadata":{}}
ℹ️  [2025-08-02 10:33:01] INFO: 📡 Processing streaming API request with usage capture for key: 3, account: 07e0785a-e0ca-40f8-962d-ae1a064a1e8a, session: d8b7fdaf2bea164479d7d93e749ef40b | {"metadata":{}}
ℹ️  [2025-08-02 10:33:01] INFO: 🟢 ▶️ [88i1zj9rtql] POST /api/v1/messages?beta=true | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [88i1zj9rtql] POST /api/v1/messages?beta=true | IP: *************"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗 🔓 API key validated successfully: 0ee312f9-6bed-4ac8-a968-5402668529cc | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗 🔓 Authenticated request from key: 3 (0ee312f9-6bed-4ac8-a968-5402668529cc) in 1ms | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗    User-Agent: "claude-cli/1.0.59 (external, cli)" | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔗 🚀 Processing stream request for key: 3 | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🔍 [Stream] API Key data received: | {"metadata":{"apiKeyName":"3","enableModelRestriction":false,"restrictedModels":[],"requestedModel":"claude-sonnet-4-********"}}
ℹ️  [2025-08-02 10:33:01] INFO: 🎯 Created new sticky session mapping for shared account: 2 (6259d7ac-6c52-4c02-aa98-3698e3da1256) for session c47681404bffb30da2d0405d192b0590 | {"metadata":{}}
ℹ️  [2025-08-02 10:33:01] INFO: 🎯 Selected shared account: 2 (6259d7ac-6c52-4c02-aa98-3698e3da1256) for API key 3 | {"metadata":{}}
ℹ️  [2025-08-02 10:33:01] INFO: 📡 Processing streaming API request with usage capture for key: 3, account: 6259d7ac-6c52-4c02-aa98-3698e3da1256, session: c47681404bffb30da2d0405d192b0590 | {"metadata":{}}
ℹ️  [2025-08-02 10:33:02] INFO: 📊 Collected input/cache data from message_start: | {"metadata":{}}
ℹ️  [2025-08-02 10:33:02] INFO: 📊 Collected output data from message_delta: | {"metadata":{}}
ℹ️  [2025-08-02 10:33:02] INFO: 🎯 Complete usage data collected, triggering callback | {"metadata":{}}
ℹ️  [2025-08-02 10:33:02] INFO: 🎯 Usage callback triggered with complete data: | {"metadata":{}}
ℹ️  [2025-08-02 10:33:02] INFO: 🔗 📊 Stream usage recorded (real) - Model: claude-3-5-haiku-********, Input: 103, Output: 26, Cache Create: 0, Cache Read: 0, Total: 129 tokens | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:02] INFO: 🟢 ◀️ [yg9hz9fzyh] POST /api/v1/messages?beta=true | 200 | 1819ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [yg9hz9fzyh] POST /api/v1/messages?beta=true | 200 | 1819ms | 0B","url":{"requestId":"yg9hz9fzyh","method":"POST","url":"/api/v1/messages?beta=true","status":200,"duration":1819,"contentLength":"0","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}}
ℹ️  [2025-08-02 10:33:02] INFO: 🔗 📱 [yg9hz9fzyh] Request from 3 (0ee312f9-6bed-4ac8-a968-5402668529cc) | 1819ms | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:02] INFO: 🔗 ✅ Request completed in 1807ms for key: 3 | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:04] INFO: 📊 Collected input/cache data from message_start: | {"metadata":{}}
ℹ️  [2025-08-02 10:33:04] INFO: 📊 Collected output data from message_delta: | {"metadata":{}}
ℹ️  [2025-08-02 10:33:04] INFO: 🎯 Complete usage data collected, triggering callback | {"metadata":{}}
ℹ️  [2025-08-02 10:33:04] INFO: 🎯 Usage callback triggered with complete data: | {"metadata":{}}
ℹ️  [2025-08-02 10:33:04] INFO: 🔗 📊 Stream usage recorded (real) - Model: claude-sonnet-4-********, Input: 3, Output: 30, Cache Create: 18912, Cache Read: 0, Total: 18945 tokens | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:04] INFO: 🟢 ◀️ [88i1zj9rtql] POST /api/v1/messages?beta=true | 200 | 3729ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [88i1zj9rtql] POST /api/v1/messages?beta=true | 200 | 3729ms | 0B","url":{"requestId":"88i1zj9rtql","method":"POST","url":"/api/v1/messages?beta=true","status":200,"duration":3729,"contentLength":"0","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}}
ℹ️  [2025-08-02 10:33:04] INFO: 🔗 📱 [88i1zj9rtql] Request from 3 (0ee312f9-6bed-4ac8-a968-5402668529cc) | 3729ms | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:04] INFO: 🔗 ✅ Request completed in 3730ms for key: 3 | {"metadata":{"type":"api"}}
ℹ️  [2025-08-02 10:33:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:33:23] INFO: 🟢 ▶️ [416ddidyso2] GET /api | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [416ddidyso2] GET /api | IP: *************"}}
⚠️  [2025-08-02 10:33:23] WARN: ◀️ [416ddidyso2] GET /api | 404 | 1ms | 93B | {"metadata":{"requestId":"416ddidyso2","method":"GET","url":"/api","status":404,"duration":1,"contentLength":"93","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"none"}}
ℹ️  [2025-08-02 10:33:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:34:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:34:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:35:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:35:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:36:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:36:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:37:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:37:18] INFO: 🟢 ▶️ [ggzlx8o6me] GET /web?tab=tutorial | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ggzlx8o6me] GET /web?tab=tutorial | IP: *************"}}
ℹ️  [2025-08-02 10:37:18] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 10:37:18] INFO: 🟢 ◀️ [ggzlx8o6me] GET /web?tab=tutorial | 200 | 7ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ggzlx8o6me] GET /web?tab=tutorial | 200 | 7ms | 0B","url":{"requestId":"ggzlx8o6me","method":"GET","url":"/web?tab=tutorial","status":200,"duration":7,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"none"}}}
ℹ️  [2025-08-02 10:37:18] INFO: 🟢 ▶️ [p7dj686ay4b] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [p7dj686ay4b] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 10:37:18] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 10:37:18] INFO: 🟢 ▶️ [essqjjxoz7i] GET /web/style.css | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [essqjjxoz7i] GET /web/style.css | IP: *************"}}
ℹ️  [2025-08-02 10:37:18] INFO: 📄 Served whitelisted file: style.css | {"metadata":{}}
ℹ️  [2025-08-02 10:37:18] INFO: 🟢 ◀️ [essqjjxoz7i] GET /web/style.css | 200 | 3ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [essqjjxoz7i] GET /web/style.css | 200 | 3ms | 0B","url":{"requestId":"essqjjxoz7i","method":"GET","url":"/web/style.css","status":200,"duration":3,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:18] INFO: 🟢 ◀️ [p7dj686ay4b] GET /web/app.js | 200 | 10ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [p7dj686ay4b] GET /web/app.js | 200 | 10ms | 0B","url":{"requestId":"p7dj686ay4b","method":"GET","url":"/web/app.js","status":200,"duration":10,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [ocmfoev334r] GET /web/auth/user | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ocmfoev334r] GET /web/auth/user | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":3}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ◀️ [ocmfoev334r] GET /web/auth/user | 200 | 6ms | 47B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ocmfoev334r] GET /web/auth/user | 200 | 6ms | 47B","url":{"requestId":"ocmfoev334r","method":"GET","url":"/web/auth/user","status":200,"duration":6,"contentLength":"47","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [t2gswip74pn] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [t2gswip74pn] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [w5ov1fmr6c] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [w5ov1fmr6c] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [gzhr3nnamh] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [gzhr3nnamh] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [70h8zp81kbm] GET /admin/supported-clients | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [70h8zp81kbm] GET /admin/supported-clients | IP: *************"}}
⚠️  [2025-08-02 10:37:19] WARN: 🔒 🔒 Invalid admin token attempt from ************* | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:19] WARN: 🔒 🔒 Invalid admin token attempt from ************* | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:19] WARN: 🔒 🔒 Invalid admin token attempt from ************* | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:19] WARN: 🔒 🔒 Invalid admin token attempt from ************* | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:19] WARN: ◀️ [t2gswip74pn] GET /admin/claude-accounts | 401 | 12ms | 76B | {"metadata":{"requestId":"t2gswip74pn","method":"GET","url":"/admin/claude-accounts","status":401,"duration":12,"contentLength":"76","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}
⚠️  [2025-08-02 10:37:19] WARN: ◀️ [w5ov1fmr6c] GET /admin/gemini-accounts | 401 | 11ms | 76B | {"metadata":{"requestId":"w5ov1fmr6c","method":"GET","url":"/admin/gemini-accounts","status":401,"duration":11,"contentLength":"76","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}
⚠️  [2025-08-02 10:37:19] WARN: ◀️ [gzhr3nnamh] GET /admin/api-keys?timeRange=all | 401 | 8ms | 76B | {"metadata":{"requestId":"gzhr3nnamh","method":"GET","url":"/admin/api-keys?timeRange=all","status":401,"duration":8,"contentLength":"76","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}
⚠️  [2025-08-02 10:37:19] WARN: ◀️ [70h8zp81kbm] GET /admin/supported-clients | 401 | 8ms | 76B | {"metadata":{"requestId":"70h8zp81kbm","method":"GET","url":"/admin/supported-clients","status":401,"duration":8,"contentLength":"76","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [uhgrb6d7k8i] GET /admin/check-updates | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [uhgrb6d7k8i] GET /admin/check-updates | IP: *************"}}
⚠️  [2025-08-02 10:37:19] WARN: 🔒 🔒 Invalid admin token attempt from ************* | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:19] WARN: ◀️ [uhgrb6d7k8i] GET /admin/check-updates | 401 | 5ms | 76B | {"metadata":{"requestId":"uhgrb6d7k8i","method":"GET","url":"/admin/check-updates","status":401,"duration":5,"contentLength":"76","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [kzen3gy9vpl] GET /web?tab=tutorial | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kzen3gy9vpl] GET /web?tab=tutorial | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [ldvuxh3itqo] GET /web?tab=tutorial | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ldvuxh3itqo] GET /web?tab=tutorial | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ◀️ [ldvuxh3itqo] GET /web?tab=tutorial | 200 | 9ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ldvuxh3itqo] GET /web?tab=tutorial | 200 | 9ms | 0B","url":{"requestId":"ldvuxh3itqo","method":"GET","url":"/web?tab=tutorial","status":200,"duration":9,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [8sbf7kak2y] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [8sbf7kak2y] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ▶️ [lx2h93cbax] GET /web/style.css | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [lx2h93cbax] GET /web/style.css | IP: *************"}}
ℹ️  [2025-08-02 10:37:19] INFO: 📄 Served whitelisted file: style.css | {"metadata":{}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ◀️ [lx2h93cbax] GET /web/style.css | 200 | 4ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [lx2h93cbax] GET /web/style.css | 200 | 4ms | 0B","url":{"requestId":"lx2h93cbax","method":"GET","url":"/web/style.css","status":200,"duration":4,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:19] INFO: 🟢 ◀️ [8sbf7kak2y] GET /web/app.js | 200 | 9ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [8sbf7kak2y] GET /web/app.js | 200 | 9ms | 0B","url":{"requestId":"8sbf7kak2y","method":"GET","url":"/web/app.js","status":200,"duration":9,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [p0sn4u6dodb] POST /web/auth/login | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [p0sn4u6dodb] POST /web/auth/login | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: ✅ 🔐 Admin login successful: cr_admin | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [p0sn4u6dodb] POST /web/auth/login | 200 | 52ms | 134B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [p0sn4u6dodb] POST /web/auth/login | 200 | 52ms | 134B","url":{"requestId":"p0sn4u6dodb","method":"POST","url":"/web/auth/login","status":200,"duration":52,"contentLength":"134","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [vnqwcxvpgq] GET /web?tab=tutorial | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [vnqwcxvpgq] GET /web?tab=tutorial | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [vnqwcxvpgq] GET /web?tab=tutorial | 200 | 5ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [vnqwcxvpgq] GET /web?tab=tutorial | 200 | 5ms | 0B","url":{"requestId":"vnqwcxvpgq","method":"GET","url":"/web?tab=tutorial","status":200,"duration":5,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [916fqib67a8] GET /web/style.css | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [916fqib67a8] GET /web/style.css | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: 📄 Served whitelisted file: style.css | {"metadata":{}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [0gkryhpnn7c] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [0gkryhpnn7c] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [916fqib67a8] GET /web/style.css | 200 | 6ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [916fqib67a8] GET /web/style.css | 200 | 6ms | 0B","url":{"requestId":"916fqib67a8","method":"GET","url":"/web/style.css","status":200,"duration":6,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [0gkryhpnn7c] GET /web/app.js | 200 | 5ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [0gkryhpnn7c] GET /web/app.js | 200 | 5ms | 0B","url":{"requestId":"0gkryhpnn7c","method":"GET","url":"/web/app.js","status":200,"duration":5,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [rcg2hs8wztf] GET /web/auth/user | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [rcg2hs8wztf] GET /web/auth/user | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [1d7x83eeunq] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [1d7x83eeunq] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [xulkxg8881r] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [xulkxg8881r] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [6w8glwkuwrw] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [6w8glwkuwrw] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 10:37:20] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":3}}
⚠️  [2025-08-02 10:37:20] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:20] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:20] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [rcg2hs8wztf] GET /web/auth/user | 200 | 8ms | 128B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [rcg2hs8wztf] GET /web/auth/user | 200 | 8ms | 128B","url":{"requestId":"rcg2hs8wztf","method":"GET","url":"/web/auth/user","status":200,"duration":8,"contentLength":"128","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [xulkxg8881r] GET /admin/gemini-accounts | 200 | 5ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [xulkxg8881r] GET /admin/gemini-accounts | 200 | 5ms | 26B","url":{"requestId":"xulkxg8881r","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":5,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ▶️ [3xr5za23znw] GET /admin/supported-clients | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [3xr5za23znw] GET /admin/supported-clients | IP: *************"}}
⚠️  [2025-08-02 10:37:20] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [3xr5za23znw] GET /admin/supported-clients | 200 | 1ms | 193B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [3xr5za23znw] GET /admin/supported-clients | 200 | 1ms | 193B","url":{"requestId":"3xr5za23znw","method":"GET","url":"/admin/supported-clients","status":200,"duration":1,"contentLength":"193","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [1d7x83eeunq] GET /admin/claude-accounts | 200 | 11ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [1d7x83eeunq] GET /admin/claude-accounts | 200 | 11ms | 0B","url":{"requestId":"1d7x83eeunq","method":"GET","url":"/admin/claude-accounts","status":200,"duration":11,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:20] INFO: 🟢 ◀️ [6w8glwkuwrw] GET /admin/api-keys?timeRange=all | 200 | 14ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [6w8glwkuwrw] GET /admin/api-keys?timeRange=all | 200 | 14ms | 0B","url":{"requestId":"6w8glwkuwrw","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":14,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:21] INFO: 🟢 ▶️ [a5dfyemb2ct] GET /admin/check-updates | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [a5dfyemb2ct] GET /admin/check-updates | IP: *************"}}
⚠️  [2025-08-02 10:37:21] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 10:37:21] INFO: 🟢 ◀️ [a5dfyemb2ct] GET /admin/check-updates | 200 | 610ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [a5dfyemb2ct] GET /admin/check-updates | 200 | 610ms | 0B","url":{"requestId":"a5dfyemb2ct","method":"GET","url":"/admin/check-updates","status":200,"duration":610,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 10:37:22] INFO: 🟢 ▶️ [zp9lfygtwi] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [zp9lfygtwi] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 10:37:22] INFO: 🟢 ▶️ [igjitmk0vi7] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [igjitmk0vi7] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 10:37:22] INFO: 🟢 ▶️ [epj4ujtp8ss] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [epj4ujtp8ss] GET /admin/api-keys?timeRange=all | IP: *************"}}
⚠️  [2025-08-02 10:37:22] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:22] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 10:37:22] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 10:37:22] INFO: 🟢 ◀️ [zp9lfygtwi] GET /admin/gemini-accounts | 200 | 9ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [zp9lfygtwi] GET /admin/gemini-accounts | 200 | 9ms | 26B","url":{"requestId":"zp9lfygtwi","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":9,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 10:37:22] INFO: 🟢 ◀️ [igjitmk0vi7] GET /admin/claude-accounts | 200 | 19ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [igjitmk0vi7] GET /admin/claude-accounts | 200 | 19ms | 0B","url":{"requestId":"igjitmk0vi7","method":"GET","url":"/admin/claude-accounts","status":200,"duration":19,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 10:37:22] INFO: 🟢 ◀️ [epj4ujtp8ss] GET /admin/api-keys?timeRange=all | 200 | 28ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [epj4ujtp8ss] GET /admin/api-keys?timeRange=all | 200 | 28ms | 0B","url":{"requestId":"epj4ujtp8ss","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":28,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 10:37:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:38:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:38:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:39:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:39:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:40:10] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:40:40] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:41:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:41:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:42:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:42:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:43:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:43:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:44:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:44:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:45:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:45:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:46:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:46:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:47:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:47:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:48:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:48:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:49:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:49:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:50:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:50:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:51:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:51:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:52:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:52:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:53:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:53:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:54:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:54:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:55:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:55:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:56:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:56:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:57:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:57:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:58:11] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 10:58:41] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:59:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 10:59:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:00:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:00:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:01:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:01:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:02:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:02:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:03:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:03:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:04:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:04:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:05:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:05:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:06:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:06:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [nvuceb831h] GET /web?tab=accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [nvuceb831h] GET /web?tab=accounts | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [nvuceb831h] GET /web?tab=accounts | 200 | 5ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [nvuceb831h] GET /web?tab=accounts | 200 | 5ms | 0B","url":{"requestId":"nvuceb831h","method":"GET","url":"/web?tab=accounts","status":200,"duration":5,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [tkcmfly1ahd] GET /web/style.css | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [tkcmfly1ahd] GET /web/style.css | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 📄 Served whitelisted file: style.css | {"metadata":{}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [kllut6m8xtb] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kllut6m8xtb] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [tkcmfly1ahd] GET /web/style.css | 200 | 10ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [tkcmfly1ahd] GET /web/style.css | 200 | 10ms | 0B","url":{"requestId":"tkcmfly1ahd","method":"GET","url":"/web/style.css","status":200,"duration":10,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [kllut6m8xtb] GET /web/app.js | 200 | 8ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [kllut6m8xtb] GET /web/app.js | 200 | 8ms | 0B","url":{"requestId":"kllut6m8xtb","method":"GET","url":"/web/app.js","status":200,"duration":8,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [g3jcys142a9] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [g3jcys142a9] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [g3jcys142a9] GET /web/app.js | 200 | 4ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [g3jcys142a9] GET /web/app.js | 200 | 4ms | 0B","url":{"requestId":"g3jcys142a9","method":"GET","url":"/web/app.js","status":200,"duration":4,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"none"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [fy372pt29fo] GET /web/auth/user | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [fy372pt29fo] GET /web/auth/user | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [fy372pt29fo] GET /web/auth/user | 200 | 3ms | 128B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [fy372pt29fo] GET /web/auth/user | 200 | 3ms | 128B","url":{"requestId":"fy372pt29fo","method":"GET","url":"/web/auth/user","status":200,"duration":3,"contentLength":"128","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [qo3kpeqhku] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [qo3kpeqhku] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [kqkx7fytfjj] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kqkx7fytfjj] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [u6qkb5ff89l] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [u6qkb5ff89l] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [i89yngr1hk8] GET /admin/supported-clients | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [i89yngr1hk8] GET /admin/supported-clients | IP: *************"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [i89yngr1hk8] GET /admin/supported-clients | 200 | 3ms | 193B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [i89yngr1hk8] GET /admin/supported-clients | 200 | 3ms | 193B","url":{"requestId":"i89yngr1hk8","method":"GET","url":"/admin/supported-clients","status":200,"duration":3,"contentLength":"193","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [qo3kpeqhku] GET /admin/gemini-accounts | 200 | 6ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [qo3kpeqhku] GET /admin/gemini-accounts | 200 | 6ms | 26B","url":{"requestId":"qo3kpeqhku","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":6,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [5tbm2kls239] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [5tbm2kls239] GET /admin/gemini-accounts | IP: *************"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [5tbm2kls239] GET /admin/gemini-accounts | 200 | 4ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [5tbm2kls239] GET /admin/gemini-accounts | 200 | 4ms | 26B","url":{"requestId":"5tbm2kls239","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":4,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [kqkx7fytfjj] GET /admin/claude-accounts | 200 | 15ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [kqkx7fytfjj] GET /admin/claude-accounts | 200 | 15ms | 0B","url":{"requestId":"kqkx7fytfjj","method":"GET","url":"/admin/claude-accounts","status":200,"duration":15,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [2gsbbrcdbn5] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [2gsbbrcdbn5] GET /admin/claude-accounts | IP: *************"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 0ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [u6qkb5ff89l] GET /admin/api-keys?timeRange=all | 200 | 20ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [u6qkb5ff89l] GET /admin/api-keys?timeRange=all | 200 | 20ms | 0B","url":{"requestId":"u6qkb5ff89l","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":20,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [ug1r3mjxf4s] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ug1r3mjxf4s] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [2gsbbrcdbn5] GET /admin/claude-accounts | 200 | 7ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [2gsbbrcdbn5] GET /admin/claude-accounts | 200 | 7ms | 0B","url":{"requestId":"2gsbbrcdbn5","method":"GET","url":"/admin/claude-accounts","status":200,"duration":7,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 4ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [ug1r3mjxf4s] GET /admin/api-keys?timeRange=all | 200 | 12ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ug1r3mjxf4s] GET /admin/api-keys?timeRange=all | 200 | 12ms | 0B","url":{"requestId":"ug1r3mjxf4s","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":12,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [w0rg24cltso] GET /admin/check-updates | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [w0rg24cltso] GET /admin/check-updates | IP: *************"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [w0rg24cltso] GET /admin/check-updates | 200 | 3ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [w0rg24cltso] GET /admin/check-updates | 200 | 3ms | 0B","url":{"requestId":"w0rg24cltso","method":"GET","url":"/admin/check-updates","status":200,"duration":3,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [ltshjdvgzse] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ltshjdvgzse] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [01gr8igou89k] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [01gr8igou89k] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ▶️ [mnbrfjpp0eq] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [mnbrfjpp0eq] GET /admin/claude-accounts | IP: *************"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:06:49] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [01gr8igou89k] GET /admin/gemini-accounts | 200 | 9ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [01gr8igou89k] GET /admin/gemini-accounts | 200 | 9ms | 26B","url":{"requestId":"01gr8igou89k","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":9,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [mnbrfjpp0eq] GET /admin/claude-accounts | 200 | 15ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [mnbrfjpp0eq] GET /admin/claude-accounts | 200 | 15ms | 0B","url":{"requestId":"mnbrfjpp0eq","method":"GET","url":"/admin/claude-accounts","status":200,"duration":15,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:06:49] INFO: 🟢 ◀️ [ltshjdvgzse] GET /admin/api-keys?timeRange=all | 200 | 21ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ltshjdvgzse] GET /admin/api-keys?timeRange=all | 200 | 21ms | 0B","url":{"requestId":"ltshjdvgzse","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":21,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:07:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:07:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:08:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:08:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:09:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:09:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:10:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:10:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:11:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:11:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:12:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:12:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:13:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:13:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:14:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:14:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:15:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:15:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:16:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:16:42] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:17:12] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:17:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:18:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:18:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:19:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:19:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:20:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:20:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:21:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:21:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:22:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:22:14] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 11:22:14] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 11:22:14] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 11:22:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:23:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:23:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:24:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:24:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:25:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:25:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:26:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:26:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:27:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:27:43] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:28:13] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:30:14] INFO: 🚀 Logger initialized | {"metadata":{"type":"startup","directory":"/app/logs","maxSize":"10m","maxFiles":5,"envOverride":false}}
ℹ️  [2025-08-02 11:30:15] INFO: ✅ Model pricing data loaded successfully | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 🔄 Connecting to Redis... | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 🔗 Redis connected successfully | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: ✅ ✅ Redis connected successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 11:30:15] INFO: 🔄 Initializing pricing service... | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 💰 Loaded pricing data for 1257 models from cache | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: ✅ 💰 Pricing service initialized successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 11:30:15] INFO: 🔄 Initializing admin credentials... | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: ✅ ✅ Admin credentials loaded from init.json (single source of truth) | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 11:30:15] INFO: 📋 Admin username: cr_admin | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: ✅ ✅ Application initialized successfully | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 11:30:15] INFO: ⏱️  Server timeout set to 600000ms (600s) | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 🔄 Cleanup tasks scheduled every 60 minutes | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 🚀 🚀 Claude Relay Service started on 0.0.0.0:3000 | {"metadata":{"type":"startup"}}
ℹ️  [2025-08-02 11:30:15] INFO: 🌐 Web interface: http://0.0.0.0:3000/web | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 🔗 API endpoint: http://0.0.0.0:3000/api/v1/messages | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: ⚙️  Admin API: http://0.0.0.0:3000/admin | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 🏥 Health check: http://0.0.0.0:3000/health | {"metadata":{}}
ℹ️  [2025-08-02 11:30:15] INFO: 📊 Metrics: http://0.0.0.0:3000/metrics | {"metadata":{}}
ℹ️  [2025-08-02 11:30:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":2}}
ℹ️  [2025-08-02 11:31:00] INFO: 🟢 ▶️ [6vove106fhr] GET /web?tab=accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [6vove106fhr] GET /web?tab=accounts | IP: *************"}}
ℹ️  [2025-08-02 11:31:00] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 11:31:00] INFO: 🟢 ◀️ [6vove106fhr] GET /web?tab=accounts | 200 | 16ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [6vove106fhr] GET /web?tab=accounts | 200 | 16ms | 0B","url":{"requestId":"6vove106fhr","method":"GET","url":"/web?tab=accounts","status":200,"duration":16,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [6yk164o9s82] GET /web/style.css | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [6yk164o9s82] GET /web/style.css | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 📄 Served whitelisted file: style.css | {"metadata":{}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [6yk164o9s82] GET /web/style.css | 200 | 5ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [6yk164o9s82] GET /web/style.css | 200 | 5ms | 0B","url":{"requestId":"6yk164o9s82","method":"GET","url":"/web/style.css","status":200,"duration":5,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [43mrmcr0exz] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [43mrmcr0exz] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [43mrmcr0exz] GET /web/app.js | 200 | 11ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [43mrmcr0exz] GET /web/app.js | 200 | 11ms | 0B","url":{"requestId":"43mrmcr0exz","method":"GET","url":"/web/app.js","status":200,"duration":11,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [gjkity8xv8c] GET /web/auth/user | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [gjkity8xv8c] GET /web/auth/user | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [gjkity8xv8c] GET /web/auth/user | 200 | 5ms | 128B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [gjkity8xv8c] GET /web/auth/user | 200 | 5ms | 128B","url":{"requestId":"gjkity8xv8c","method":"GET","url":"/web/auth/user","status":200,"duration":5,"contentLength":"128","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [l9o3l4g51z] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [l9o3l4g51z] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [8t5v4uzjt24] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [8t5v4uzjt24] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [b1qxkvzid46] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [b1qxkvzid46] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [7o6pkbwq7ab] GET /admin/supported-clients | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [7o6pkbwq7ab] GET /admin/supported-clients | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 5ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 4ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [7o6pkbwq7ab] GET /admin/supported-clients | 200 | 4ms | 193B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [7o6pkbwq7ab] GET /admin/supported-clients | 200 | 4ms | 193B","url":{"requestId":"7o6pkbwq7ab","method":"GET","url":"/admin/supported-clients","status":200,"duration":4,"contentLength":"193","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [8t5v4uzjt24] GET /admin/gemini-accounts | 200 | 9ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [8t5v4uzjt24] GET /admin/gemini-accounts | 200 | 9ms | 26B","url":{"requestId":"8t5v4uzjt24","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":9,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [kbmcm21hwl] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kbmcm21hwl] GET /admin/gemini-accounts | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [kbmcm21hwl] GET /admin/gemini-accounts | 200 | 3ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [kbmcm21hwl] GET /admin/gemini-accounts | 200 | 3ms | 26B","url":{"requestId":"kbmcm21hwl","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":3,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [l9o3l4g51z] GET /admin/claude-accounts | 200 | 22ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [l9o3l4g51z] GET /admin/claude-accounts | 200 | 22ms | 0B","url":{"requestId":"l9o3l4g51z","method":"GET","url":"/admin/claude-accounts","status":200,"duration":22,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [21dvefqx0v7] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [21dvefqx0v7] GET /admin/claude-accounts | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [b1qxkvzid46] GET /admin/api-keys?timeRange=all | 200 | 24ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [b1qxkvzid46] GET /admin/api-keys?timeRange=all | 200 | 24ms | 0B","url":{"requestId":"b1qxkvzid46","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":24,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [21dvefqx0v7] GET /admin/claude-accounts | 200 | 7ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [21dvefqx0v7] GET /admin/claude-accounts | 200 | 7ms | 0B","url":{"requestId":"21dvefqx0v7","method":"GET","url":"/admin/claude-accounts","status":200,"duration":7,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [95fzxld9ucw] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [95fzxld9ucw] GET /admin/api-keys?timeRange=all | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [95fzxld9ucw] GET /admin/api-keys?timeRange=all | 200 | 6ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [95fzxld9ucw] GET /admin/api-keys?timeRange=all | 200 | 6ms | 0B","url":{"requestId":"95fzxld9ucw","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":6,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [kcbbmb7rr09] GET /admin/check-updates | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kcbbmb7rr09] GET /admin/check-updates | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [kcbbmb7rr09] GET /admin/check-updates | 200 | 5ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [kcbbmb7rr09] GET /admin/check-updates | 200 | 5ms | 0B","url":{"requestId":"kcbbmb7rr09","method":"GET","url":"/admin/check-updates","status":200,"duration":5,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [j5fh11r4rrs] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [j5fh11r4rrs] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [wjvdcvkby0a] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [wjvdcvkby0a] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [ow11ixbava] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ow11ixbava] GET /admin/api-keys?timeRange=all | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:31:01] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [wjvdcvkby0a] GET /admin/gemini-accounts | 200 | 3ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [wjvdcvkby0a] GET /admin/gemini-accounts | 200 | 3ms | 26B","url":{"requestId":"wjvdcvkby0a","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":3,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [j5fh11r4rrs] GET /admin/claude-accounts | 200 | 8ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [j5fh11r4rrs] GET /admin/claude-accounts | 200 | 8ms | 0B","url":{"requestId":"j5fh11r4rrs","method":"GET","url":"/admin/claude-accounts","status":200,"duration":8,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ◀️ [ow11ixbava] GET /admin/api-keys?timeRange=all | 200 | 11ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ow11ixbava] GET /admin/api-keys?timeRange=all | 200 | 11ms | 0B","url":{"requestId":"ow11ixbava","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":11,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 11:31:01] INFO: 🟢 ▶️ [ei1mixyri6e] GET /favicon.ico | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ei1mixyri6e] GET /favicon.ico | IP: *************"}}
⚠️  [2025-08-02 11:31:01] WARN: ◀️ [ei1mixyri6e] GET /favicon.ico | 404 | 2ms | 101B | {"metadata":{"requestId":"ei1mixyri6e","method":"GET","url":"/favicon.ico","status":404,"duration":2,"contentLength":"101","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}
ℹ️  [2025-08-02 11:31:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:31:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:32:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:32:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:33:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:33:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:34:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:34:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:35:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:35:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:36:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:36:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:37:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:37:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:38:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:38:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:39:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:39:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:40:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:40:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ▶️ [4qfbq3nmb2] GET /admin/dashboard | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [4qfbq3nmb2] GET /admin/dashboard | IP: *************"}}
⚠️  [2025-08-02 11:40:48] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ▶️ [20wnzvuwgmm] GET /admin/usage-costs?period=today | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [20wnzvuwgmm] GET /admin/usage-costs?period=today | IP: *************"}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ▶️ [j6ndh0iy95f] GET /admin/usage-costs?period=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [j6ndh0iy95f] GET /admin/usage-costs?period=all | IP: *************"}}
⚠️  [2025-08-02 11:40:48] WARN: 🔒 🔐 Admin authenticated: cr_admin in 4ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Calculating usage costs for period: today | {"metadata":{}}
⚠️  [2025-08-02 11:40:48] WARN: 🔒 🔐 Admin authenticated: cr_admin in 5ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Calculating usage costs for period: all | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ◀️ [4qfbq3nmb2] GET /admin/dashboard | 200 | 20ms | 797B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [4qfbq3nmb2] GET /admin/dashboard | 200 | 20ms | 797B","url":{"requestId":"4qfbq3nmb2","method":"GET","url":"/admin/dashboard","status":200,"duration":20,"contentLength":"797","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Total period calculation: found 5 monthly model keys | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ◀️ [20wnzvuwgmm] GET /admin/usage-costs?period=today | 200 | 17ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [20wnzvuwgmm] GET /admin/usage-costs?period=today | 200 | 17ms | 0B","url":{"requestId":"20wnzvuwgmm","method":"GET","url":"/admin/usage-costs?period=today","status":200,"duration":17,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Processing 3 unique models for total cost calculation | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Model claude-opus-4-********: 215473 tokens, cost: $4.06 | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Model claude-sonnet-4-********: 216449415 tokens, cost: $773.32 | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 💰 Model claude-3-5-haiku-********: 892735 tokens, cost: $0.7652 | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ▶️ [8727g2qq96u] GET /admin/model-stats?period=monthly | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [8727g2qq96u] GET /admin/model-stats?period=monthly | IP: *************"}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ◀️ [j6ndh0iy95f] GET /admin/usage-costs?period=all | 200 | 21ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [j6ndh0iy95f] GET /admin/usage-costs?period=all | 200 | 21ms | 0B","url":{"requestId":"j6ndh0iy95f","method":"GET","url":"/admin/usage-costs?period=all","status":200,"duration":21,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
⚠️  [2025-08-02 11:40:48] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Getting global model stats, period: monthly, today: 2025-08-02, currentMonth: 2025-08 | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Searching pattern: usage:model:monthly:*:2025-08 | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ▶️ [jds0lxel1ed] GET /admin/usage-trend?granularity=day&days=7 | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [jds0lxel1ed] GET /admin/usage-trend?granularity=day&days=7 | IP: *************"}}
ℹ️  [2025-08-02 11:40:48] INFO: 🟢 ▶️ [d4dtvtkuujg] GET /admin/api-keys-usage-trend?granularity=day&days=7 | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [d4dtvtkuujg] GET /admin/api-keys-usage-trend?granularity=day&days=7 | IP: *************"}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Found 2 matching keys: | {"metadata":{"0":"usage:model:monthly:claude-sonnet-4-********:2025-08","1":"usage:model:monthly:claude-3-5-haiku-********:2025-08"}}
⚠️  [2025-08-02 11:40:48] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 11:40:48] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Getting API keys usage trend, granularity: day, days: 7 | {"metadata":{}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Model claude-sonnet-4-******** data: | {"metadata":{"inputTokens":"3","outputTokens":"30","cacheCreateTokens":"18912","cacheReadTokens":"0","allTokens":"18945","requests":"1"}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Model claude-3-5-haiku-******** data: | {"metadata":{"inputTokens":"103","outputTokens":"26","cacheCreateTokens":"0","cacheReadTokens":"0","allTokens":"129","requests":"1"}}
ℹ️  [2025-08-02 11:40:48] INFO: 📊 Returning 2 global model stats for period monthly: | {"metadata":{"0":{"model":"claude-sonnet-4-********","period":"monthly","requests":1,"inputTokens":3,"outputTokens":30,"cacheCreateTokens":18912,"cacheReadTokens":0,"allTokens":18945,"usage":{"requests":1,"inputTokens":3,"outputTokens":30,"cacheCreateTokens":18912,"cacheReadTokens":0,"totalTokens":18945},"costs":{"input":0.000009,"output":0.00045,"cacheWrite":0.07092000000000001,"cacheRead":0,"total":0.07137900000000001},"formatted":{"input":"$0.000009","output":"$0.000450","cacheWrite":"$0.0709","cacheRead":"$0.000000","total":"$0.0714"},"pricing":{"input":3,"output":15,"cacheWrite":3.75,"cacheRead":0.3}},"1":{"model":"claude-3-5-haiku-********","period":"monthly","requests":1,"inputTokens":103,"outputTokens":26,"cacheCreateTokens":0,"cacheReadTokens":0,"allTokens":129,"usage":{"requests":1,"inputTokens":103,"outputTokens":26,"cacheCreateTokens":0,"cacheReadTokens":0,"totalTokens":129},"costs":{"input":0.00008239999999999998,"output":0.000104,"cacheWrite":0,"cacheRead":0,"total":0.00018639999999999998},"formatted":{"input":"$0.000082","output":"$0.000104","cacheWrite":"$0.000000","cacheRead":"$0.000000","total":"$0.000186"},"pricing":{"input":0.7999999999999999,"output":4,"cacheWrite":1,"cacheRead":0.08}}}}
ℹ️  [2025-08-02 11:40:49] INFO: 🟢 ◀️ [8727g2qq96u] GET /admin/model-stats?period=monthly | 200 | 10ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [8727g2qq96u] GET /admin/model-stats?period=monthly | 200 | 10ms | 0B","url":{"requestId":"8727g2qq96u","method":"GET","url":"/admin/model-stats?period=monthly","status":200,"duration":10,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 11:40:49] INFO: 🟢 ◀️ [d4dtvtkuujg] GET /admin/api-keys-usage-trend?granularity=day&days=7 | 200 | 11ms | 898B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [d4dtvtkuujg] GET /admin/api-keys-usage-trend?granularity=day&days=7 | 200 | 11ms | 898B","url":{"requestId":"d4dtvtkuujg","method":"GET","url":"/admin/api-keys-usage-trend?granularity=day&days=7","status":200,"duration":11,"contentLength":"898","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 11:40:49] INFO: 🟢 ◀️ [jds0lxel1ed] GET /admin/usage-trend?granularity=day&days=7 | 200 | 14ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [jds0lxel1ed] GET /admin/usage-trend?granularity=day&days=7 | 200 | 14ms | 0B","url":{"requestId":"jds0lxel1ed","method":"GET","url":"/admin/usage-trend?granularity=day&days=7","status":200,"duration":14,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 11:41:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:41:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:42:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:42:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:43:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:43:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:44:14] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:44:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:45:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:45:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:46:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:46:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:47:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:47:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:48:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:48:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:49:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:49:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:50:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:50:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:51:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:51:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:52:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:52:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:53:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:53:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:54:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:54:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:55:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:55:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:56:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:56:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:57:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:57:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:58:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 11:58:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:59:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 11:59:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:00:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:00:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:01:15] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:01:45] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:02:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:02:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:03:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:03:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:04:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:04:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:05:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:05:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:06:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:06:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:07:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:07:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:08:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:08:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:09:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:09:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:10:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:10:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:11:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:11:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:12:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:12:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:13:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:13:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:14:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:14:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:15:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:15:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:16:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:16:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:17:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:17:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:18:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:18:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:19:16] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:19:46] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:20:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:20:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:21:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:21:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:22:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:22:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:23:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:23:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:24:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:24:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:25:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:25:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:26:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:26:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:27:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:27:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:28:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:28:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:29:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:29:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:30:15] INFO: 🧹 Starting scheduled cleanup... | {"metadata":{}}
ℹ️  [2025-08-02 12:30:15] INFO: 🧹 Redis cleanup completed | {"metadata":{}}
ℹ️  [2025-08-02 12:30:15] INFO: ✅ 🧹 Cleanup completed: 0 expired keys, 0 error accounts reset | {"metadata":{"type":"success"}}
ℹ️  [2025-08-02 12:30:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [vfe047ovoz] GET /web | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [vfe047ovoz] GET /web | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📄 Served whitelisted file: index.html | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [vfe047ovoz] GET /web | 200 | 12ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [vfe047ovoz] GET /web | 200 | 12ms | 0B","url":{"requestId":"vfe047ovoz","method":"GET","url":"/web","status":200,"duration":12,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=tutorial"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [l9cwryej3ti] GET /web/style.css | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [l9cwryej3ti] GET /web/style.css | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📄 Served whitelisted file: style.css | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [790bf5aulak] GET /web/app.js | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [790bf5aulak] GET /web/app.js | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📄 Served whitelisted file: app.js | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [l9cwryej3ti] GET /web/style.css | 200 | 6ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [l9cwryej3ti] GET /web/style.css | 200 | 6ms | 0B","url":{"requestId":"l9cwryej3ti","method":"GET","url":"/web/style.css","status":200,"duration":6,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [790bf5aulak] GET /web/app.js | 200 | 8ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [790bf5aulak] GET /web/app.js | 200 | 8ms | 0B","url":{"requestId":"790bf5aulak","method":"GET","url":"/web/app.js","status":200,"duration":8,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [24ukgsomp9] GET /web/auth/user | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [24ukgsomp9] GET /web/auth/user | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [72u1bq5v3o4] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [72u1bq5v3o4] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [kfsg9tx3bi] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [kfsg9tx3bi] GET /admin/gemini-accounts | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [caohvay3enu] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [caohvay3enu] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [5jlibzz4o93] GET /admin/supported-clients | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [5jlibzz4o93] GET /admin/supported-clients | IP: *************"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [24ukgsomp9] GET /web/auth/user | 200 | 7ms | 128B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [24ukgsomp9] GET /web/auth/user | 200 | 7ms | 128B","url":{"requestId":"24ukgsomp9","method":"GET","url":"/web/auth/user","status":200,"duration":7,"contentLength":"128","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [5jlibzz4o93] GET /admin/supported-clients | 200 | 3ms | 193B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [5jlibzz4o93] GET /admin/supported-clients | 200 | 3ms | 193B","url":{"requestId":"5jlibzz4o93","method":"GET","url":"/admin/supported-clients","status":200,"duration":3,"contentLength":"193","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [kfsg9tx3bi] GET /admin/gemini-accounts | 200 | 5ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [kfsg9tx3bi] GET /admin/gemini-accounts | 200 | 5ms | 26B","url":{"requestId":"kfsg9tx3bi","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":5,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [72u1bq5v3o4] GET /admin/claude-accounts | 200 | 9ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [72u1bq5v3o4] GET /admin/claude-accounts | 200 | 9ms | 0B","url":{"requestId":"72u1bq5v3o4","method":"GET","url":"/admin/claude-accounts","status":200,"duration":9,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [caohvay3enu] GET /admin/api-keys?timeRange=all | 200 | 11ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [caohvay3enu] GET /admin/api-keys?timeRange=all | 200 | 11ms | 0B","url":{"requestId":"caohvay3enu","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":11,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [ut0b7b5u4rk] GET /admin/check-updates | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [ut0b7b5u4rk] GET /admin/check-updates | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [fkpcpmpir7v] GET /admin/dashboard | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [fkpcpmpir7v] GET /admin/dashboard | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [mn0wrfqohyo] GET /admin/usage-costs?period=today | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [mn0wrfqohyo] GET /admin/usage-costs?period=today | IP: *************"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Calculating usage costs for period: today | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [nk0w2bj2kh9] GET /admin/usage-costs?period=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [nk0w2bj2kh9] GET /admin/usage-costs?period=all | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [1dzma5yva0m] GET /admin/model-stats?period=monthly | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [1dzma5yva0m] GET /admin/model-stats?period=monthly | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [20bug7u52bl] GET /admin/usage-trend?granularity=day&days=7 | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [20bug7u52bl] GET /admin/usage-trend?granularity=day&days=7 | IP: *************"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 49ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Calculating usage costs for period: all | {"metadata":{}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 50ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Getting global model stats, period: monthly, today: 2025-08-02, currentMonth: 2025-08 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Searching pattern: usage:model:monthly:*:2025-08 | {"metadata":{}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 51ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Found 2 matching keys: | {"metadata":{"0":"usage:model:monthly:claude-sonnet-4-********:2025-08","1":"usage:model:monthly:claude-3-5-haiku-********:2025-08"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Model claude-sonnet-4-******** data: | {"metadata":{"inputTokens":"3","outputTokens":"30","cacheCreateTokens":"18912","cacheReadTokens":"0","allTokens":"18945","requests":"1"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Model claude-3-5-haiku-******** data: | {"metadata":{"inputTokens":"103","outputTokens":"26","cacheCreateTokens":"0","cacheReadTokens":"0","allTokens":"129","requests":"1"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Returning 2 global model stats for period monthly: | {"metadata":{"0":{"model":"claude-sonnet-4-********","period":"monthly","requests":1,"inputTokens":3,"outputTokens":30,"cacheCreateTokens":18912,"cacheReadTokens":0,"allTokens":18945,"usage":{"requests":1,"inputTokens":3,"outputTokens":30,"cacheCreateTokens":18912,"cacheReadTokens":0,"totalTokens":18945},"costs":{"input":0.000009,"output":0.00045,"cacheWrite":0.07092000000000001,"cacheRead":0,"total":0.07137900000000001},"formatted":{"input":"$0.000009","output":"$0.000450","cacheWrite":"$0.0709","cacheRead":"$0.000000","total":"$0.0714"},"pricing":{"input":3,"output":15,"cacheWrite":3.75,"cacheRead":0.3}},"1":{"model":"claude-3-5-haiku-********","period":"monthly","requests":1,"inputTokens":103,"outputTokens":26,"cacheCreateTokens":0,"cacheReadTokens":0,"allTokens":129,"usage":{"requests":1,"inputTokens":103,"outputTokens":26,"cacheCreateTokens":0,"cacheReadTokens":0,"totalTokens":129},"costs":{"input":0.00008239999999999998,"output":0.000104,"cacheWrite":0,"cacheRead":0,"total":0.00018639999999999998},"formatted":{"input":"$0.000082","output":"$0.000104","cacheWrite":"$0.000000","cacheRead":"$0.000000","total":"$0.000186"},"pricing":{"input":0.7999999999999999,"output":4,"cacheWrite":1,"cacheRead":0.08}}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [1dzma5yva0m] GET /admin/model-stats?period=monthly | 200 | 63ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [1dzma5yva0m] GET /admin/model-stats?period=monthly | 200 | 63ms | 0B","url":{"requestId":"1dzma5yva0m","method":"GET","url":"/admin/model-stats?period=monthly","status":200,"duration":63,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [f63ibwp8zt] GET /admin/api-keys-usage-trend?granularity=day&days=7 | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [f63ibwp8zt] GET /admin/api-keys-usage-trend?granularity=day&days=7 | IP: *************"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 0ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Getting API keys usage trend, granularity: day, days: 7 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [fkpcpmpir7v] GET /admin/dashboard | 200 | 72ms | 798B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [fkpcpmpir7v] GET /admin/dashboard | 200 | 72ms | 798B","url":{"requestId":"fkpcpmpir7v","method":"GET","url":"/admin/dashboard","status":200,"duration":72,"contentLength":"798","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [llpn78nplq] GET /admin/model-stats?period=monthly | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [llpn78nplq] GET /admin/model-stats?period=monthly | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Total period calculation: found 5 monthly model keys | {"metadata":{}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Getting global model stats, period: monthly, today: 2025-08-02, currentMonth: 2025-08 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Searching pattern: usage:model:monthly:*:2025-08 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Found 2 matching keys: | {"metadata":{"0":"usage:model:monthly:claude-sonnet-4-********:2025-08","1":"usage:model:monthly:claude-3-5-haiku-********:2025-08"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Model claude-sonnet-4-******** data: | {"metadata":{"inputTokens":"3","outputTokens":"30","cacheCreateTokens":"18912","cacheReadTokens":"0","allTokens":"18945","requests":"1"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Model claude-3-5-haiku-******** data: | {"metadata":{"inputTokens":"103","outputTokens":"26","cacheCreateTokens":"0","cacheReadTokens":"0","allTokens":"129","requests":"1"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Returning 2 global model stats for period monthly: | {"metadata":{"0":{"model":"claude-sonnet-4-********","period":"monthly","requests":1,"inputTokens":3,"outputTokens":30,"cacheCreateTokens":18912,"cacheReadTokens":0,"allTokens":18945,"usage":{"requests":1,"inputTokens":3,"outputTokens":30,"cacheCreateTokens":18912,"cacheReadTokens":0,"totalTokens":18945},"costs":{"input":0.000009,"output":0.00045,"cacheWrite":0.07092000000000001,"cacheRead":0,"total":0.07137900000000001},"formatted":{"input":"$0.000009","output":"$0.000450","cacheWrite":"$0.0709","cacheRead":"$0.000000","total":"$0.0714"},"pricing":{"input":3,"output":15,"cacheWrite":3.75,"cacheRead":0.3}},"1":{"model":"claude-3-5-haiku-********","period":"monthly","requests":1,"inputTokens":103,"outputTokens":26,"cacheCreateTokens":0,"cacheReadTokens":0,"allTokens":129,"usage":{"requests":1,"inputTokens":103,"outputTokens":26,"cacheCreateTokens":0,"cacheReadTokens":0,"totalTokens":129},"costs":{"input":0.00008239999999999998,"output":0.000104,"cacheWrite":0,"cacheRead":0,"total":0.00018639999999999998},"formatted":{"input":"$0.000082","output":"$0.000104","cacheWrite":"$0.000000","cacheRead":"$0.000000","total":"$0.000186"},"pricing":{"input":0.7999999999999999,"output":4,"cacheWrite":1,"cacheRead":0.08}}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [mn0wrfqohyo] GET /admin/usage-costs?period=today | 200 | 81ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [mn0wrfqohyo] GET /admin/usage-costs?period=today | 200 | 81ms | 0B","url":{"requestId":"mn0wrfqohyo","method":"GET","url":"/admin/usage-costs?period=today","status":200,"duration":81,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Processing 3 unique models for total cost calculation | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Model claude-opus-4-********: 215473 tokens, cost: $4.06 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Model claude-sonnet-4-********: 216449415 tokens, cost: $773.32 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 💰 Model claude-3-5-haiku-********: 892735 tokens, cost: $0.7652 | {"metadata":{}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [llpn78nplq] GET /admin/model-stats?period=monthly | 200 | 9ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [llpn78nplq] GET /admin/model-stats?period=monthly | 200 | 9ms | 0B","url":{"requestId":"llpn78nplq","method":"GET","url":"/admin/model-stats?period=monthly","status":200,"duration":9,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [nk0w2bj2kh9] GET /admin/usage-costs?period=all | 200 | 83ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [nk0w2bj2kh9] GET /admin/usage-costs?period=all | 200 | 83ms | 0B","url":{"requestId":"nk0w2bj2kh9","method":"GET","url":"/admin/usage-costs?period=all","status":200,"duration":83,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [f63ibwp8zt] GET /admin/api-keys-usage-trend?granularity=day&days=7 | 200 | 22ms | 898B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [f63ibwp8zt] GET /admin/api-keys-usage-trend?granularity=day&days=7 | 200 | 22ms | 898B","url":{"requestId":"f63ibwp8zt","method":"GET","url":"/admin/api-keys-usage-trend?granularity=day&days=7","status":200,"duration":22,"contentLength":"898","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [20bug7u52bl] GET /admin/usage-trend?granularity=day&days=7 | 200 | 87ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [20bug7u52bl] GET /admin/usage-trend?granularity=day&days=7 | 200 | 87ms | 0B","url":{"requestId":"20bug7u52bl","method":"GET","url":"/admin/usage-trend?granularity=day&days=7","status":200,"duration":87,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [8yciy222ri] GET /admin/api-keys-usage-trend?granularity=day&days=7 | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [8yciy222ri] GET /admin/api-keys-usage-trend?granularity=day&days=7 | IP: *************"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ▶️ [fxlzmqj6nis] GET /admin/usage-trend?granularity=day&days=7 | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [fxlzmqj6nis] GET /admin/usage-trend?granularity=day&days=7 | IP: *************"}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 📊 Getting API keys usage trend, granularity: day, days: 7 | {"metadata":{}}
⚠️  [2025-08-02 12:30:44] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [8yciy222ri] GET /admin/api-keys-usage-trend?granularity=day&days=7 | 200 | 10ms | 898B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [8yciy222ri] GET /admin/api-keys-usage-trend?granularity=day&days=7 | 200 | 10ms | 898B","url":{"requestId":"8yciy222ri","method":"GET","url":"/admin/api-keys-usage-trend?granularity=day&days=7","status":200,"duration":10,"contentLength":"898","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:44] INFO: 🟢 ◀️ [fxlzmqj6nis] GET /admin/usage-trend?granularity=day&days=7 | 200 | 14ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [fxlzmqj6nis] GET /admin/usage-trend?granularity=day&days=7 | 200 | 14ms | 0B","url":{"requestId":"fxlzmqj6nis","method":"GET","url":"/admin/usage-trend?granularity=day&days=7","status":200,"duration":14,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ◀️ [ut0b7b5u4rk] GET /admin/check-updates | 200 | 847ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [ut0b7b5u4rk] GET /admin/check-updates | 200 | 847ms | 0B","url":{"requestId":"ut0b7b5u4rk","method":"GET","url":"/admin/check-updates","status":200,"duration":847,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web"}}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ▶️ [w5lf80jgn6] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [w5lf80jgn6] GET /admin/api-keys?timeRange=all | IP: *************"}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ▶️ [jcy8kpjjf9] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [jcy8kpjjf9] GET /admin/claude-accounts | IP: *************"}}
⚠️  [2025-08-02 12:30:45] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:45] WARN: 🔒 🔐 Admin authenticated: cr_admin in 3ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ▶️ [rgqolv0xjxd] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [rgqolv0xjxd] GET /admin/gemini-accounts | IP: *************"}}
⚠️  [2025-08-02 12:30:45] WARN: 🔒 🔐 Admin authenticated: cr_admin in 0ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ◀️ [rgqolv0xjxd] GET /admin/gemini-accounts | 200 | 2ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [rgqolv0xjxd] GET /admin/gemini-accounts | 200 | 2ms | 26B","url":{"requestId":"rgqolv0xjxd","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":2,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=apiKeys"}}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ◀️ [jcy8kpjjf9] GET /admin/claude-accounts | 200 | 10ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [jcy8kpjjf9] GET /admin/claude-accounts | 200 | 10ms | 0B","url":{"requestId":"jcy8kpjjf9","method":"GET","url":"/admin/claude-accounts","status":200,"duration":10,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=apiKeys"}}}
ℹ️  [2025-08-02 12:30:45] INFO: 🟢 ◀️ [w5lf80jgn6] GET /admin/api-keys?timeRange=all | 200 | 15ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [w5lf80jgn6] GET /admin/api-keys?timeRange=all | 200 | 15ms | 0B","url":{"requestId":"w5lf80jgn6","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":15,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=apiKeys"}}}
ℹ️  [2025-08-02 12:30:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:30:47] INFO: 🟢 ▶️ [baivw96o9vj] GET /admin/claude-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [baivw96o9vj] GET /admin/claude-accounts | IP: *************"}}
ℹ️  [2025-08-02 12:30:47] INFO: 🟢 ▶️ [dks8v1dxbz4] GET /admin/gemini-accounts | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [dks8v1dxbz4] GET /admin/gemini-accounts | IP: *************"}}
⚠️  [2025-08-02 12:30:47] WARN: 🔒 🔐 Admin authenticated: cr_admin in 4ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
⚠️  [2025-08-02 12:30:47] WARN: 🔒 🔐 Admin authenticated: cr_admin in 2ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:47] INFO: 🟢 ◀️ [dks8v1dxbz4] GET /admin/gemini-accounts | 200 | 5ms | 26B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [dks8v1dxbz4] GET /admin/gemini-accounts | 200 | 5ms | 26B","url":{"requestId":"dks8v1dxbz4","method":"GET","url":"/admin/gemini-accounts","status":200,"duration":5,"contentLength":"26","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 12:30:47] INFO: 🟢 ▶️ [6sp9hkfaam] GET /admin/api-keys?timeRange=all | IP: ************* undefined - undefined (undefinedms) | {"metadata":{"type":"request","method":"▶️ [6sp9hkfaam] GET /admin/api-keys?timeRange=all | IP: *************"}}
⚠️  [2025-08-02 12:30:47] WARN: 🔒 🔐 Admin authenticated: cr_admin in 1ms | {"metadata":{"type":"security","pid":7,"hostname":"144806c5e00a"}}
ℹ️  [2025-08-02 12:30:47] INFO: 🟢 ◀️ [baivw96o9vj] GET /admin/claude-accounts | 200 | 11ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [baivw96o9vj] GET /admin/claude-accounts | 200 | 11ms | 0B","url":{"requestId":"baivw96o9vj","method":"GET","url":"/admin/claude-accounts","status":200,"duration":11,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 12:30:47] INFO: 🟢 ◀️ [6sp9hkfaam] GET /admin/api-keys?timeRange=all | 200 | 6ms | 0B [object Object] - undefined (undefinedms) | {"metadata":{"type":"request","method":"◀️ [6sp9hkfaam] GET /admin/api-keys?timeRange=all | 200 | 6ms | 0B","url":{"requestId":"6sp9hkfaam","method":"GET","url":"/admin/api-keys?timeRange=all","status":200,"duration":6,"contentLength":"0","ip":"*************","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://127.0.0.1:3001/web?tab=accounts"}}}
ℹ️  [2025-08-02 12:31:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:31:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:32:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:32:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:33:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:33:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:34:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:34:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:35:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:35:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:36:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:36:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:37:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:37:47] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:38:17] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:38:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:39:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:39:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:40:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:40:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:41:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:41:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:42:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:42:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:43:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:43:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:44:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:44:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:45:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:45:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:46:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:46:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:47:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:47:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:48:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:48:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:49:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:49:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:50:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:50:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:51:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:51:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:52:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:52:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:53:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:53:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:54:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:54:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:55:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:55:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:56:18] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:56:48] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:57:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:57:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
ℹ️  [2025-08-02 12:58:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:58:49] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":0}}
ℹ️  [2025-08-02 12:59:19] INFO: ⚡ health-check completed | {"metadata":{"type":"performance","duration":1}}
