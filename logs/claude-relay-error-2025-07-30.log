❌ [2025-07-30 02:34:06] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 02:34:06] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 02:34:06] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 02:34:06] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 02:34:08] ERROR: ◀️ [wcj5b5wr2iq] POST /api/v1/messages?beta=true | 502 | 1358ms | 42B | {"metadata":{"requestId":"wcj5b5wr2iq","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1358,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 02:37:09] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 02:37:09] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 02:37:09] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 02:37:09] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 02:38:01] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 02:38:01] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 02:38:01] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 02:38:01] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 02:38:07] ERROR: ◀️ [vsuy5xbwz4] POST /api/v1/messages?beta=true | 502 | 6136ms | 42B | {"metadata":{"requestId":"vsuy5xbwz4","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":6136,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 03:30:32] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:31:03] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 03:31:03] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 03:31:03] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:31:03] ERROR: ◀️ [j94ocsbjmu] POST /api/v1/messages?beta=true | 504 | 30532ms | 95B | {"metadata":{"requestId":"j94ocsbjmu","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30532,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 03:31:03] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 03:31:08] ERROR: ◀️ [8ushx87cj2] POST /api/v1/messages?beta=true | 502 | 4670ms | 42B | {"metadata":{"requestId":"8ushx87cj2","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":4670,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 03:39:03] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 03:39:03] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 03:39:03] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 03:39:03] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:40:50] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 03:40:50] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 03:40:50] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 03:40:50] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:40:51] ERROR: ◀️ [fcgsdteysd] POST /api/v1/messages?beta=true | 502 | 1347ms | 42B | {"metadata":{"requestId":"fcgsdteysd","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1347,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 03:46:13] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 03:46:13] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 03:46:13] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 03:46:13] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:46:14] ERROR: ◀️ [83pcrfxzuvh] POST /api/v1/messages?beta=true | 502 | 1375ms | 42B | {"metadata":{"requestId":"83pcrfxzuvh","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1375,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 03:53:55] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 03:53:55] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 03:53:55] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 03:53:55] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 03:53:56] ERROR: ◀️ [vq7wz1l6e7i] POST /api/v1/messages?beta=true | 502 | 1395ms | 42B | {"metadata":{"requestId":"vq7wz1l6e7i","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1395,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 04:09:19] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-30 04:09:19] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-30 04:09:19] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:02:21] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-30 06:02:21] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-30 06:02:21] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:46:35] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 06:46:35] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:46:35] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:46:35] ERROR: ◀️ [xv5yq2p0htk] POST /api/v1/messages?beta=true | 504 | 30563ms | 95B | {"metadata":{"requestId":"xv5yq2p0htk","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30563,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 06:46:35] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:54:01] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 06:54:01] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:54:01] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:54:01] ERROR: ◀️ [9tn29lvaxuh] POST /api/v1/messages?beta=true | 504 | 30523ms | 95B | {"metadata":{"requestId":"9tn29lvaxuh","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30523,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 06:54:01] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:56:04] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-30 06:56:04] ERROR: ◀️ [61vm55o4zkf] POST /api/v1/messages?beta=true | 504 | 30545ms | 0B | {"metadata":{"requestId":"61vm55o4zkf","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30545,"contentLength":"0","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 06:56:04] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-30 06:56:04] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:56:04] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-30 06:56:34] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 06:56:34] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:56:34] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:56:34] ERROR: ◀️ [2fssnovidid] POST /api/v1/messages?beta=true | 504 | 30532ms | 95B | {"metadata":{"requestId":"2fssnovidid","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30532,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 06:56:34] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:57:06] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 06:57:06] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:57:06] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:57:06] ERROR: ◀️ [70a26xhrzw4] POST /api/v1/messages?beta=true | 504 | 30521ms | 95B | {"metadata":{"requestId":"70a26xhrzw4","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30521,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 06:57:06] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:57:13] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:57:13] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:57:13] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:59:13] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 06:59:13] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:59:13] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 06:59:13] ERROR: ◀️ [8iwnxtsgsat] POST /api/v1/messages?beta=true | 504 | 30532ms | 95B | {"metadata":{"requestId":"8iwnxtsgsat","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30532,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 06:59:13] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:59:44] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 06:59:44] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 06:59:44] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 13:41:35] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 13:41:35] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 13:41:35] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 13:41:35] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 13:41:36] ERROR: ◀️ [5ytumh9s9by] POST /api/v1/messages?beta=true | 502 | 1219ms | 42B | {"metadata":{"requestId":"5ytumh9s9by","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1219,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:41:38] ERROR: ◀️ [lf2yiu6b9di] POST /api/v1/messages?beta=true | 502 | 1228ms | 42B | {"metadata":{"requestId":"lf2yiu6b9di","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1228,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:42:42] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 13:42:42] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 13:42:42] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 13:42:42] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 13:42:44] ERROR: ◀️ [7g6534mbr6i] POST /api/v1/messages?beta=true | 502 | 1432ms | 42B | {"metadata":{"requestId":"7g6534mbr6i","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1432,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:42:45] ERROR: ◀️ [pkryymtm3r] POST /api/v1/messages?beta=true | 502 | 1410ms | 42B | {"metadata":{"requestId":"pkryymtm3r","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1410,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:42:48] ERROR: ◀️ [zzc1iy25lg8] POST /api/v1/messages?beta=true | 502 | 1633ms | 42B | {"metadata":{"requestId":"zzc1iy25lg8","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1633,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:42:52] ERROR: ◀️ [1j6j6gvg6noi] POST /api/v1/messages?beta=true | 502 | 1351ms | 42B | {"metadata":{"requestId":"1j6j6gvg6noi","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1351,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:42:58] ERROR: ◀️ [x80c5dv7fd7] POST /api/v1/messages?beta=true | 502 | 1435ms | 42B | {"metadata":{"requestId":"x80c5dv7fd7","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1435,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:43:08] ERROR: ◀️ [3pl7gv2m59r] POST /api/v1/messages?beta=true | 502 | 1501ms | 42B | {"metadata":{"requestId":"3pl7gv2m59r","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1501,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:48:02] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 13:48:02] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 13:48:02] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 13:48:02] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 13:48:03] ERROR: ◀️ [5fmzoztfzri] POST /api/v1/messages?beta=true | 502 | 1432ms | 42B | {"metadata":{"requestId":"5fmzoztfzri","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1432,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:48:05] ERROR: ◀️ [ub3bt1newer] POST /api/v1/messages?beta=true | 502 | 1425ms | 42B | {"metadata":{"requestId":"ub3bt1newer","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1425,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:48:24] ERROR: ◀️ [ok0zvsl0rme] POST /api/v1/messages?beta=true | 502 | 17240ms | 42B | {"metadata":{"requestId":"ok0zvsl0rme","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":17240,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 13:50:34] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-30 13:50:34] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-30 13:50:34] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-30 13:50:34] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 13:50:37] ERROR: ◀️ [7j06g8wcnwg] POST /api/v1/messages?beta=true | 502 | 2524ms | 42B | {"metadata":{"requestId":"7j06g8wcnwg","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":2524,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 14:18:36] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-30 14:18:36] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-30 14:18:36] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:00:44] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:00:44] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:00:44] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:00:44] ERROR: ◀️ [83bfw0s5n3t] POST /api/v1/messages?beta=true | 504 | 30565ms | 95B | {"metadata":{"requestId":"83bfw0s5n3t","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30565,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:00:44] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:01:15] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:01:15] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:01:15] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:01:15] ERROR: ◀️ [59sotp6vwdr] POST /api/v1/messages?beta=true | 504 | 30539ms | 95B | {"metadata":{"requestId":"59sotp6vwdr","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30539,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:01:15] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:01:47] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:01:47] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:01:47] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:01:47] ERROR: ◀️ [qufgslyrufa] POST /api/v1/messages?beta=true | 504 | 30544ms | 95B | {"metadata":{"requestId":"qufgslyrufa","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30544,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:01:47] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:02:20] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:02:20] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:02:20] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:02:20] ERROR: ◀️ [a2uav584e] POST /api/v1/messages?beta=true | 504 | 30549ms | 95B | {"metadata":{"requestId":"a2uav584e","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30549,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:02:20] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:02:55] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:02:55] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:02:55] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:02:55] ERROR: ◀️ [eqv1f9q0px9] POST /api/v1/messages?beta=true | 504 | 30536ms | 95B | {"metadata":{"requestId":"eqv1f9q0px9","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30536,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:02:55] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:03:35] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:03:35] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:03:35] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:03:35] ERROR: ◀️ [vqulfxxa3wf] POST /api/v1/messages?beta=true | 504 | 30520ms | 95B | {"metadata":{"requestId":"vqulfxxa3wf","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30520,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:03:35] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:04:25] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:04:25] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:04:25] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:04:25] ERROR: ◀️ [lw7ifh4yrdo] POST /api/v1/messages?beta=true | 504 | 30550ms | 95B | {"metadata":{"requestId":"lw7ifh4yrdo","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30550,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:04:25] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:05:30] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:05:30] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:05:30] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:05:30] ERROR: ◀️ [1nvpn74krc8] POST /api/v1/messages?beta=true | 504 | 30541ms | 95B | {"metadata":{"requestId":"1nvpn74krc8","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30541,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:05:30] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:06:40] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:06:40] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:06:40] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:06:40] ERROR: ◀️ [acopwelgmza] POST /api/v1/messages?beta=true | 504 | 30571ms | 95B | {"metadata":{"requestId":"acopwelgmza","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30571,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:06:40] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:07:45] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:07:45] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:07:45] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:07:45] ERROR: ◀️ [e7lk7y9nmy8] POST /api/v1/messages?beta=true | 504 | 30531ms | 95B | {"metadata":{"requestId":"e7lk7y9nmy8","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30531,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:07:45] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 15:08:55] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 15:08:55] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 15:08:55] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 15:08:55] ERROR: ◀️ [eo8v7gx1h4c] POST /api/v1/messages?beta=true | 504 | 30541ms | 95B | {"metadata":{"requestId":"eo8v7gx1h4c","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30541,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 15:08:55] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:14:56] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-30 16:14:56] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-30 16:14:56] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:20:54] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-30 16:20:54] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-30 16:20:54] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:21:25] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:21:25] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:21:25] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:21:25] ERROR: ◀️ [vsje8482ss] POST /api/v1/messages?beta=true | 504 | 30492ms | 95B | {"metadata":{"requestId":"vsje8482ss","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30492,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:21:25] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:21:56] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:21:56] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:21:56] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:21:56] ERROR: ◀️ [s3u0zlzu6r] POST /api/v1/messages?beta=true | 504 | 30614ms | 95B | {"metadata":{"requestId":"s3u0zlzu6r","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30614,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:21:56] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:22:28] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:22:28] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:22:28] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:22:28] ERROR: ◀️ [w1ofkd2vg4e] POST /api/v1/messages?beta=true | 504 | 30493ms | 95B | {"metadata":{"requestId":"w1ofkd2vg4e","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30493,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:22:28] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:23:01] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:23:01] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:23:01] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:23:01] ERROR: ◀️ [j70g1iu8e] POST /api/v1/messages?beta=true | 504 | 30629ms | 95B | {"metadata":{"requestId":"j70g1iu8e","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30629,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:23:01] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:23:36] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:23:36] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:23:36] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:23:36] ERROR: ◀️ [up4x4xlbu] POST /api/v1/messages?beta=true | 504 | 30709ms | 95B | {"metadata":{"requestId":"up4x4xlbu","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30709,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:23:36] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:24:17] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:24:17] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:24:17] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:24:17] ERROR: ◀️ [aqkvl5khopv] POST /api/v1/messages?beta=true | 504 | 30510ms | 95B | {"metadata":{"requestId":"aqkvl5khopv","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30510,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:24:17] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:25:04] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:25:04] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:25:04] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:25:04] ERROR: ◀️ [bad2xeq8eb7] POST /api/v1/messages?beta=true | 504 | 30544ms | 95B | {"metadata":{"requestId":"bad2xeq8eb7","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30544,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:25:04] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:26:13] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:26:13] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:26:13] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:26:13] ERROR: ◀️ [zllws029t4] POST /api/v1/messages?beta=true | 504 | 30563ms | 95B | {"metadata":{"requestId":"zllws029t4","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30563,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:26:13] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:27:17] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:27:17] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:27:17] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:27:17] ERROR: ◀️ [q0zqlr4cmr] POST /api/v1/messages?beta=true | 504 | 30526ms | 95B | {"metadata":{"requestId":"q0zqlr4cmr","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30526,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:27:17] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:28:23] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:28:23] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:28:23] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:28:23] ERROR: ◀️ [xuz4u3mgdbb] POST /api/v1/messages?beta=true | 504 | 30495ms | 95B | {"metadata":{"requestId":"xuz4u3mgdbb","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30495,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:28:23] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:29:27] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:29:27] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:29:27] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:29:27] ERROR: ◀️ [dmjhgd2fmt] POST /api/v1/messages?beta=true | 504 | 30513ms | 95B | {"metadata":{"requestId":"dmjhgd2fmt","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30513,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:29:27] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:30:38] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-30 16:30:38] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-30 16:30:38] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:31:09] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:31:09] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:31:09] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:31:09] ERROR: ◀️ [q5ssv8yo68] POST /api/v1/messages?beta=true | 504 | 30508ms | 95B | {"metadata":{"requestId":"q5ssv8yo68","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30508,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:31:09] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:31:40] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:31:40] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:31:40] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:31:40] ERROR: ◀️ [ayfmsx52cq] POST /api/v1/messages?beta=true | 504 | 30659ms | 95B | {"metadata":{"requestId":"ayfmsx52cq","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30659,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:31:40] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:32:12] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:32:12] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:32:12] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:32:12] ERROR: ◀️ [rsord2qf93k] POST /api/v1/messages?beta=true | 504 | 30517ms | 95B | {"metadata":{"requestId":"rsord2qf93k","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30517,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:32:12] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:32:45] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:32:45] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:32:45] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:32:45] ERROR: ◀️ [p79bbyxubl] POST /api/v1/messages?beta=true | 504 | 30544ms | 95B | {"metadata":{"requestId":"p79bbyxubl","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30544,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:32:45] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-30 16:33:19] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-30 16:33:19] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-30 16:33:19] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-30 16:33:19] ERROR: ◀️ [qt0zj3pf3lf] POST /api/v1/messages?beta=true | 504 | 30508ms | 95B | {"metadata":{"requestId":"qt0zj3pf3lf","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30508,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-30 16:33:19] ERROR: ❌ Claude API request error: | {"metadata":{}}
