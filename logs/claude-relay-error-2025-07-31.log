❌ [2025-07-31 00:30:20] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-31 00:30:20] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-31 00:30:20] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:32:06] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-31 01:32:06] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-31 01:32:06] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:33:28] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-31 01:33:28] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-31 01:33:28] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:36:34] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-31 01:36:34] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-31 01:36:34] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:39:02] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-31 01:39:02] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-31 01:39:02] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:39:32] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 01:39:32] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 01:39:32] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:39:32] ERROR: ◀️ [0wueyyflcudr] POST /api/v1/messages?beta=true | 504 | 30517ms | 95B | {"metadata":{"requestId":"0wueyyflcudr","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30517,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 01:39:32] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 01:40:03] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 01:40:03] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 01:40:03] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:40:03] ERROR: ◀️ [u1757j8yup8] POST /api/v1/messages?beta=true | 504 | 30506ms | 95B | {"metadata":{"requestId":"u1757j8yup8","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30506,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 01:40:03] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 01:40:35] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 01:40:35] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 01:40:35] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:40:35] ERROR: ◀️ [0of2gv7uckw] POST /api/v1/messages?beta=true | 504 | 30531ms | 95B | {"metadata":{"requestId":"0of2gv7uckw","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30531,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 01:40:35] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 01:41:08] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 01:41:08] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 01:41:08] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:41:08] ERROR: ◀️ [ggqc5jmacub] POST /api/v1/messages?beta=true | 504 | 30528ms | 95B | {"metadata":{"requestId":"ggqc5jmacub","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30528,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 01:41:08] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 01:41:43] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 01:41:43] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 01:41:43] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 01:41:43] ERROR: ◀️ [1mq13k078f6] POST /api/v1/messages?beta=true | 504 | 30544ms | 95B | {"metadata":{"requestId":"1mq13k078f6","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30544,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 01:41:43] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:49:02] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-31 04:49:02] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-31 04:49:02] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 04:49:32] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 04:49:32] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 04:49:32] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 04:49:32] ERROR: ◀️ [u2ltyqx055] POST /api/v1/messages?beta=true | 504 | 30543ms | 95B | {"metadata":{"requestId":"u2ltyqx055","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30543,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 04:49:32] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:50:03] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 04:50:03] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 04:50:03] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 04:50:03] ERROR: ◀️ [ysv5apnqfke] POST /api/v1/messages?beta=true | 504 | 30533ms | 95B | {"metadata":{"requestId":"ysv5apnqfke","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30533,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 04:50:03] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:50:35] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 04:50:35] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 04:50:35] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 04:50:35] ERROR: ◀️ [kmo98dq7zlr] POST /api/v1/messages?beta=true | 504 | 30506ms | 95B | {"metadata":{"requestId":"kmo98dq7zlr","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30506,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 04:50:35] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:51:08] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 04:51:08] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 04:51:08] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 04:51:08] ERROR: ◀️ [e47edtwbuoi] POST /api/v1/messages?beta=true | 504 | 30503ms | 95B | {"metadata":{"requestId":"e47edtwbuoi","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30503,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 04:51:08] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:51:43] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-31 04:51:43] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 04:51:43] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 04:51:43] ERROR: ◀️ [yuzo0ms9ac] POST /api/v1/messages?beta=true | 504 | 30543ms | 95B | {"metadata":{"requestId":"yuzo0ms9ac","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30543,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 04:51:43] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:52:20] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-31 04:52:20] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-31 04:52:20] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 07:24:08] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-31 07:24:08] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-31 07:24:08] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-31 07:24:08] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 07:24:09] ERROR: ◀️ [5oecacjyfjh] POST /api/v1/messages?beta=true | 502 | 1350ms | 42B | {"metadata":{"requestId":"5oecacjyfjh","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":1350,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-31 07:26:45] ERROR: ❌ Claude API returned error status: 400 | {"metadata":{}}
❌ [2025-07-31 07:26:45] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-31 07:26:45] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 400 | {"metadata":{}}
Error: Claude API error: 400
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-31 07:26:45] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-31 23:26:08] ERROR: ❌ Failed to check/update pricing: Failed to download pricing data: connect ECONNREFUSED ***************:443 | {"metadata":{}}
Error: Failed to download pricing data: connect ECONNREFUSED ***************:443
    at ClientRequest.<anonymous> (/app/src/services/pricingService.js:110:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.socketErrorListener (node:_http_client:501:9)
    at TLSSocket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
