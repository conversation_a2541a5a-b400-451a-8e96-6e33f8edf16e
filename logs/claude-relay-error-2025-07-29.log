❌ [2025-07-29 03:26:36] ERROR: ❌ Failed to check for updates: | {"metadata":{}}
❌ [2025-07-29 03:26:44] ERROR: ❌ Failed to check for updates: | {"metadata":{}}
❌ [2025-07-29 03:26:46] ERROR: ❌ Failed to check for updates: | {"metadata":{}}
❌ [2025-07-29 03:26:49] ERROR: ❌ Failed to check for updates: | {"metadata":{}}
❌ [2025-07-29 03:41:07] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 03:41:07] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 03:41:07] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 08:04:10] ERROR: ❌ Claude API returned error status: 400 | {"metadata":{}}
❌ [2025-07-29 08:04:10] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-29 08:04:10] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 400 | {"metadata":{}}
Error: Claude API error: 400
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-29 08:04:10] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 08:07:24] ERROR: ❌ Claude API returned error status: 400 | {"metadata":{}}
❌ [2025-07-29 08:07:24] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-29 08:07:24] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 400 | {"metadata":{}}
Error: Claude API error: 400
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-29 08:07:24] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 08:07:43] ERROR: ❌ Claude API returned error status: 400 | {"metadata":{}}
❌ [2025-07-29 08:07:43] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-29 08:07:43] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 400 | {"metadata":{}}
Error: Claude API error: 400
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-29 08:07:43] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 12:10:31] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 12:10:31] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 12:10:31] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 12:13:07] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 12:13:07] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 12:13:07] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 12:40:39] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 12:40:39] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 12:40:39] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 12:40:54] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 12:40:54] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 12:40:54] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 12:42:20] ERROR: ❌ Claude API returned error status: 400 | {"metadata":{}}
❌ [2025-07-29 12:42:20] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-29 12:42:20] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 400 | {"metadata":{}}
Error: Claude API error: 400
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-29 12:42:20] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 12:57:14] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 12:57:14] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 12:57:14] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 13:32:50] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 13:32:50] ERROR: ◀️ [vbk88lrdff] POST /api/v1/messages?beta=true | 504 | 30545ms | 0B | {"metadata":{"requestId":"vbk88lrdff","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30545,"contentLength":"0","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 13:32:50] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 13:32:50] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 13:32:50] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 13:48:58] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 13:48:58] ERROR: ◀️ [ujivdvje6u8] POST /api/v1/messages?beta=true | 504 | 30519ms | 0B | {"metadata":{"requestId":"ujivdvje6u8","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30519,"contentLength":"0","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 13:48:58] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 13:48:58] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 13:48:58] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 14:06:02] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 14:06:02] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 14:06:02] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 14:16:00] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 14:16:00] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 14:16:00] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 14:30:07] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 14:30:07] ERROR: ◀️ [33fq4zhjmbj] POST /api/v1/messages?beta=true | 504 | 30536ms | 0B | {"metadata":{"requestId":"33fq4zhjmbj","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30536,"contentLength":"0","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 14:30:07] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 14:30:07] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 14:30:07] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 14:30:38] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-29 14:30:38] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 14:30:38] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 14:30:38] ERROR: ◀️ [zqj9e1dm8q] POST /api/v1/messages?beta=true | 504 | 30563ms | 95B | {"metadata":{"requestId":"zqj9e1dm8q","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30563,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 14:30:38] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 14:31:09] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-29 14:31:09] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 14:31:09] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 14:31:09] ERROR: ◀️ [j3s7upa9ino] POST /api/v1/messages?beta=true | 504 | 30561ms | 95B | {"metadata":{"requestId":"j3s7upa9ino","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30561,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 14:31:09] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 14:31:21] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 14:31:21] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 14:31:21] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 14:36:47] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 14:36:47] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 14:36:47] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:23:33] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 15:23:33] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 15:23:33] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:32:38] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 15:32:38] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 15:32:38] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:33:42] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 15:33:42] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 15:33:42] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:35:15] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 15:35:15] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 15:35:15] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:35:46] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-29 15:35:46] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 15:35:46] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:35:46] ERROR: ◀️ [3z81m2au38f] POST /api/v1/messages?beta=true | 504 | 30518ms | 95B | {"metadata":{"requestId":"3z81m2au38f","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30518,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 15:35:46] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 15:35:54] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 15:35:54] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 15:35:54] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:37:37] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 15:37:37] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 15:37:37] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:38:08] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-29 15:38:08] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 15:38:08] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:38:08] ERROR: ◀️ [qzmvsvvo01g] POST /api/v1/messages?beta=true | 504 | 30492ms | 95B | {"metadata":{"requestId":"qzmvsvvo01g","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30492,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 15:38:08] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 15:38:19] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 15:38:19] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 15:38:19] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:48:48] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 15:48:48] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 15:48:48] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:49:41] ERROR: ❌ Claude API request timeout | {"metadata":{}}
❌ [2025-07-29 15:49:41] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 15:49:41] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 15:49:41] ERROR: ◀️ [q3ba550mktr] POST /api/v1/messages?beta=true | 504 | 30543ms | 95B | {"metadata":{"requestId":"q3ba550mktr","method":"POST","url":"/api/v1/messages?beta=true","status":504,"duration":30543,"contentLength":"95","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 15:49:41] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 15:49:56] ERROR: ❌ Claude API request error: | {"metadata":{}}
❌ [2025-07-29 15:49:56] ERROR: ❌ Claude relay request failed for key: 3: | {"metadata":{}}
❌ [2025-07-29 15:49:56] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 16:10:06] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 16:10:06] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 16:10:06] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 17:01:55] ERROR: ❌ Claude stream request timeout | {"metadata":{}}
❌ [2025-07-29 17:01:55] ERROR: ❌ Claude stream relay with usage capture failed: Request timeout | {"metadata":{}}
Error: Request timeout
    at ClientRequest.<anonymous> (/app/src/services/claudeRelayService.js:930:16)
    at ClientRequest.emit (node:events:517:28)
    at TLSSocket.emitRequestTimeout (node:_http_client:847:9)
    at Object.onceWrapper (node:events:631:28)
    at TLSSocket.emit (node:events:529:35)
    at Socket._onTimeout (node:net:598:8)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
❌ [2025-07-29 17:01:55] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 17:06:11] ERROR: ❌ Claude API returned error status: 502 | {"metadata":{}}
❌ [2025-07-29 17:06:11] ERROR: ❌ Claude API error response: | {"metadata":{}}
❌ [2025-07-29 17:06:11] ERROR: ❌ Claude stream relay with usage capture failed: Claude API error: 502 | {"metadata":{}}
Error: Claude API error: 502
    at IncomingMessage.<anonymous> (/app/src/services/claudeRelayService.js:722:20)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
❌ [2025-07-29 17:06:11] ERROR: ❌ Claude relay error: | {"metadata":{}}
❌ [2025-07-29 17:06:14] ERROR: ◀️ [44hbsbidqga] POST /api/v1/messages?beta=true | 502 | 2314ms | 42B | {"metadata":{"requestId":"44hbsbidqga","method":"POST","url":"/api/v1/messages?beta=true","status":502,"duration":2314,"contentLength":"42","ip":"*************","userAgent":"claude-cli/1.0.59 (external, cli)","referer":"none"}}
❌ [2025-07-29 17:08:13] ERROR: ❌ Claude stream request error: | {"metadata":{}}
❌ [2025-07-29 17:08:13] ERROR: ❌ Claude stream relay with usage capture failed: socket hang up | {"metadata":{"code":"ECONNRESET"}}
Error: socket hang up
    at connResetException (node:internal/errors:720:14)
    at TLSSocket.socketCloseListener (node:_http_client:474:25)
    at TLSSocket.emit (node:events:529:35)
    at node:net:350:12
    at Socket.done (node:_tls_wrap:657:7)
    at Object.onceWrapper (node:events:632:26)
    at Socket.emit (node:events:517:28)
    at TCP.<anonymous> (node:net:350:12)
❌ [2025-07-29 17:08:13] ERROR: ❌ Claude relay error: | {"metadata":{}}
