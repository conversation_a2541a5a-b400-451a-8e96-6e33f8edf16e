﻿var dfdPrompt = null;  
var installPromptDiv = document.getElementById('install-prompt');  

function shouldShowInstallPrompt() {  
    const oneDay = 720 * 60 * 60 * 1000; 
    const lastClosed = localStorage.getItem('installPromptClosed');  
    if (lastClosed) {  
        const now = Date.now();  
        return now - lastClosed > oneDay;  
    }  
    return true;  
}  
    
function showInstallPrompt() {  
    if (dfdPrompt && shouldShowInstallPrompt()) { 
        installPromptDiv.style.display = 'block';  
    }  
}  
    
function closePrompt() {  
    installPromptDiv.style.display = 'none';  
    localStorage.setItem('installPromptClosed', Date.now());  
}  
    
window.addEventListener('beforeinstallprompt', function (e) {  
    dfdPrompt = e;  
    e.preventDefault();  
    showInstallPrompt();  
});  
    
function addToDesktop() {  
    if (dfdPrompt) {  
        dfdPrompt.prompt();  
        dfdPrompt.userChoice.then(function (choiceResult) {  
            closePrompt();  
            dfdPrompt = null;  
        });  
    } else {  
        // 可以根据需要提示用户
    }  
}  
    
window.onload = function() {
    showInstallPrompt();
    document.getElementById('install-btn').addEventListener('click', addToDesktop);
    document.getElementById('close-install-prompt').addEventListener('click', closePrompt);
};