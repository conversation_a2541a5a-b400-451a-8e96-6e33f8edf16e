﻿/*!
 * Theme Name:One Nav
 * Theme URI:https://www.iotheme.cn/
 * Author:iowen
 * Author URI:https://www.iowen.cn/
 * Version: 4.0426
 */
function change_input(t){"true"==$(t).attr("data-status")&&$(t).val().length<=$(t).parent().attr("data-max")?$(t).parent().attr("data-min",$(t).val().length):"true"==$(t).attr("data-status")&&$(t).val($(t).val().substring(0,$(t).parent().attr("data-max")-1))}function load_rand_post(t){var e=loadingShow(t.id,!1);$.ajax({url:theme.ajaxurl,type:"POST",dataType:"html",data:{action:t.action,data:t}}).done(function(a){$(t.id+" .ajax-panel").html(a),loadingHid(e)}).fail(function(){loadingHid(e)})}function isURL(t){var e=t,a=/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;return 1==new RegExp(a).test(e)}function isPC(){for(var t=navigator.userAgent,e=["Android","iPhone","webOS","BlackBerry","SymbianOS","Windows Phone","iPad","iPod"],a=!0,i=0;i<e.length;i++)if(t.indexOf(e[i])>0){a=!1;break}return a}function chack_name(t){return!!RegExp(/[( )(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\*)(\()(\))(\+)(\=)(\[)(\])(\{)(\})(\\)(\;)(\:)(\')(\")(\,)(\.)(\/)(\<)(\>)(\»)(\«)(\“)(\”)(\?)(\)]+/).test(t)}function showAlert(t){var e,a;switch(t.status){case 0:title=localize.successAlert,e="primary",a="io-loading icon-spin";break;case 1:title=localize.successAlert,e="success",a="io-adopt";break;case 2:title=localize.infoAlert,e="info",a="io-tishi";break;case 3:title=localize.warningAlert,e="warning",a="io-warning";break;case 4:title=localize.errorAlert,e="danger",a="io-close"}var i=t.msg;$("#alert_placeholder")[0]||$("body").append('<div id="alert_placeholder" class="alert-system"></div>');var o=$('<div class="alert-body text-sm io-alert-'+e+" alert alert-"+e+' d-flex py-2 align-items-center"><i class="io '+a+' text-lg mr-2"></i><span class="mr-2">'+i+"</span></div>");removeAlert(),$("#alert_placeholder").append(o),"primary"==e?o.slideDown().addClass("show"):(o.slideDown().addClass("show"),setTimeout(function(){removeAlert(o)},3500))}function removeAlert(t){t||(t=$(".io-alert-primary")),t[0]&&(t.removeClass("show"),setTimeout(function(){t.remove()},300))}function toTarget(t,e,a){var i=t.children(".anchor"),o=t.children(".hover").first(),n=t.closest(".tab-auto-scrollbar");o&&0<o.length||(o=a?t.find(".active").parent():t.find(".active")),0<o.length?e?i.css({left:o.position().left+n.scrollLeft()+"px",width:o.outerWidth()+"px",opacity:"1"}):i.css({left:o.position().left+n.scrollLeft()+o.outerWidth()/4+"px",width:o.outerWidth()/2+"px",opacity:"1"}):i.css({opacity:"0"})}function loadingShow(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"body";if((!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&$(".load-loading")[0])return ioadindex++,$(".load-loading");var e=$('<div class="load-loading" style="display:none"><div class="bg"></div><div class="rounded-lg bg-light" style="z-index:1"><div class="spinner-border m-4" role="status"><span class="sr-only">Loading...</span></div></div></div>');return $(t).prepend(e),e.fadeIn(200),e}function loadingHid(t){ioadindex>0?ioadindex--:(ioadindex=0,t.fadeOut(300,function(){t.remove()}))}function ioPopupTips(t,e,a){var i="";switch(t){case 1:i="io-adopt";break;case 2:i="io-tishi";break;case 3:i="io-warning";break;case 4:i="io-close"}var o=1==t?"tips-success":"tips-error",n='<section class="io-bomb '+o+' io-bomb-sm io-bomb-open"><div class="io-bomb-overlay"></div><div class="io-bomb-body text-center"><div class="io-bomb-content bg-white px-5"><i class="io '+i+' icon-8x"></i><p class="text-md mt-3">'+e+"</p></div></div></section>",s=$(n);$("body").addClass("modal-open").append(s),hasScrollbar()&&$("body").css("padding-right",getScrollbarWidth()),setTimeout(function(){$("body").removeClass("modal-open"),hasScrollbar()&&$("body").css("padding-right",""),$.isFunction(a)&&a(!0),s.removeClass("io-bomb-open").addClass("io-bomb-close"),setTimeout(function(){s.removeClass("io-bomb-close"),setTimeout(function(){s.remove()},200)},400)},2e3)}function ioPopup(t,e,a,i){var a=a?'style="'+a+'"':"",o="";"big"==t?o="io-bomb-lg":"no-padding"==t?o="io-bomb-nopd":"cover"==t?o="io-bomb-cover io-bomb-nopd":"full"==t?o="io-bomb-xl":"small"==t?o="io-bomb-sm":"confirm"==t?o="io-bomb-md":"pay"==t&&(o="io-bomb-sm io-bomb-nopd");var n='\t<div class="io-bomb '+o+' io-bomb-open">\t\t<div class="io-bomb-overlay" '+a+'></div>\t\t<div class="io-bomb-body text-center">\t\t\t<div class="io-bomb-content bg-white">\t\t\t\t'+e+'\t\t\t</div>\t\t\t<div class="btn-close-bomb mt-2">                <i class="io io-close"></i>            </div>\t\t</div>\t</div>\t',s=$(n);$("body").addClass("modal-open").append(s),hasScrollbar()&&$("body").css("padding-right",getScrollbarWidth());var r=function(){$("body").removeClass("modal-open"),hasScrollbar()&&$("body").css("padding-right",""),$(s).removeClass("io-bomb-open").addClass("io-bomb-close"),setTimeout(function(){$(s).removeClass("io-bomb-close"),setTimeout(function(){s.remove()},200)},600)};return $(s).on("click touchstart",".btn-close-bomb i, .io-bomb-overlay",function(t){t.preventDefault(),$.isFunction(i)&&i(!0),r()}),s}function ioConfirm(t,e,a){var i='\t<div class="io-bomb io-bomb-confirm io-bomb-open">\t\t<div class="io-bomb-overlay"></div>\t\t<div class="io-bomb-body">\t\t\t<div class="io-bomb-content bg-white text-sm">                <div class="io-bomb-header fx-yellow modal-header-bg text-center p-3">                    <i class="io io-tishi icon-2x"></i>                    <div class="text-md mt-1">'+t+'</div>                </div>\t\t\t\t<div class="m-4">'+e+'</div>                <div class="text-center mb-4">                    <button class="btn vc-red btn-shadow mx-2 px-5" onclick="_onclick(true);">'+localize.okBtn+'</button>                    <button class="btn vc-l-yellow btn-outline mx-2 px-5" onclick="_onclick(false);">'+localize.cancelBtn+"</button>                </div>\t\t\t</div>\t\t</div>\t</div>\t",o=$(i);$("body").addClass("modal-open").append(o),hasScrollbar()&&$("body").css("padding-right",getScrollbarWidth()),_onclick=function(t){n(),$.isFunction(a)&&a(t,$(this))};var n=function(){$("body").removeClass("modal-open"),hasScrollbar()&&$("body").css("padding-right",""),$(o).removeClass("io-bomb-open").addClass("io-bomb-close"),setTimeout(function(){$(o).removeClass("io-bomb-close"),setTimeout(function(){o.remove()},200)},600)};return o}function debounce(t,e,a){var i;return function(){var o=this,n=arguments,s=function(){i=null,a||t.apply(o,n)},r=a&&!i;clearTimeout(i),i=setTimeout(s,e),r&&t.apply(o,n)}}function ioModal(t){var e=t.data("modal_size")||"modal-medium",a=t.data("modal_type")||"modal-suspend",i="refresh_modal"+a,o='<div class="modal fade" id="'+i+'" tabindex="-1" role="dialog" aria-hidden="false">    <div class="modal-dialog '+e+' modal-dialog-centered" role="document">    <div class="modal-content '+a+'">    </div>    </div>    </div>    </div>',n=$("#"+i);return n[0]||($("body").append(o),n=$("#"+i)),n.find(".modal-content").html('<div class="io-modal-content"></div><div class="loading-anim io-radius bg-blur-20"><div class="d-flex align-items-center justify-content-center h-100"><i class="io io-loading icon-spin icon-2x"></i></div></div>').css({height:"220px",overflow:"hidden"}),n.modal("show"),n}function GetQueryVal(t){var e=window.parent.location.search;if(-1!=e.indexOf("?")){var a=e.substr(1);a.indexOf(!0)&&(a=a.substr(0)),strs=a.split("&");for(var i=0;i<strs.length;i++)if(-1!=strs[i].indexOf(t))return strs[i].split("=")[1]}return null}function setChartTheme(){chartOption&&"object"===(void 0===chartOption?"undefined":_typeof(chartOption))&&(ioChart.dispose(),ioChart=echarts.init(domChart,chartTheme),ioChart.setOption(chartOption))}function refreshChart(){chartOption&&"object"===(void 0===chartOption?"undefined":_typeof(chartOption))&&ioChart.resize()}function hasScrollbar(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)?"11":"22"}function getScrollbarWidth(){var t,e,a=document.createElement("DIV");return a.style.cssText="position:absolute; top:-1000px; width:100px; height:100px; overflow:hidden;",t=document.body.appendChild(a).clientWidth,a.style.overflowY="scroll",e=a.clientWidth,document.body.removeChild(a),t-e}function setCookie(t,e,a){var i="";if(""!=a){var o=new Date;o.setTime(o.getTime()+24*a*60*60*1e3),i="expires="+o.toGMTString()}document.cookie=t+"="+e+"; "+i+"; path=/"}function getCookie(t){for(var e=t+"=",a=document.cookie.split(";"),i=0;i<a.length;i++){var o=a[i].trim();if(0==o.indexOf(e))return o.substring(e.length,o.length)}return""}function is_function(functionName){try{return"function"==typeof eval(functionName)}catch(t){}return!1}function captcha_ajax(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(t.attr("disabled"))return!1;if(!e){var i=t.closest("form");e=i.serializeObject()}var o=t.data("action");if(o&&(e.action=o),e.captcha_type&&window.captcha&&!window.captcha.ticket)return CaptchaOpen(t,e.captcha_type),!1;window.captcha&&(e.captcha=JSON.parse(JSON.stringify(window.captcha)),e.captcha._this&&delete e.captcha._this,window.captcha={});var n={};n.status=0,n.msg=localize.loading,showAlert(n);var s=t.html();t.attr("disabled",!0).html('<i class="io io-loading icon-spin mr-2"></i>'+localize.wait),$.ajax({url:theme.ajaxurl,type:"POST",dataType:"json",data:e}).done(function(i){i.msg?(n.status=i.status,n.msg=i.msg,showAlert(n)):removeAlert(),t.attr("disabled",!1).html(s),$.isFunction(a)&&a(i,t,e),i.goto?(window.location.href=i.goto,window.location.reload):i.reload&&window.location.reload()}).fail(function(e){e=e.responseJSON,e&&e.msg?(n.status=e.status,n.msg=e.msg,showAlert(n)):(n.status=4,n.msg=localize.networkerror,showAlert(n)),t.attr("disabled",!1).html(s)})}function copyText(t,e,a,i){var o=t.toString(),n=document.querySelector("#copy-input");n||(n=document.createElement("input"),n.id="copy-input",n.readOnly="readOnly",n.style.position="fixed",n.style.left="-2000px",n.style.zIndex="-1000",i.parentNode.appendChild(n)),n.value=o,function(t,e,a){if(t.createTextRange){var i=t.createTextRange();i.collapse(!0),i.moveStart("character",e),i.moveEnd("character",a-e),i.select()}else t.setSelectionRange(e,a),t.select()}(n,0,o.length),document.execCommand("copy")?$.isFunction(e)&&e():$.isFunction(a)&&a(),n.blur()}function get_version(){return void 0!==theme.version?" V"+theme.version:""}var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){function e(){if(t(".sites-seo-load")[0]){var e=t(".sites-seo-load");t.get(theme.ajaxurl+"/?action=get_sites_seo&url="+e.data("url"),null,function(t,a){if("0"==t.errcode){var i="",o="",n=e.data("go_to");t.data.result.forEach(function(t){switch(t.type){case"BaiduPCWeight":o="百度PC";break;case"BaiduMobileWeight":o="百度移动";break;case"HaoSouWeight":o="360";break;case"SMWeight":o="神马";break;case"TouTiaoWeight":o="头条"}i+='<a class="sites-weight '+t.type+'" href="'+n+'" title="'+o+'" target="_blank" rel="external nofollow"><span>'+t.weight+"</span></a>"}),e.html(i)}})}}function a(e){if(e[0]){var a=t('<i class="io io-arrow-right-o sidebar-more text-sm"></i>');e.find(".menu-item-has-children>a").after(a)}}function i(){r(),l(),refreshChart()}function o(e){var a=t("html"),i=t(".switch-dark-mode"),o=t(".mode-ico"),n=t("#post_content_ifr").contents().find("body");a.hasClass("io-black-mode")?(n.addClass("io-black-mode"),e&&setCookie("io_night_mode",0,30),i.attr("data-original-title")?i.attr("data-original-title",localize.lightMode):i.attr("title",localize.lightMode),o.removeClass("io-night").addClass("io-light"),chartTheme="dark",setChartTheme()):(n.removeClass("io-black-mode"),e&&setCookie("io_night_mode",1,30),i.attr("data-original-title")?i.attr("data-original-title",localize.nightMode):i.attr("title",localize.nightMode),o.removeClass("io-light").addClass("io-night"),chartTheme="",setChartTheme())}function n(){try{var t=getCookie("io_night_mode");"0"===t||!t&&window.matchMedia("(prefers-color-scheme: dark)").matches?(document.documentElement.classList.add("io-black-mode"),document.documentElement.classList.remove(theme.defaultclass)):(document.documentElement.classList.remove("io-black-mode"),document.documentElement.classList.add(theme.defaultclass)),o(!1)}catch(t){}}function s(){if(t(".slider_menu[sliderTab]").each(function(){var e=t(this);if(!e.hasClass("into")){var a=e.children("ul");a.prepend('<li class="anchor" style="position:absolute;width:0;height:28px"></li>');var i=a.find(".active").parent();0<i.length&&a.children(".anchor").css({left:i.position().left+i.scrollLeft()+"px",width:i.outerWidth()+"px",height:i.height()+"px",opacity:"1"}),e.addClass("into")}}),is_function("Swiper")){new Swiper(".swiper-post-module",{autoplay:{disableOnInteraction:!1},lazy:{loadPrevNext:!0},slidesPerView:1,loop:!0,pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),new Swiper(".swiper-widgets",{autoplay:{disableOnInteraction:!1,delay:5e3},effect:"fade",thumbs:{swiper:{el:".swiper-widgets-thumbs",slidesPerView:"auto",freeMode:!0,centerInsufficientSlides:!0},autoScrollOffset:1},on:{init:function(t){this.slides.eq(0).addClass("anim-slide")},transitionStart:function(){for(var t=0;t<this.slides.length;t++){this.slides.eq(t).removeClass("anim-slide")}},transitionEnd:function(){this.slides.eq(this.activeIndex).addClass("anim-slide")}}}),new Swiper(".swiper-term-content",{nested:!0,slidesPerView:"auto",freeMode:!0,mousewheel:!0,watchSlidesProgress:!0,resistanceRatio:!1})}}function r(){var e=t(".main-footer");if(e.attr("style",""),e.hasClass("footer-stick")){var a=jQuery(window).height(),i=e.outerHeight(!0),o=e.position().top+i;a>o-parseInt(e.css("marginTop"),10)&&e.css({marginTop:a-o})}}function l(){"1"==theme.minNav&&!T&&767.98<t(window).width()||!T&&767.98<t(window).width()&&t(window).width()<1024?(t("#mini-button").prop("checked",!1),c(),T=!0,S&&(t("body").addClass("mini-sidebar"),S=!1)):"1"!=theme.minNav&&(T&&t(window).width()>=1024||S&&!T&&t(window).width()>=1024)?(t("#mini-button").prop("checked",!0),c(),T=!1,S&&(S=!1)):t(window).width()<767.98&&t("body").hasClass("mini-sidebar")&&(t("body").removeClass("mini-sidebar"),S=!0,T=!1)}function d(e){t("body").hasClass("mini-sidebar")||(e.parent("li").siblings("li").removeClass("sidebar-show").children("ul").slideUp(200),"none"==e.nextAll("ul").css("display")?(e.nextAll("ul").slideDown(200),e.parent("li").addClass("sidebar-show").siblings("li").removeClass("sidebar-show")):(e.nextAll("ul").slideUp(200),e.parent("li").removeClass("sidebar-show")))}function c(){t('.header-mini-btn input[type="checkbox"]').prop("checked")?(t("body").removeClass("mini-sidebar"),t(".sidebar-menu ul ul").css("display","none")):(t(".sidebar-item.sidebar-show").removeClass("sidebar-show"),t(".sidebar-menu ul").removeAttr("style"),t("body").addClass("mini-sidebar"))}function u(e,a,i){e.addClass("disabled"),t.ajax({url:theme.ajaxurl,type:"GET",dataType:"html",data:i,cache:!0}).done(function(i){i.trim()&&(a.html(i),isPC()&&t('.ajax-url [data-toggle="tooltip"]').tooltip({trigger:"hover"}),e.addClass("load")),e.removeClass("disabled")}).fail(function(){e.removeClass("disabled")})}function m(t){for(var e=f("myLinks"),a=0;a<e.length;a++)if(e[a].url==t.url)return void showAlert(JSON.parse('{"status":4,"msg":"'+localize.urlExist+'"}'));e.unshift(t),h(t,!1,!1),p(e,"myLinks")}function h(e,a,i){a?t(".customize_nothing_click").remove():t(".customize_nothing").remove();var o,n=e.url.match(/^(?:https?:\/\/)?((?:[-A-Za-z0-9]+\.)+[A-Za-z]{2,6})/);!n||n.length<2?o=e.url:(o=n[0],"1"==theme.urlformat&&(o=n[1]));var s=t('<div class="url-card  col-6 '+theme.classColumns+' col-xxl-10a"><div class="url-body mini"><a href="'+e.url+'" target="_blank" class="card new-site mb-3 site-'+e.id+'" data-id="'+e.id+'" data-url="'+e.url+'" data-toggle="tooltip" data-placement="bottom" title="'+e.name+'" rel="external nofollow"><div class="card-body" style="padding:0.4rem 0.5rem;"><div class="url-content d-flex align-items-center"><div class="url-img rounded-circle mr-2 d-flex align-items-center justify-content-center"><img src="'+theme.icourl+o+theme.icopng+'"></div><div class="url-info flex-fill"><div class="text-sm overflowClip_1"><strong>'+e.name+'</strong></div></div></div></div></a></div><a href="javascript:;" class="text-center remove-site" data-id="'+e.id+'" style="display: none"><i class="io io-close"></i></a></div>');a?(i?t(".my-click-list").prepend(s):t(".my-click-list").append(s),s.children(".remove-site").on("click",g)):(t("#add-site").before(s),s.children(".remove-site").on("click",b)),j&&s.children(".remove-site").show(),isPC()&&t('.new-site[data-toggle="tooltip"]').tooltip({trigger:"hover"})}function f(t){var e=window.localStorage.getItem(t);return e?e=JSON.parse(e):[]}function p(t,e){window.localStorage.setItem(e,JSON.stringify(t))}function v(e){var a=f(e?"livelists":"myLinks");if(a.length&&!e&&!t("#add-site")[0])return void t(".customize_nothing.custom-site").children(".nothing").html('<a href="javascript:;" class="add-new-custom-site" data-action="add_custom_urls" data-term_name="我的导航" data-urls="'+Base64.encode(JSON.stringify(a))+'" >您已登录，检测到您的设备上有数据，点击<strong style="color:#db2323">同步到服务器</strong>。</a>');if(a.length)for(var i=0;i<a.length;i++)h(a[i],e,!1)}function b(){for(var e=t(this).data("id"),a=f("myLinks"),i=0;i<a.length;i++)if(parseInt(a[i].id)===parseInt(e)){console.log(a[i].id,e),a.splice(i,1);break}p(a,"myLinks"),t(this).parent().remove()}function g(){for(var e=t(this).data("id"),a=f("livelists"),i=0;i<a.length;i++)if(parseInt(a[i].id)===parseInt(e)){console.log(a[i].id,e),a.splice(i,1);break}p(a,"livelists"),t(this).parent().remove()}function w(){t(".customize-sites").hasClass("edit")?(isPC()&&t('.customize-sites .new-site[data-toggle="tooltip"]').tooltip("disable"),t(".customize-sites .site-list").sortable({items:".sortable",containment:".main-content",update:function(e,a){t(".customize-sites .site-list").sortable("disable");var i=t(this).data("term_id"),o=t(this).sortable("serialize"),n={action:"update_custom_url_order",term_id:i,order:o};t.ajax({url:theme.ajaxurl,type:"POST",data:n,cache:!1,dataType:"json",success:function(e){1!=e.status&&showAlert(e),t(".customize-sites .site-list").sortable("enable")},error:function(e){t(".customize-sites .site-list").sortable("enable"),showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))}})}})):(isPC()&&t('.customize-sites .new-site[data-toggle="tooltip"]').tooltip("enable"),t(".customize-sites .site-list").sortable("destroy"))}function y(){var e=t(".s-type-list.big label").data("page"),a=window.localStorage.getItem("searchlist_"+e),i=window.localStorage.getItem("searchlistmenu_"+e);if(a){var o=t(".hide-type-list input#"+a);o.prop("checked",!0),window.setTimeout(function(){o.closest(".tab-auto-scrollbar").tabToCenter(o.parent("li"))},100),t(".hide-type-list input#m_"+a).prop("checked",!0)}i&&(t(".s-type-list.big label").removeClass("active"),t(".s-type-list [data-id="+i+"]").addClass("active")),toTarget(t(".s-type-list.big"),!1,!1),t(".big.tab-auto-scrollbar").tabToCenter(t(".big.tab-auto-scrollbar label.active")),t(".hide-type-list .s-current").removeClass("s-current"),t('.hide-type-list input:radio[name="type"]:checked').parents(".search-group").addClass("s-current"),t('.hide-type-list input:radio[name="type2"]:checked').parents(".search-group").addClass("s-current"),t(".super-search-fm").attr("action",t(".hide-type-list input:radio:checked").val()),t(".search-key").attr("placeholder",t(".hide-type-list input:radio:checked").data("placeholder")),"type-zhannei"!=a&&"type-big-zhannei"!=a||t(".search-key").attr("zhannei","true")}function k(e,a){t.ajax({type:"GET",url:"//suggestqueries.google.com/complete/search?client=firefox&callback=iowenHot",async:!0,data:{q:e},dataType:"jsonp",jsonp:"callback",success:function(e){var i=a.children(".search-smart-tips");if(i.children("ul").text(""),$=e[1].length){for(var o=0;o<$;o++)i.children("ul").append("<li>"+e[1][o]+"</li>"),i.find("li").eq(o).click(function(){var e=t(this).html();a.find(".smart-tips.search-key").val(e),a.children(".super-search-fm").submit(),i.slideUp(200)});i.slideDown(200)}else i.slideUp(200)},error:function(t){$=0}})}function C(e,a){t.ajax({type:"GET",url:"//suggestion.baidu.com/su?p=3&cb=iowenHot",async:!0,data:{wd:e},dataType:"jsonp",jsonp:"cb",success:function(e){var i=a.children(".search-smart-tips");if(i.children("ul").text(""),$=e.s.length){for(var o=0;o<$;o++)i.children("ul").append("<li>"+e.s[o]+"</li>"),i.find("li").eq(o).click(function(){var e=t(this).html();a.find(".smart-tips.search-key").val(e),a.children(".super-search-fm").submit(),i.slideUp(200)});i.slideDown(200)}else i.slideUp(200)},error:function(t){$=0}})}function x(e,a){t("#modal-new-url-ico").show(),t.post("//apiv2.iotheme.cn/webinfo/get.php",{url:e,key:theme.apikey},function(e,i){0==e.code?(t("#modal-new-url-ico").hide(),t("#modal-new-url-summary").addClass("is-invalid")):(t("#modal-new-url-ico").hide(),""==e.site_title&&""==e.site_description?t("#modal-new-url-summary").addClass("is-invalid"):(a.find('[name="url_name"]').val(e.site_title),a.find('[name="url_summary"]').val(e.site_description)))}).fail(function(){t("#modal-new-url-ico").hide(),t(".refre_msg").html('<i class="io io-tishi"></i>'+localize.timeout).show(200).delay(4e3).hide(200)})}t(document).ready(function(){if("1"!==theme.minNav&&l(),o(!1),y(),r(),isPC()?t('[data-toggle="tooltip"]').tooltip({trigger:"hover"}):t('.qr-img[data-toggle="tooltip"]').tooltip({trigger:"hover"}),s(),t(".sidebar-tools")[0]&&t(".sidebar-tools").theiaStickySidebar({additionalMarginTop:0,additionalMarginBottom:20}),"1"===theme.isCustomize&&(v(!1),v(!0)),t(window).scrollTop()>=50&&(t("#go-to-up").fadeIn(200),t(".big-header-banner").addClass("header-bg")),t("#loading")[0]){var i=t("#loading");i.addClass("close"),setTimeout(function(){i.remove()},600)}"1"===theme.isHome&&setTimeout(function(){t('a.smooth[href="'+window.location.hash+'"]')[0]?t('a.smooth[href="'+window.location.hash+'"]').click():""!=window.location.hash&&t("html, body").animate({scrollTop:t(window.location.hash).offset().top-90},{duration:500,easing:"swing"})},300),a(t(".sidebar-item.top-menu")),t(".io-ajax-auto").each(function(){var e=t(this),a=e.attr("href");return a||(a=e.data("href")),t.get(a,null,function(t,a){e.html(t)}),!1}),GetQueryVal("iopay")&&(window.load_io_pay||(window.load_io_pay=!0,t.getScript(theme.uri+"/iopay/assets/js/pay.js",function(){weixin_auto_send()}))),e()}),t(document).on("click","a.smooth",function(e){var a=t(this);if(e.preventDefault(),t("#sidebar").hasClass("show")&&!a.hasClass("change-href")&&t("#sidebar").modal("toggle"),"#"==a.attr("href").substr(0,1)&&t(a.attr("href"))[0]&&t("html, body").animate({scrollTop:t(a.attr("href")).offset().top-90},{duration:500,easing:"swing"}),a.hasClass("go-search-btn")&&t("#search-text").focus(),!a.hasClass("change-href")){var i=t("a"+a.attr("href"));i[0]&&(i.click(),toTarget(i.closest("ul"),!0,!0))}}),t(document).on("click","a.smooth-n",function(e){e.preventDefault(),t("html, body").animate({scrollTop:t(t(this).attr("href")).offset().top-90},{duration:500,easing:"swing"})}),t(".panel-body.single img:not(.unfancybox)").each(function(e){var a=t(this);a.hasClass("wp-smiley")||this.parentNode.href||("1"==theme.lazyload?a.wrap("<a class='js' href='"+a.data("src")+"' data-fancybox='fancybox' data-caption='"+this.alt+"'></a>"):a.wrap("<a class='js' href='"+this.src+"' data-fancybox='fancybox' data-caption='"+this.alt+"'></a>"))});var _=0;t(window).resize(function(){clearTimeout(_),_=setTimeout(i,200)}),t(".count-a").each(function(){var e=t(this);e.prop("Counter",0).animate({Counter:e.text()},{duration:1e3,easing:"swing",step:function(t){e.text(Math.ceil(t))}})}),t(document).on("click","a[target!='_blank']:not(.qrcode-signin)",function(){var e=t(this);if(isPC()&&"1"==theme.loading&&e.attr("href")&&0!=e.attr("href").indexOf("#")&&0!=e.attr("href").indexOf("java")&&!e.data("fancybox")&&!e.data("commentid")&&!e.hasClass("nofx")){var a=t('<div id="load-loading"></div>');t("body").prepend(a),a.animate({opacity:"1"},200,"swing").delay(2e3).hide(300,function(){a.remove()})}}),t(document).on("click",".btn-like",function(){var e=t(this);if("post_like"==e.data("action"))if(e.hasClass("liked"))showAlert(JSON.parse('{"status":1,"msg":"'+localize.liked+'"}'));else{var a=e.children(".flex-column");e.addClass("liked"),t.ajax({type:"POST",url:theme.ajaxurl,data:{action:e.data("action"),post_id:e.data("id"),ticket:e.data("ticket")},success:function(e){var i=t('<i class="io io-zan" style="color: #f12345;transform: scale(1) translateY(0);position: absolute;transition: .6s;opacity: 1;"></i>');a.prepend(i),showAlert(JSON.parse('{"status":1,"msg":"'+localize.like+'"}')),t(".like-count").html(e),i.addClass("home-like-hide")},error:function(){showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))}})}else{if(e.hasClass("disabled"))return!1;var i=0,o=e.data("id");e.hasClass("liked")&&(i=1),e.addClass("disabled"),t.ajax({type:"POST",url:theme.ajaxurl,data:{action:e.data("action"),post_id:e.data("id"),post_type:e.data("post_type"),delete:i,ticket:e.data("ticket")},success:function(a){if(e.removeClass("disabled"),1==a.status)return t(".star-count-"+o).html(a.count),1==i?(e.removeClass("liked"),e.find(".star-ico").removeClass("icon-collection").addClass("icon-collection-line")):(e.addClass("liked"),e.find(".star-ico").removeClass("icon-collection-line").addClass("icon-collection")),ioPopupTips(a.status,a.msg),!1;ioPopupTips(a.status,a.msg)},error:function(){e.removeClass("disabled"),ioPopupTips(4,localize.networkerror)}})}return!1}),t(document).on("click",".home-like",function(){var e=t(this);if(e.hasClass("liked"))showAlert(JSON.parse('{"status":3,"msg":"'+localize.liked+'"}'));else{var a=e.data("id");e.addClass("liked"),t.ajax({type:"POST",url:theme.ajaxurl,data:{action:"post_like",post_id:a},success:function(i){var o=t('<i class="io io-zan" style="color: #f12345;transform: scale(1) translateY(0);position: absolute;transition: .6s;opacity: 1;"></i>');e.prepend(o),showAlert(JSON.parse('{"status":1,"msg":"'+localize.like+'"}')),t(".home-like-"+a).html(i),o.addClass("home-like-hide")},error:function(){showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))}})}return!1}),t(document).on("click","a.is-views[data-id]",function(){t.ajax({type:"GET",url:theme.ajaxurl,data:{action:"io_postviews",postviews_id:t(this).data("id")},cache:!1})}),t(document).on("click",".switch-dark-mode",function(e){e.preventDefault(),t("html").toggleClass("io-black-mode "+theme.defaultclass),o(!0),t("#"+t(".switch-dark-mode").attr("aria-describedby")).remove()}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",n),t(window).scroll(function(){t(this).scrollTop()>=50?(t("#go-to-up").fadeIn(200),t(".big-header-banner").addClass("header-bg")):(t("#go-to-up").fadeOut(200),t(".big-header-banner").removeClass("header-bg"))}),t(".go-up").click(function(){return t("body,html").animate({scrollTop:0},500),!1}),t(".slider_menu li:not(.anchor)").hover(function(){t(this).addClass("hover"),toTarget(t(this).parent(),!0,!0)},function(){t(this).removeClass("hover");var e=t(this).parent("ul");window.setTimeout(function(){toTarget(e,!0,!0)},50)}),t.fn.tabToCenter=function(e){var a=t(this);if(a.length){var i=e;if(i.length){var o=a.innerWidth(),n=i.innerWidth(),s=a.scrollLeft(),r=i.position().left,l=~~(s+r-o/2+n/2);l=l>0?l:0,a.animate({scrollLeft:l},300)}}},t(document).on("click",".tab-auto-scrollbar a",function(e){t(this).closest(".tab-auto-scrollbar").tabToCenter(t(this).parent("li"))}),t("#sidebar-switch").on("click",function(){t("body").removeClass("mini-sidebar")});var T=!1,S=!1;t(document).on("click",".sidebar-menu-inner .sidebar-more,.sidebar-menu-inner a",function(){d(t(this))}),t("#mini-button").on("click",function(){t(".sidebar-nav").hasClass()||t("body").addClass("animate-nav"),c()}),t(document).on("mouseenter",".mini-sidebar .sidebar-nav .sidebar-menu ul:first>li,.mini-sidebar .sidebar-nav .flex-bottom ul:first>li",function(){var e=0,a=t(this);0!=a.parents(".flex-bottom").length&&(e=-4);var i=t(".sidebar-popup.second");0==i.length&&(t("body").append("<div class='second sidebar-popup sidebar-menu-inner text-sm'><div></div></div>"),i=t(".sidebar-popup.second")),t(".sidebar-popup.second>div").html(a.html()),i.show();var o=a.offset().top-t(window).scrollTop()+e,n=t(window).height()-t(".sidebar-popup.second>div").height();n-o<=0&&(o=n>=0?n-8:0),i.stop().animate({top:o},50),a.one("mouseleave",function(){t("body").hasClass("mini-sidebar")&&t(".sidebar-scroll").innerHeight()>t(".sidebar-menu").innerHeight()?t(".mini-sidebar .sidebar-nav .sidebar-menu").one("mouseleave",function(){i.hide()}):i.hide()})}),t(document).on("mouseenter",".mini-sidebar .sidebar-nav .slimScrollBar,.second.sidebar-popup",function(){var e=t(".sidebar-popup.second");e.show(),t(this).one("mouseleave",function(){e.hide()})}),t(document).on("click",".ajax-cm-home .ajax-cm",function(e){e.preventDefault();var a=t(this),i=a.data("id"),o=t(a.attr("href")).children(".site-list");0==o.children(".url-card").length&&(a.addClass("disabled"),t.ajax({url:theme.ajaxurl,type:"POST",dataType:"html",data:{action:a.data("action"),term_id:i},cache:!0}).done(function(e){if(e.trim()){var i=t(e);o.html(i),isPC()&&i.find('[data-toggle="tooltip"]').tooltip({trigger:"hover"})}a.removeClass("disabled")}).fail(function(){a.removeClass("disabled")}))}),t(document).on("click",".ajax-home-hot-list:not(.load)",function(e){e.preventDefault();var a=t(this),i=t(a.attr("href")).children(".ajax-list-body");a.addClass("disabled"),t.ajax({url:theme.ajaxurl,type:"POST",dataType:"html",data:t.extend({action:a.data("action")},a.data("datas")),cache:!0}).done(function(e){if(e.trim()){var o=t(e);i.html(o),isPC()&&o.find('[data-toggle="tooltip"]').tooltip({trigger:"hover"}),a.addClass("load")}a.removeClass("disabled")}).fail(function(){a.removeClass("disabled")})}),t(document).on("click","a.tab-widget-link:not(.load)",function(e){e.preventDefault();var a=t(this);a.addClass("disabled"),t.ajax({url:theme.ajaxurl,type:"POST",dataType:"html",data:t.extend({action:a.data("action")},a.data("datas"))}).done(function(e){t(a.attr("href")+" .widget-item").html(e),a.addClass("load"),a.removeClass("disabled")}).fail(function(){a.removeClass("disabled")})}),t(document).on("click","a.tab-noajax",function(e){var a=t(this),i=a.data("link");i?a.parents(".d-flex.flex-fill.flex-tab").children(".btn-move.tab-move").show().attr("href",i):a.parents(".d-flex.flex-fill.flex-tab").children(".btn-move.tab-move").hide()}),t(document).on("click",".login-btn-action",function(){window.location.href=theme.loginurl,window.location.reload}),t(document).on("click",".ajax-list-home a:not(.load)",function(e){e.preventDefault(),u(t(this),t(t(this).attr("href")).children(".ajax-list-body"),t(this).parents("li").data())}),t(".add-link-form").on("submit",function(){var e=t(".site-add-name").val(),a=t(".site-add-url").val();m({id:+new Date,name:e,url:a}),this.reset(),this.querySelector("input").focus(),t(this).find(".btn-close-fm").click()});var j=!1;t(".customize-menu .btn-edit").click(function(){j?(t(".url-card .remove-site,#add-site").hide(),t(".url-card .remove-site,.add-custom-site").hide(),t(".url-card .remove-cm-site").hide(),t(".customize-sites").removeClass("edit"),w(),t(".customize-menu .btn-edit").html(localize.editBtn)):(t(".url-card .remove-site,#add-site").show(),t(".url-card .remove-site,.add-custom-site").show(),t(".url-card .remove-cm-site").show(),t(".customize-sites").addClass("edit"),w(),t(".customize-menu .btn-edit").html(localize.okBtn)),j=!j}),t(document).on("click",".add-new-custom-site",function(e){var a=t(this);t.ajax({url:theme.ajaxurl,type:"POST",dataType:"json",data:a.data()}).done(function(t){showAlert(t)}).fail(function(){showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))})}),t(".add-custom-site-form").on("submit",function(){var e=t(this),a=this,i=e.find("input[name=url]").val(),o=e.find("input[name=url_name]").val(),n=e.find("input:radio:checked").val();if(""==e.find("input[name=term_name]").val()&&void 0==n)return showAlert(JSON.parse('{"status":3,"msg":"'+localize.selectCategory+'"}')),!1;t.ajax({url:theme.ajaxurl,type:"POST",dataType:"json",data:e.serialize()+"&action=add_custom_url"}).done(function(s){if(1!=s.status)return void showAlert(s);var r,l=i.match(/^(?:https?:\/\/)?((?:[-A-Za-z0-9]+\.)+[A-Za-z]{2,6})/);!l||l.length<2?r=i:(r=l[0],"1"==theme.urlformat&&(r=l[1]));var d=s.id,c=t('<div id="url-'+d+'" class="url-card sortable col-6 '+theme.classColumns+' col-xxl-10a"><div class="url-body mini"><a href="'+i+'" target="_blank" class="card new-site mb-3 site-'+d+'" data-id="'+d+'" data-url="'+i+'" data-toggle="tooltip" data-placement="bottom" title="'+o+'" rel="external nofollow"><div class="card-body" style="padding:0.4rem 0.5rem;"><div class="url-content d-flex align-items-center"><div class="url-img rounded-circle mr-2 d-flex align-items-center justify-content-center"><img src="'+theme.icourl+r+theme.icopng+'"></div><div class="url-info flex-fill"><div class="text-sm overflowClip_1"><strong>'+o+'</strong></div></div></div></div></a></div><a href="javascript:;" class="text-center remove-cm-site" data-action="delete_custom_url" data-id="'+d+'"><i class="io io-close"></i></a></div>');t(".add-custom-site[data-term_id="+n+"]").before(c),a.reset(),a.querySelector("input").focus(),e.find(".btn-close-fm").click(),showAlert(JSON.parse('{"status":1,"msg":"'+localize.addSuccess+'"}'))}).fail(function(){showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))})}),t(document).on("click",".url-card .remove-cm-site",function(e){var a=t(this);a.addClass("disabled"),t.ajax({url:theme.ajaxurl,type:"POST",dataType:"json",data:a.data()}).done(function(t){1==t.status&&a.parent().remove(),a.removeClass("disabled"),showAlert(t)}).fail(function(){a.removeClass("disabled"),showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))})}),t("input[name=term_name]").focus(function(){t("input[name=term_id]").prop("checked",!1)}),t(".form_custom_term_id").on("click",function(e){t("input[name=term_name]").val("")}),t(document).on("click",".url-card a.card",function(e){var a=t(this),i={id:a.data("id"),name:a.find("strong").html(),url:a.data("url")};if(""!==i.url){for(var o=f("livelists"),n=!0,s=0;s<o.length;s++)o[s].name===i.name&&(n=!1);if(n){var r=theme.customizemax;o.length>r-1&&(t(".my-click-list .site-"+o[r-1].id).parent().remove(),o.splice(r-1,1)),h(i,!0,!0),o.unshift(i),p(o,"livelists")}}}),t(document).on("click",".s-type-list label",function(e){var a=t(this);t(".s-type-list.big label").removeClass("active"),a.addClass("active"),window.localStorage.setItem("searchlistmenu_"+a.data("page"),a.data("id"));var i=a.parents(".s-search");i.find(".search-group").removeClass("s-current"),i.find("#"+a.attr("for")).parents(".search-group").addClass("s-current"),a.closest(".tab-auto-scrollbar").tabToCenter(a),toTarget(a.parents(".s-type-list"),!1,!1)}),t(".hide-type-list .search-group input").on("click",function(){var e=t(this),a=e.parents(".s-search");window.localStorage.setItem("searchlist_"+e.data("page"),e.attr("id").replace("m_","")),a.children(".super-search-fm").attr("action",e.val()),a.find(".search-key").attr("placeholder",e.data("placeholder")),"type-zhannei"==e.attr("id")||"type-big-zhannei"==e.attr("id")||"m_type-zhannei"==e.attr("id")?a.find(".search-key").attr("zhannei","true"):a.find(".search-key").attr("zhannei",""),e.closest(".tab-auto-scrollbar").tabToCenter(e.parent()),a.find(".search-key").select(),a.find(".search-key").focus()}),t(document).on("submit",".super-search-fm",function(){var e=t(this),a=encodeURIComponent(e.find(".search-key").val());if(""==a)return!1;var i=e.attr("action");return-1!=i.indexOf("%s%")?window.open(i.replace("%s%",a)):window.open(i+a),!1});var z,O=-1,$=0,A=!1;t(".smart-tips.search-key").off().on({compositionstart:function(){t(this).attr("data-status",!1)},compositionend:function(){t(this).attr("data-status",!0)},blur:function(){z="",t(".search-smart-tips").delay(150).slideUp(200)},focus:function(){var e=t(this);if(A=""!=e.attr("zhannei"),z=e.parents("#search"),"true"==e.attr("data-status")&&e.val()&&!A)switch(theme.hotWords){case"baidu":C(e.val(),z);break;case"google":k(e.val(),z)}},keyup:function(e){var a=t(this);if(A=""!=a.attr("zhannei"),z=a.parents("#search"),"true"==a.attr("data-status")&&a.val()){if(38==e.keyCode||40==e.keyCode||A)return;switch(theme.hotWords){case"baidu":C(a.val(),z);break;case"google":k(a.val(),z)}O=-1}else t(".search-smart-tips").slideUp(200)},keydown:function(e){var a=t(this);if(""==a.attr("zhannei")){if(z=a.parents("#search"),40===e.keyCode){O===$-1?O=0:O++,z.find(".search-smart-tips ul li").eq(O).addClass("current").siblings().removeClass("current");var i=z.find(".search-smart-tips ul li").eq(O).html();z.find(".smart-tips.search-key").val(i)}if(38===e.keyCode){e.preventDefault&&e.preventDefault(),e.returnValue&&(e.returnValue=!1),0===O||-1===O?O=$-1:O--,z.find(".search-smart-tips ul li").eq(O).addClass("current").siblings().removeClass("current");var i=z.find(".search-smart-tips ul li").eq(O).html();z.find(".smart-tips.search-key").val(i)}}}}),t(".nav-login-user.dropdown----").hover(function(){var e=t(this);e.hasClass("show")||e.children("a").click()},function(){}),t("#add-new-sites-modal").on("show.bs.modal",function(e){var a=t(e.relatedTarget),i=t(this);i.find('[name="term_id"]').val(a.data("terms_id")),i.find('[name="url"]').val(a.data("new_url")),i.find('[name="url_name"]').val(""),i.find('[name="url_summary"]').removeClass("is-invalid").val(""),a.data("new_url","");var o=i.find('[name="url"]').val();""!=o&&(x(o,i),P=o)});var P="";t("#modal-new-url").on("blur",function(){var e=t(this);""!=e.val()&&(isURL(e.val())?P!=e.val()&&(P=e.val(),x(e.val(),t(".add_new_sites_modal"))):showAlert(JSON.parse('{"status":4,"msg":"URL 无效！"}')))}),t("#modal-new-url-summary").on("blur",function(){var e=t(this);""!=e.val()&&e.removeClass("is-invalid")}),t(document).on("click","a.sidebar-rand-post",function(){load_rand_post(t(this).data())}),t(document).on("click","[data-clipboard-text]",function(e){var a=t(this),i=a.data("clipboard-text");a.hasClass("down_count")&&t.ajax({type:"POST",url:theme.ajaxurl,data:a.data(),success:function(e){t(".down-count-text").html(e)}}),i&&copyText(i,function(){alert(localize.extractionCode)},function(){},this)}),t(document).on("click",".password-show-btn",function(){var e=t(this),a=e.find(".io"),i=e.siblings("input");"0"==e.data("show")?(a.removeClass("icon-chakan-line"),a.addClass("icon-hide-line"),i.attr("type","text"),e.data("show",1)):(a.removeClass("icon-hide-line"),a.addClass("icon-chakan-line"),i.attr("type","password"),e.data("show",0))}),t('.count-tips input[type="text"]').off().on({compositionstart:function(){t(this).attr("data-status",!1)},compositionend:function(){t(this).attr("data-status",!0),change_input(this)},input:function(){change_input(this)}}),t(document).on("click","[data-for]",function(){var e,a=t(this),i=a.data("for"),o=a.parents("form"),n=a.data("value"),s=t(a.parents("[for-group]")[0]);s.length?s.find('[data-for="'+i+'"]').removeClass("active"):a.siblings().removeClass("active"),a.addClass("active"),e=a.html(),o.find("input[name='"+i+"']").val(n).trigger("change"),o.find("span[name='"+i+"']").html(e)}),t(".only-submit #submit").click(function(){var e=t(this),a=e.closest("form");return captcha_ajax(e,"",function(t){1==t.status&&(a[0].reset(),a.find(".image-captcha").click())}),!1}),t(document).on("click","#wp_login_form #submit",function(){return captcha_ajax(t(this),"",function(t){1==t.status&&(t.goto||window.location.reload())}),!1}),t(document).on("click",".open-login",function(){t(this);if(t("#user_agreement")[0]&&!t("#user_agreement").is(":checked"))return ioPopupTips(2,localize.userAgreement),!1}),t(document).on("click",".user-reset-password",function(){var e=t(this),a=e.attr("href"),i=e.closest(".modal-content");return i.css({height:i.outerHeight()}).animate({height:"220px"},200),i.find(".io-modal-content").html(""),i.find(".loading-anim").fadeIn(200),t.get(a,null,function(e,a){i.find(".io-modal-content").html(e).slideDown(200,function(){i.find(".loading-anim").fadeOut(200);var e=t(this).outerHeight();i.animate({height:e},200,"swing",function(){i.css({height:"",overflow:"",transition:""})})}),t("[captcha-type]")[0]&&CaptchaInit()}),!1}),t(".user-bind-modal").on("click",function(){var e=t(this),a=e.attr("href"),i=ioModal(e);return t.get(a,null,function(e,a){i.find(".io-modal-content").html(e).slideDown(200,function(){i.find(".loading-anim").fadeOut(200);var e=t(this).outerHeight(),a=i.find(".modal-content");a.animate({height:e},200,"swing",function(){a.css({height:"",overflow:"",transition:""})})}),t("[captcha-type]")[0]&&CaptchaInit()}),!1}),t(document).on("click",".user-bind-from .btn-submit",function(){var e=t(this),a=e.closest(".modal-content");return captcha_ajax(e,"",function(i){i.html&&(e.closest(".io-modal-content").html(i.html).slideDown(200,function(){var e=t(this).outerHeight();a.animate({height:e},200,"swing",function(){a.css({height:"",overflow:"",transition:""})})}),t("[captcha-type]")[0]&&CaptchaInit())}),!1}),t(document).on("click",".io-ajax-modal-get",function(){var e=t(this),a=e.attr("href");a||(a=e.data("href"));var i=ioModal(e);return t.get(a,null,function(e,a){i.find(".io-modal-content").html(e).slideDown(200,function(){i.find(".loading-anim").fadeOut(200);var e=t(this).outerHeight(),a=i.find(".modal-content");a.animate({height:e},200,"swing",function(){a.css({height:"",overflow:"",transition:""})})}),t(".modal .dependency-box").dependency(),t(".initiate-pay")[0]&&!window.load_io_pay&&(window.load_io_pay=!0,t.getScript(theme.uri+"/iopay/assets/js/pay.js"))}),!1}),t(document).on("click",".modal .io-ajax-price-get",function(){var e=t(this),a=e.attr("href");a||(a=e.data("href"));var i=e.parent();if(i.hasClass("disabled")||e.attr("disabled"))return!1;i.children().attr("disabled",!1),e.attr("disabled",!0),i.addClass("disabled");var o=e.closest("form"),n=o.find(e.data("target"));return n.append('<div class="d-flex align-items-center justify-content-center bg-o-muted position-absolute io-radius h-100 w-100"><i class="io io-loading icon-spin icon-2x"></i></div>'),t.get(a,null,function(e,a){var o=t(e);n.html(o),o[0].click(),i.removeClass("disabled")}),!1}),t(document).on("input",".get-ajax-custom-product-val",debounce(function(){var e=t(this);if(url=e.data("href"),e.hasClass("disabled"))return!1;e.addClass("disabled");var a=e.closest("form"),i=a.find(e.data("target"));return i.html('<i class="io io-loading icon-spin"></i>'),t.get(url,a.serializeObject(),function(t,a){t.msg?(alert.status=t.status?t.status:t.error?4:1,alert.msg=t.msg,showAlert(alert),i.html('<i class="io io-point"></i>')):i.html(t),e.removeClass("disabled")}),!1},800)),t(document).on("click",".io-ajax-modal",function(){var e=t(this),a=ioModal(e);return t.ajax({type:"POST",url:theme.ajaxurl,data:e.data(),success:function(e){a.find(".io-modal-content").html(e).slideDown(200,function(){a.find(".loading-anim").fadeOut(200);var e=t(this).outerHeight(),i=a.find(".modal-content");i.animate({height:e},200,"swing",function(){i.css({height:"",overflow:"",transition:""})})})},error:function(){a.modal("hide"),showAlert(JSON.parse('{"status":4,"msg":"'+localize.networkerror+'"}'))}}),!1}),t(document).on("input propertychange","#user_email",function(){t(this).val().length>4&&t(".verification").slideDown()}),t(document).on("click",".btn-token",function(){var e=t(this),a=e.closest("form");if(e.attr("disabled"))return!1;var i=a.find("#user_email");i[0]||(i=a.find(".mm_mail")),countdown=60;var o=a.find(".btn-token"),n=e.html(),s=function t(){countdown>0?(o.html(countdown+localize.reSend).attr("disabled",!0),countdown--,setTimeout(t,1e3)):(o.html(n).attr("disabled",!1),countdown=60)};!function(){captcha_ajax(e,"",function(t){i.attr("readonly","readonly"),1==t.status?s():i.removeAttr("readonly")})}()}),t.fn.dependency=function(){function e(t){switch(t){case!0:case"true":case 1:case"1":t=!0;break;case null:case!1:case"false":case 0:case"0":t=!1}return t}function a(a,i,o){if("=="==a)return e(i)==e(o);if("!="==a)return e(i)!=e(o);if(">="==a)return Number(o)>=Number(i);if("<="==a)return Number(o)<=Number(i);if(">"==a)return Number(o)>Number(i);if("<"==a)return Number(o)<Number(i);if("any"==a){if(t.isArray(o)){for(var n=o.length-1;n>=0;n--)if(-1!==t.inArray(o[n],i.split(",")))return!0}else if(-1!==t.inArray(o,i.split(",")))return!0}else if("not-any"==a)if(t.isArray(o)){for(var n=o.length-1;n>=0;n--)if(-1==t.inArray(o[n],i.split(",")))return!0}else if(-1==t.inArray(o,i.split(",")))return!0;return!1}return this.each(function(){var e=t(this),i=e.find("[data-controller]");if(i.length){i.each(function(){var i=t(this);if(!i.attr("is-on")){var o=i.attr("is-on",!0).data("controller").split("|"),n=i.data("condition").split("|"),s=i.data("value").toString().split("|");t.each(o,function(o,r){var l=s[o]||"",d=n[o]||n[0]||"==";e.on("change","[name='"+r+"']",function(e){var o=t(this),n=o.attr("type"),s="checkbox"==n?o.is(":checked"):o.val(),r=a(d,l,s);i.trigger("controller.change",r),r?i.show():i.hide()})})}})}})},t(".dependency-box").dependency()}(jQuery);var ioadindex=0,chartTheme="",domChart=document.getElementById("chart-container"),ioChart,chartOption;$.fn.serializeObject=function(){var t={},e=this.serializeArray();return $.each(e,function(){void 0!==t[this.name]?(t[this.name].push||(t[this.name]=[t[this.name]]),t[this.name].push(this.value||"")):t[this.name]=this.value||""}),t},console.log("\n %c OneNav"+get_version()+" 导航主题 By 一为 %c https://www.iotheme.cn/ \n","color: #ffffff; background: #f1404b; padding:5px 0;","background: #030307; padding:5px 0;");
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):function(){const r=e.Base64,o=t();o.noConflict=()=>(e.Base64=r,o),e.Meteor&&(Base64=o),e.Base64=o}()}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:this,(function(){"use strict";const e="3.6.0",t="function"==typeof atob,r="function"==typeof btoa,o="function"==typeof Buffer,n="function"==typeof TextDecoder?new TextDecoder:void 0,a="function"==typeof TextEncoder?new TextEncoder:void 0,f=[..."ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="],i=(e=>{let t={};return e.forEach(((e,r)=>t[e]=r)),t})(f),c=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,u=String.fromCharCode.bind(String),s="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):(e,t=(e=>e))=>new Uint8Array(Array.prototype.slice.call(e,0).map(t)),d=e=>e.replace(/[+\/]/g,(e=>"+"==e?"-":"_")).replace(/=+$/m,""),l=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),h=e=>{let t,r,o,n,a="";const i=e.length%3;for(let i=0;i<e.length;){if((r=e.charCodeAt(i++))>255||(o=e.charCodeAt(i++))>255||(n=e.charCodeAt(i++))>255)throw new TypeError("invalid character found");t=r<<16|o<<8|n,a+=f[t>>18&63]+f[t>>12&63]+f[t>>6&63]+f[63&t]}return i?a.slice(0,i-3)+"===".substring(i):a},p=r?e=>btoa(e):o?e=>Buffer.from(e,"binary").toString("base64"):h,y=o?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let r=0,o=e.length;r<o;r+=4096)t.push(u.apply(null,e.subarray(r,r+4096)));return p(t.join(""))},A=(e,t=!1)=>t?d(y(e)):y(e),b=e=>{if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?u(192|t>>>6)+u(128|63&t):u(224|t>>>12&15)+u(128|t>>>6&63)+u(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return u(240|t>>>18&7)+u(128|t>>>12&63)+u(128|t>>>6&63)+u(128|63&t)},g=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,B=e=>e.replace(g,b),x=o?e=>Buffer.from(e,"utf8").toString("base64"):a?e=>y(a.encode(e)):e=>p(B(e)),C=(e,t=!1)=>t?d(x(e)):x(e),m=e=>C(e,!0),U=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,F=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return u(55296+(t>>>10))+u(56320+(1023&t));case 3:return u((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return u((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},w=e=>e.replace(U,F),S=e=>{if(e=e.replace(/\s+/g,""),!c.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,r,o,n="";for(let a=0;a<e.length;)t=i[e.charAt(a++)]<<18|i[e.charAt(a++)]<<12|(r=i[e.charAt(a++)])<<6|(o=i[e.charAt(a++)]),n+=64===r?u(t>>16&255):64===o?u(t>>16&255,t>>8&255):u(t>>16&255,t>>8&255,255&t);return n},E=t?e=>atob(l(e)):o?e=>Buffer.from(e,"base64").toString("binary"):S,v=o?e=>s(Buffer.from(e,"base64")):e=>s(E(e),(e=>e.charCodeAt(0))),D=e=>v(z(e)),R=o?e=>Buffer.from(e,"base64").toString("utf8"):n?e=>n.decode(v(e)):e=>w(E(e)),z=e=>l(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),T=e=>R(z(e)),Z=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),j=function(){const e=(e,t)=>Object.defineProperty(String.prototype,e,Z(t));e("fromBase64",(function(){return T(this)})),e("toBase64",(function(e){return C(this,e)})),e("toBase64URI",(function(){return C(this,!0)})),e("toBase64URL",(function(){return C(this,!0)})),e("toUint8Array",(function(){return D(this)}))},I=function(){const e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,Z(t));e("toBase64",(function(e){return A(this,e)})),e("toBase64URI",(function(){return A(this,!0)})),e("toBase64URL",(function(){return A(this,!0)}))},O={version:e,VERSION:"3.6.0",atob:E,atobPolyfill:S,btoa:p,btoaPolyfill:h,fromBase64:T,toBase64:C,encode:C,encodeURI:m,encodeURL:m,utob:B,btou:w,decode:T,isValid:e=>{if("string"!=typeof e)return!1;const t=e.replace(/\s+/g,"").replace(/=+$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:A,toUint8Array:D,extendString:j,extendUint8Array:I,extendBuiltins:()=>{j(),I()},Base64:{}};return Object.keys(O).forEach((e=>O.Base64[e]=O[e])),O}));
function ChromBookmarkConverter(){this.bookmarks={folders:[]},this.stripUnneededTags=function(a){return a=a.replace(/<p>/gi,""),a=a.replace(/<P>/gi,""),a=a.replace(/<dt>/gi,""),a=a.replace(/<DT>/gi,"")},this.processChromeBookmarksContent=function(a){var c,b=this;a=this.stripUnneededTags(a),c=$.parseHTML(a),$.each(c,function(a,c){if("DL"==c.tagName){var d={type:"folder",title:"未命名",items:[]};b.bookmarks.folders.push(d),b.processDL(c,1,d)}})},this.processDL=function(a,b,c){var d=this,e=0,f={},g={type:"folder",title:"",add_date:"",last_modified:"",items:[]},h={},i=$(a),j=!1;$.each(i.children(),function(a,i){var k,l,m,n,o,p,q,r,s;e+=1,k=b+"."+e,1==j&&i.tagName.toLowerCase()!="DL".toLowerCase()&&(j=!1,console.log("h3",f),g.items.push(f)),i.tagName.toLowerCase()=="DL".toLowerCase()&&(g={type:"folder",title:f.title,add_date:f.add_date,last_modified:f.last_modified,items:[]},1==j&&(j=!1),d.bookmarks.folders.push(g),d.processDL(i,k,g)),i.tagName.toLowerCase()=="H3".toLowerCase()&&(l=$(i),m=l.text()?l.text():"未命名",n=l.attr("add_date"),o=l.attr("last_modified"),f={type:"header",title:m,add_date:n,last_modified:o},j=!0),"a"==i.tagName.toLowerCase()&&isURL($(i).attr("href"))&&""!=$(i).text()&&(p=$(i),q=p.text(),r=p.attr("href"),s=p.attr("add_date"),p.attr("icon"),h={type:"link",title:q,href:r,add_date:s},c.items.push(h))})}}
