version: '3.8'
services:
  claude-relay:
    image: claude-relay-service-custom
    container_name: claude-relay-service
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - REDIS_HOST=redis
      - ADMIN_USERNAME=${ADMIN_USERNAME:-}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
      - CLAUDE_API_URL=https://api.duckcode.top/api/claude/v1/messages
      - CLAUDE_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************.0M0sRXRpChHdwNsJmJHPKN1rXLlc0kL_oUhB49kAiSI
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: claude-relay-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data

volumes:
  redis_data:
