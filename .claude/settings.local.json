{"permissions": {"allow": ["mcp__zen__analyze", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "mcp__zen__codereview", "mcp__zen__testgen", "mcp__sequential-thinking__sequentialthinking", "Bash(grep:*)", "mcp__chrome-mcp-stdio__chrome_navigate", "mcp__chrome-mcp-stdio__chrome_screenshot", "mcp__chrome-mcp-stdio__chrome_get_interactive_elements", "mcp__chrome-mcp-stdio__chrome_get_web_content", "mcp__chrome-mcp-stdio__chrome_inject_script", "mcp__chrome-mcp-stdio__chrome_console", "mcp__puppeteer__puppeteer_evaluate"], "deny": []}}