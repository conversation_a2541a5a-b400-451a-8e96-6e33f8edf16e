# 数据库架构规格说明
*硬核指南 - WordPress 主题数据库设计*

## 📊 数据库架构概览

### 设计原则
- **数据完整性** - 使用外键约束确保数据一致性
- **性能优化** - 合理的索引设计支持高并发访问
- **可扩展性** - 支持未来功能扩展的表结构
- **安全性** - 敏感数据加密存储

---

## 🗄️ WordPress 核心表扩展

### 1. 文章表 (wp_posts) 扩展

#### sites 文章类型字段
```sql
-- 文章类型为 'sites' 的记录将使用以下字段：
-- post_title: 网站名称
-- post_content: 网站描述（支持HTML）
-- post_excerpt: 简短描述
-- post_status: 发布状态 (publish, draft, private)
-- post_type: 'sites'
-- post_parent: 父级网站ID（用于网站关联）
-- menu_order: 排序权重
```

### 2. 文章元数据表 (wp_postmeta) 字段规范

#### 网站元数据字段
```sql
-- 核心网站信息
site_url (text): 目标网站URL，经过esc_url验证
site_icon (text): 网站图标URL或上传的图标ID
site_icon_alt (text): 图标替代文本

-- 显示配置
display_mode (varchar(20)): 显示模式 [mini|default|max|featured]
card_style (varchar(20)): 卡片样式 [normal|ai-site|promotion]
tooltip_text (text): 悬停提示文字
external_link (text): 外部跳转链接

-- 质量与特性标记
quality_score (int): 质量评分 (1-10)
is_featured (boolean): 是否为推荐网站
is_ai_site (boolean): 是否为AI网站
has_mobile_app (boolean): 是否有移动应用
requires_registration (boolean): 是否需要注册

-- 访问控制
access_level (varchar(20)): 访问级别 [free|premium|vip]
age_restriction (int): 年龄限制 (0=无限制)
content_warning (text): 内容警告信息

-- SEO 相关
seo_keywords (text): SEO关键词
seo_description (text): SEO描述
canonical_url (text): 规范化URL

-- 统计数据缓存（定期更新）
visit_count_today (int): 今日访问量
visit_count_week (int): 本周访问量
visit_count_month (int): 本月访问量
visit_count_total (int): 总访问量
last_visit_time (datetime): 最后访问时间

-- 网站状态
site_status (varchar(20)): 网站状态 [active|inactive|broken|maintenance]
last_checked (datetime): 最后检查时间
response_time (int): 响应时间(毫秒)
```

---

## 📈 自定义数据表设计

### 1. 访问统计表 (wp_yinghe_site_visits)

```sql
CREATE TABLE {$wpdb->prefix}yinghe_site_visits (
    -- 主键和基础信息
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    site_id bigint(20) unsigned NOT NULL COMMENT '网站文章ID',
    
    -- 用户信息
    user_id bigint(20) unsigned NULL COMMENT 'WordPress用户ID（已登录用户）',
    user_ip varchar(45) NOT NULL COMMENT '访问者IP地址（支持IPv6）',
    user_agent text COMMENT '用户代理字符串',
    user_fingerprint varchar(64) COMMENT '用户指纹hash（用于去重）',
    
    -- 访问信息
    visit_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    visit_duration int(11) DEFAULT NULL COMMENT '访问持续时间（秒）',
    page_url text COMMENT '访问页面URL',
    referer_url text COMMENT '来源页面URL',
    
    -- 设备信息
    device_type enum('desktop','mobile','tablet','other') DEFAULT 'other',
    browser_name varchar(50) COMMENT '浏览器名称',
    browser_version varchar(20) COMMENT '浏览器版本',
    os_name varchar(50) COMMENT '操作系统名称',
    os_version varchar(20) COMMENT '操作系统版本',
    
    -- 地理位置（隐私保护）
    country_code char(2) COMMENT '国家代码',
    region varchar(100) COMMENT '地区/省份',
    city varchar(100) COMMENT '城市',
    
    -- 行为数据
    click_count int(11) DEFAULT 1 COMMENT '点击次数',
    bounce boolean DEFAULT FALSE COMMENT '是否跳出访问',
    converted boolean DEFAULT FALSE COMMENT '是否转化访问',
    
    -- 技术信息
    screen_resolution varchar(20) COMMENT '屏幕分辨率',
    viewport_size varchar(20) COMMENT '视口大小',
    color_depth int(3) COMMENT '颜色深度',
    timezone varchar(50) COMMENT '时区',
    
    -- 营销分析
    utm_source varchar(100) COMMENT 'UTM来源',
    utm_medium varchar(100) COMMENT 'UTM媒介',
    utm_campaign varchar(100) COMMENT 'UTM活动',
    utm_term varchar(100) COMMENT 'UTM关键词',
    utm_content varchar(100) COMMENT 'UTM内容',
    
    -- 时间戳
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    INDEX idx_site_id (site_id),
    INDEX idx_user_id (user_id),
    INDEX idx_visit_time (visit_time),
    INDEX idx_user_ip (user_ip),
    INDEX idx_device_type (device_type),
    INDEX idx_country_code (country_code),
    INDEX idx_utm_source (utm_source),
    
    -- 外键约束
    FOREIGN KEY (site_id) REFERENCES {$wpdb->prefix}posts(ID) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES {$wpdb->prefix}users(ID) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='网站访问统计记录表';
```

### 2. 搜索引擎配置表 (wp_yinghe_search_engines)

```sql
CREATE TABLE {$wpdb->prefix}yinghe_search_engines (
    -- 主键
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    
    -- 基础信息
    name varchar(100) NOT NULL COMMENT '搜索引擎名称',
    display_name varchar(100) NOT NULL COMMENT '显示名称',
    description text COMMENT '搜索引擎描述',
    
    -- 搜索配置
    search_url text NOT NULL COMMENT '搜索URL模板，使用%s%作为关键词占位符',
    search_method enum('GET','POST') DEFAULT 'GET' COMMENT '搜索方法',
    encoding varchar(20) DEFAULT 'UTF-8' COMMENT '字符编码',
    
    -- 界面配置
    placeholder text COMMENT '搜索框占位符文本',
    button_text varchar(50) DEFAULT '搜索' COMMENT '搜索按钮文本',
    icon_url text COMMENT '搜索引擎图标URL',
    
    -- 分类和状态
    category enum('yingshi','ziliao','yinyue','yuedu','yule','gongju') NOT NULL DEFAULT 'yingshi',
    category_display varchar(50) COMMENT '分类显示名称',
    is_active tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    is_default tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认引擎',
    
    -- 排序和权重
    sort_order int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越小越靠前',
    weight int(11) DEFAULT 1 COMMENT '权重，影响推荐度',
    
    -- 访问统计
    usage_count int(11) DEFAULT 0 COMMENT '使用次数统计',
    last_used datetime COMMENT '最后使用时间',
    
    -- 质量监控
    response_time int(11) COMMENT '平均响应时间（毫秒）',
    success_rate decimal(5,2) COMMENT '成功率（百分比）',
    last_checked datetime COMMENT '最后检查时间',
    status enum('active','slow','error','maintenance') DEFAULT 'active',
    
    -- 高级配置
    requires_proxy tinyint(1) DEFAULT 0 COMMENT '是否需要代理访问',
    rate_limit int(11) COMMENT '速率限制（请求/分钟）',
    api_key varchar(255) COMMENT 'API密钥（加密存储）',
    custom_headers json COMMENT '自定义HTTP头',
    
    -- 时间戳
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_name_category (name, category),
    INDEX idx_category (category),
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_usage_count (usage_count),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='搜索引擎配置表';
```

### 3. 网站状态监控表 (wp_yinghe_site_monitoring)

```sql
CREATE TABLE {$wpdb->prefix}yinghe_site_monitoring (
    -- 主键
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    site_id bigint(20) unsigned NOT NULL,
    
    -- 检查信息
    check_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    check_type enum('manual','scheduled','user_report') DEFAULT 'scheduled',
    
    -- 状态结果
    status_code int(11) COMMENT 'HTTP状态码',
    response_time int(11) COMMENT '响应时间（毫秒）',
    is_accessible tinyint(1) DEFAULT 1 COMMENT '是否可访问',
    error_message text COMMENT '错误信息',
    
    -- 内容检查
    content_changed tinyint(1) DEFAULT 0 COMMENT '内容是否发生变化',
    content_hash varchar(64) COMMENT '内容Hash值',
    title_changed tinyint(1) DEFAULT 0 COMMENT '标题是否变化',
    
    -- 安全检查
    ssl_valid tinyint(1) COMMENT 'SSL证书是否有效',
    ssl_expires datetime COMMENT 'SSL证书过期时间',
    malware_detected tinyint(1) DEFAULT 0 COMMENT '是否检测到恶意软件',
    
    -- 性能指标
    page_size int(11) COMMENT '页面大小（字节）',
    resource_count int(11) COMMENT '资源文件数量',
    redirect_count int(11) DEFAULT 0 COMMENT '重定向次数',
    final_url text COMMENT '最终URL（如果有重定向）',
    
    -- 元数据
    checker_ip varchar(45) COMMENT '检查者IP',
    user_agent varchar(500) COMMENT '检查时使用的User Agent',
    
    PRIMARY KEY (id),
    INDEX idx_site_id (site_id),
    INDEX idx_check_time (check_time),
    INDEX idx_status_code (status_code),
    INDEX idx_is_accessible (is_accessible),
    
    FOREIGN KEY (site_id) REFERENCES {$wpdb->prefix}posts(ID) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='网站状态监控记录表';
```

### 4. 用户行为分析表 (wp_yinghe_user_analytics)

```sql
CREATE TABLE {$wpdb->prefix}yinghe_user_analytics (
    -- 主键
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    
    -- 会话信息
    session_id varchar(64) NOT NULL COMMENT '会话ID',
    user_id bigint(20) unsigned NULL COMMENT 'WordPress用户ID',
    user_fingerprint varchar(64) NOT NULL COMMENT '用户指纹',
    
    -- 行为数据
    action_type enum('page_view','site_click','search','filter','theme_switch','scroll','download') NOT NULL,
    target_id bigint(20) unsigned COMMENT '目标对象ID（网站ID、分类ID等）',
    target_type varchar(50) COMMENT '目标类型（site、category、search等）',
    action_data json COMMENT '行为详细数据',
    
    -- 页面信息
    page_url text NOT NULL COMMENT '当前页面URL',
    page_title varchar(255) COMMENT '页面标题',
    referer_url text COMMENT '来源页面',
    
    -- 设备环境
    device_type enum('desktop','mobile','tablet') DEFAULT 'desktop',
    screen_resolution varchar(20) COMMENT '屏幕分辨率',
    browser_name varchar(50) COMMENT '浏览器名称',
    
    -- 位置信息
    scroll_depth int(3) COMMENT '滚动深度（百分比）',
    click_x int(11) COMMENT '点击X坐标',
    click_y int(11) COMMENT '点击Y坐标',
    
    -- 时间信息
    action_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    time_on_page int(11) COMMENT '页面停留时间（秒）',
    
    -- 上下文信息
    ab_test_variant varchar(50) COMMENT 'A/B测试变体',
    user_segment varchar(50) COMMENT '用户分群',
    
    PRIMARY KEY (id),
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_target_id (target_id),
    INDEX idx_action_time (action_time),
    INDEX idx_device_type (device_type),
    
    FOREIGN KEY (user_id) REFERENCES {$wpdb->prefix}users(ID) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='用户行为分析表';
```

### 5. 缓存管理表 (wp_yinghe_cache_meta)

```sql
CREATE TABLE {$wpdb->prefix}yinghe_cache_meta (
    -- 主键
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    
    -- 缓存标识
    cache_key varchar(255) NOT NULL COMMENT '缓存键名',
    cache_group varchar(100) NOT NULL DEFAULT 'default' COMMENT '缓存分组',
    cache_subgroup varchar(100) COMMENT '缓存子分组',
    
    -- 缓存数据
    cache_value longtext NOT NULL COMMENT '缓存值',
    cache_size int(11) COMMENT '缓存大小（字节）',
    
    -- 生命周期
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at datetime NOT NULL COMMENT '过期时间',
    last_accessed datetime COMMENT '最后访问时间',
    access_count int(11) DEFAULT 0 COMMENT '访问次数',
    
    -- 依赖管理
    depends_on json COMMENT '依赖的对象ID列表',
    tags json COMMENT '缓存标签，用于批量清理',
    
    -- 状态信息
    is_compressed tinyint(1) DEFAULT 0 COMMENT '是否压缩存储',
    compression_type varchar(20) COMMENT '压缩类型',
    status enum('active','expired','invalidated') DEFAULT 'active',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_cache_key_group (cache_key, cache_group),
    INDEX idx_cache_group (cache_group),
    INDEX idx_expires_at (expires_at),
    INDEX idx_status (status),
    INDEX idx_last_accessed (last_accessed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='缓存元数据管理表';
```

---

## 🔄 数据同步和维护

### 1. 数据清理策略

#### 访问日志清理
```sql
-- 删除30天前的访问记录
DELETE FROM {$wpdb->prefix}yinghe_site_visits 
WHERE visit_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 保留统计汇总数据
INSERT INTO {$wpdb->prefix}yinghe_site_visits_archive 
SELECT site_id, DATE(visit_time) as visit_date, COUNT(*) as visit_count
FROM {$wpdb->prefix}yinghe_site_visits 
WHERE visit_time < DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY site_id, DATE(visit_time);
```

#### 缓存过期清理
```sql
-- 清理过期缓存
DELETE FROM {$wpdb->prefix}yinghe_cache_meta 
WHERE expires_at < NOW() OR status = 'expired';

-- 清理长期未访问的缓存
DELETE FROM {$wpdb->prefix}yinghe_cache_meta 
WHERE last_accessed < DATE_SUB(NOW(), INTERVAL 7 DAY) 
AND access_count < 5;
```

### 2. 数据备份策略

#### 关键表备份
```bash
#!/bin/bash
# 备份关键业务表
mysqldump -u username -p database_name \
    wp_yinghe_site_visits \
    wp_yinghe_search_engines \
    wp_yinghe_site_monitoring \
    wp_yinghe_user_analytics \
    --single-transaction \
    --routines \
    --triggers \
    > yinghe_backup_$(date +%Y%m%d).sql
```

### 3. 性能优化

#### 索引优化建议
```sql
-- 基于查询模式创建复合索引
CREATE INDEX idx_visits_site_time ON {$wpdb->prefix}yinghe_site_visits(site_id, visit_time);
CREATE INDEX idx_visits_user_time ON {$wpdb->prefix}yinghe_site_visits(user_id, visit_time);

-- 分区表（适用于大数据量）
ALTER TABLE {$wpdb->prefix}yinghe_site_visits 
PARTITION BY RANGE (YEAR(visit_time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

---

## 📊 数据迁移和初始化

### 1. 从原型数据迁移

#### 网站数据导入脚本
```php
class YingheDataMigrator {
    public function migrate_from_prototype() {
        // 解析原型HTML文件
        $html_content = file_get_contents(get_template_directory() . '/prototype/index.html');
        $dom = new DOMDocument();
        $dom->loadHTML($html_content);
        
        // 提取网站卡片数据
        $cards = $dom->getElementsByClassName('url-card');
        
        foreach ($cards as $card) {
            $this->import_site_card($card);
        }
    }
    
    private function import_site_card($card_element) {
        // 提取网站信息
        $title = $this->extract_site_title($card_element);
        $url = $this->extract_site_url($card_element);
        $icon = $this->extract_site_icon($card_element);
        $category = $this->extract_site_category($card_element);
        
        // 创建WordPress文章
        $post_data = [
            'post_title' => $title,
            'post_type' => 'sites',
            'post_status' => 'publish',
            'meta_input' => [
                'site_url' => $url,
                'site_icon' => $icon,
                'display_mode' => $this->determine_display_mode($card_element),
                'visit_count_total' => 0
            ]
        ];
        
        $post_id = wp_insert_post($post_data);
        
        // 关联分类
        wp_set_object_terms($post_id, $category, 'site_category');
    }
}
```

### 2. 默认数据初始化

#### 搜索引擎预设数据
```sql
INSERT INTO {$wpdb->prefix}yinghe_search_engines 
(name, display_name, search_url, placeholder, category, sort_order, is_active) 
VALUES 
('qiku', '奇库影视', 'https://qkys1.cc/vodsearch/%s%-------------.html', '输入你想看的影片名', 'yingshi', 1, 1),
('555movie', '555电影', 'https://www.55yy6.com/vodsearch/%s%-------------.html', '输入你想看的影片名称', 'yingshi', 2, 1),
('douban', '豆瓣', 'https://search.douban.com/movie/subject_search?search_text=%s%', '输入影片、演员、导演名称', 'ziliao', 1, 1),
('imdb', 'IMDb', 'https://www.imdb.com/find/?q=%s%', 'Enter movie, actor, director name', 'ziliao', 2, 1);
```

---

## 🔧 数据库维护工具

### 1. 数据完整性检查

```php
class YingheDBMaintenance {
    public function check_data_integrity() {
        global $wpdb;
        
        $issues = [];
        
        // 检查孤儿访问记录
        $orphan_visits = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}yinghe_site_visits v 
            LEFT JOIN {$wpdb->prefix}posts p ON v.site_id = p.ID 
            WHERE p.ID IS NULL
        ");
        
        if ($orphan_visits > 0) {
            $issues[] = "发现 {$orphan_visits} 条孤儿访问记录";
        }
        
        // 检查缓存过期数据
        $expired_cache = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}yinghe_cache_meta 
            WHERE expires_at < NOW() AND status = 'active'
        ");
        
        if ($expired_cache > 0) {
            $issues[] = "发现 {$expired_cache} 条过期缓存数据";
        }
        
        return $issues;
    }
}
```

### 2. 性能监控

```php
class YingheDBMonitor {
    public function get_performance_stats() {
        global $wpdb;
        
        return [
            'total_visits' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}yinghe_site_visits"),
            'today_visits' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}yinghe_site_visits WHERE DATE(visit_time) = CURDATE()"),
            'avg_response_time' => $wpdb->get_var("SELECT AVG(response_time) FROM {$wpdb->prefix}yinghe_site_monitoring WHERE check_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)"),
            'cache_hit_rate' => $this->calculate_cache_hit_rate(),
            'db_size' => $this->get_database_size()
        ];
    }
}
```

---

## 📝 数据库文档说明

### 字段命名规范
- 使用小写字母和下划线
- 时间字段以 `_at` 或 `_time` 结尾
- 计数字段以 `_count` 结尾
- 布尔字段以 `is_` 或 `has_` 开头
- URL字段以 `_url` 结尾

### 数据类型选择
- **时间**: 使用 `datetime` 类型，支持时区
- **文本**: 使用 `utf8mb4` 编码支持emoji
- **JSON**: 存储结构化数据，便于查询
- **枚举**: 限定值范围，提高数据一致性

### 安全考虑
- 敏感数据加密存储
- IP地址脱敏处理
- 定期数据清理
- 访问权限控制

这套数据库架构设计确保了硬核指南主题的数据安全、性能和可扩展性，为后续功能开发提供了坚实的基础。