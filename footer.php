<?php
/**
 * 硬核指南主题 - 底部模板
 * 
 * @package YingheTheme
 * @subpackage Templates
 * @since 1.0.0
 */
?>

    <!-- 主题切换器 -->
    <div class="theme-switcher-container">
        <?php 
        yinghe_render_theme_switcher([
            'position' => 'fixed',
            'show_text' => false,
            'save_preference' => true
        ]); 
        ?>
    </div>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" id="back-to-top">
        <i class="io io-arrow-up"></i>
    </div>

    <!-- 底部小工具区域 -->
    <?php if (is_active_sidebar('footer-widgets')): ?>
        <footer class="site-footer">
            <div class="container">
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-widgets'); ?>
                </div>
            </div>
        </footer>
    <?php endif; ?>

    <!-- 版权信息 -->
    <div class="site-copyright">
        <div class="container">
            <div class="copyright-content">
                <p>&copy; <?php echo date('Y'); ?> <a href="<?php echo esc_url(home_url('/')); ?>"><?php bloginfo('name'); ?></a>. 
                <?php echo get_theme_mod('copyright_text', __('保留所有权利。', 'yinghe')); ?></p>
                
                <?php if (get_theme_mod('show_theme_credit', true)): ?>
                    <p class="theme-credit">
                        <?php printf(__('主题由 %s 强力驱动', 'yinghe'), '<a href="https://yinghezhinan.com" target="_blank" rel="noopener">硬核指南</a>'); ?>
                    </p>
                <?php endif; ?>
                
                <?php if (get_theme_mod('icp_number')): ?>
                    <p class="icp-info">
                        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
                            <?php echo esc_html(get_theme_mod('icp_number')); ?>
                        </a>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 模态窗口容器 -->
    <div id="modal-container"></div>

    <!-- JavaScript -->
    <script>
        // 页面加载完成后隐藏加载器
        window.addEventListener('load', function() {
            const loader = document.getElementById('page-loader');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 300);
            }
        });

        // 返回顶部按钮
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('back-to-top');
            if (backToTop) {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('show');
                } else {
                    backToTop.classList.remove('show');
                }
            }
        });

        // 返回顶部点击事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('#back-to-top')) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        });

        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('io-black-mode');
            
            if (isDark) {
                html.classList.remove('io-black-mode');
                html.classList.add('io-grey-mode');
                document.cookie = 'io_night_mode=1; path=/; max-age=31536000';
            } else {
                html.classList.remove('io-grey-mode');
                html.classList.add('io-black-mode');
                document.cookie = 'io_night_mode=0; path=/; max-age=31536000';
            }
            
            // 触发主题切换事件
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: isDark ? 'light' : 'dark' }
            }));
        }

        // 懒加载初始化
        if (typeof LazyLoad !== 'undefined') {
            const lazyLoadInstance = new LazyLoad({
                elements_selector: ".lazy",
                threshold: 300,
                callback_loaded: function(element) {
                    element.classList.add('loaded');
                }
            });
        }

        // PWA 安装提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // 显示安装按钮（如果有的话）
            const installButton = document.getElementById('pwa-install-button');
            if (installButton) {
                installButton.style.display = 'block';
                installButton.addEventListener('click', () => {
                    installButton.style.display = 'none';
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('用户接受了 PWA 安装');
                        }
                        deferredPrompt = null;
                    });
                });
            }
        });
    </script>

    <?php wp_footer(); ?>

    <!-- 自定义底部代码 -->
    <?php echo get_theme_mod('custom_footer_code', ''); ?>

    <!-- 统计代码 -->
    <?php echo get_theme_mod('analytics_code', ''); ?>

    <style>
        /* 页面加载器样式 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--yinghe-bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .loader-spinner {
            text-align: center;
        }

        /* 返回顶部按钮样式 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--yinghe-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: var(--yinghe-shadow);
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .back-to-top:hover {
            background: var(--yinghe-secondary);
            transform: translateY(-2px);
            box-shadow: var(--yinghe-shadow-lg);
        }

        .back-to-top i {
            font-size: 18px;
        }

        /* 主题切换器容器样式 */
        .theme-switcher-container {
            position: fixed;
            bottom: 100px;
            right: 30px;
            z-index: 1000;
        }

        /* 底部样式 */
        .site-footer {
            background: var(--yinghe-bg-secondary);
            border-top: 1px solid var(--yinghe-border-color);
            padding: 40px 0 20px;
            margin-top: 60px;
        }

        .site-copyright {
            background: var(--yinghe-bg-tertiary);
            border-top: 1px solid var(--yinghe-border-color);
            padding: 20px 0;
            text-align: center;
            font-size: 14px;
            color: var(--yinghe-text-muted);
        }

        .copyright-content p {
            margin: 5px 0;
        }

        .copyright-content a {
            color: var(--yinghe-primary);
            text-decoration: none;
        }

        .copyright-content a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .back-to-top,
            .theme-switcher-container {
                bottom: 20px;
                right: 20px;
            }

            .theme-switcher-container {
                bottom: 80px;
            }

            .back-to-top {
                width: 45px;
                height: 45px;
            }

            .site-footer {
                padding: 30px 0 15px;
                margin-top: 40px;
            }

            .site-copyright {
                padding: 15px 0;
                font-size: 13px;
            }
        }
    </style>

</body>
</html>