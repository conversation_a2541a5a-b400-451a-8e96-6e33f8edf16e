<?php
/**
 * 硬核指南主题 - 主页模板
 * 
 * @package YingheTheme
 * @subpackage Templates
 * @since 1.0.0
 */

get_header(); ?>

<div class="main-content flex-fill">
    <?php 
    // 渲染头部系统组件
    yinghe_render_header_system([
        'show_mini_header' => true,
        'show_big_header' => !is_paged(), // 只在第一页显示大头部
        'search_config' => [
            'enabled' => true,
            'engines' => yinghe_get_search_engines(),
            'placeholder' => __('输入关键字搜索', 'yinghe'),
        ],
        'announcement_config' => [
            'enabled' => get_theme_mod('show_announcements', true),
            'content' => get_option('yinghe_announcement_content', ''),
        ]
    ]);
    ?>

    <div id="content" class="container container-fluid customize-width">
        <div class="content">
            <div class="content-wrap">
                <div class="content-layout">
                    
                    <?php
                    // 获取所有网站分类
                    $categories = yinghe_get_site_categories_tree();
                    
                    foreach ($categories as $category) :
                        $sites = get_posts([
                            'post_type' => 'sites',
                            'numberposts' => 12,
                            'meta_key' => 'is_featured',
                            'meta_value' => '1',
                            'tax_query' => [
                                [
                                    'taxonomy' => 'site_category',
                                    'field' => 'term_id',
                                    'terms' => $category->term_id,
                                ]
                            ]
                        ]);
                        
                        if (!empty($sites)) :
                    ?>
                    
                    <div class="category-section" id="term-<?php echo esc_attr($category->term_id); ?>">
                        <?php
                        // 渲染分类头部
                        yinghe_render_category_header([
                            'category' => $category,
                            'show_hot_links' => $category->slug === 'cooperation',
                            'hot_links' => [
                                [
                                    'url' => 'https://simhaoka.com/phone/index?id=A1836F4FAE6B05E435438C28F731765A',
                                    'title' => '超值大流量卡',
                                    'icon' => 'io-star1',
                                    'class' => 'sim'
                                ],
                                [
                                    'url' => 'javascript:;',
                                    'title' => '美团外卖券',
                                    'icon' => 'io-heart3',
                                    'class' => 'meituan',
                                    'tooltip' => '每天领188元美团外卖神券',
                                    'modal_id' => 'window1'
                                ]
                            ]
                        ]);
                        ?>
                        
                        <div class="row io-mx-n2">
                            <?php foreach ($sites as $site) : ?>
                                <div class="url-card io-px-2 col-2a col-sm-2a col-md-3a col-lg-3a col-xl-4a col-xxl-6a">
                                    <?php
                                    // 渲染网站卡片组件
                                    yinghe_render_website_card([
                                        'site_id' => $site->ID,
                                        'display_mode' => get_post_meta($site->ID, 'display_mode', true) ?: 'mini',
                                        'show_visit_count' => true,
                                        'enable_tracking' => true,
                                        'custom_class' => get_post_meta($site->ID, 'card_style', true)
                                    ]);
                                    ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php
                        // 渲染子分类
                        if (!empty($category->children)) :
                            foreach ($category->children as $subcategory) :
                                $subsites = get_posts([
                                    'post_type' => 'sites',
                                    'numberposts' => 12,
                                    'tax_query' => [
                                        [
                                            'taxonomy' => 'site_category',
                                            'field' => 'term_id',
                                            'terms' => $subcategory->term_id,
                                        ]
                                    ]
                                ]);
                                
                                if (!empty($subsites)) :
                        ?>
                        
                        <div class="subcategory-section" id="term-<?php echo esc_attr($category->term_id); ?>-<?php echo esc_attr($subcategory->term_id); ?>">
                            <?php
                            // 渲染子分类头部
                            yinghe_render_category_header([
                                'category' => $subcategory,
                                'is_subcategory' => true,
                                'parent_category' => $category
                            ]);
                            ?>
                            
                            <div class="row io-mx-n2">
                                <?php foreach ($subsites as $site) : ?>
                                    <div class="url-card io-px-2 col-2a col-sm-2a col-md-3a col-lg-3a col-xl-4a col-xxl-6a">
                                        <?php
                                        yinghe_render_website_card([
                                            'site_id' => $site->ID,
                                            'display_mode' => get_post_meta($site->ID, 'display_mode', true) ?: 'default',
                                            'show_visit_count' => true,
                                            'enable_tracking' => true
                                        ]);
                                        ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <?php
                                endif;
                            endforeach;
                        endif;
                        ?>
                    </div>
                    
                    <?php
                        endif;
                    endforeach;
                    ?>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// 渲染模态窗口系统
yinghe_render_modal_system([
    'modals' => [
        'window1' => [
            'title' => '美团外卖优惠券',
            'content_tabs' => [
                '美团外卖' => [
                    'type' => 'image',
                    'content' => get_template_directory_uri() . '/assets/images/meituan-qr.webp'
                ]
            ]
        ],
        'window10' => [
            'title' => '滴滴打车优惠',
            'content_tabs' => [
                '滴滴打车' => [
                    'type' => 'image', 
                    'content' => get_template_directory_uri() . '/assets/images/didi-qr.webp'
                ]
            ]
        ]
    ]
]);

get_footer();
?>