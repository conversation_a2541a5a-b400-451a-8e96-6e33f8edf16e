<?php
/**
 * 硬核指南主题 - 头部模板
 * 
 * @package YingheTheme
 * @subpackage Templates
 * @since 1.0.0
 */
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?> class="io-grey-mode">
<head>
    <!-- 主题模式脚本 -->
    <script>
        var default_c = "io-grey-mode";
        var night = document.cookie.replace(/(?:(?:^|.*;\s*)io_night_mode\s*\=\s*([^;]*).*$)|^.*$/, "$1");
        try {
            if (night === "0" || (!night && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
                document.documentElement.classList.add("io-black-mode");
                document.documentElement.classList.remove(default_c);
            } else {
                document.documentElement.classList.remove("io-black-mode");
                document.documentElement.classList.add(default_c);
            }
        } catch (_) {}
    </script>

    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="renderer" content="webkit">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <meta name="force-rendering" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-title" content="<?php bloginfo('name'); ?>">
    <meta name="theme-color" content="#FFFFFF">

    <?php if (is_home() || is_front_page()): ?>
        <title><?php bloginfo('name'); ?> - <?php bloginfo('description'); ?></title>
    <?php else: ?>
        <title><?php wp_title(' - ', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <?php endif; ?>

    <!-- SEO Meta Tags -->
    <meta name="keywords" content="<?php echo esc_attr(get_theme_mod('site_keywords', '硬核指南,影视导航,免费影音')); ?>">
    <meta name="description" content="<?php echo esc_attr(get_bloginfo('description')); ?>">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>">
    
    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    
    <!-- Favicon -->
    <?php $favicon = get_theme_mod('favicon', get_template_directory_uri() . '/static/picture/favicon.png'); ?>
    <link rel="shortcut icon" href="<?php echo esc_url($favicon); ?>">
    <link rel="apple-touch-icon" href="<?php echo esc_url($favicon); ?>">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="<?php echo esc_url(get_template_directory_uri() . '/manifest.json'); ?>">
    
    <!-- Icon Sizes -->
    <link rel="shortcut icon" sizes="152x152" href="<?php echo esc_url(get_template_directory_uri() . '/icon/152x152.webp'); ?>">
    <link rel="shortcut icon" sizes="512x512" href="<?php echo esc_url(get_template_directory_uri() . '/icon/512x512.webp'); ?>">
    <link rel="apple-touch-icon" sizes="152x152" href="<?php echo esc_url(get_template_directory_uri() . '/icon/152x152.webp'); ?>">
    <link rel="apple-touch-icon" sizes="512x512" href="<?php echo esc_url(get_template_directory_uri() . '/icon/512x512.webp'); ?>">

    <!-- 预加载关键资源 -->
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) { 
            contain-intrinsic-size: 3000px 1500px 
        }
    </style>

    <?php wp_head(); ?>

    <!-- 自定义CSS变量 -->
    <style>
        .customize-width {
            max-width: <?php echo esc_attr(get_theme_mod('container_max_width', 1900)); ?>px;
        }
        .sidebar-nav {
            width: <?php echo esc_attr(get_theme_mod('sidebar_width', 220)); ?>px;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: <?php echo esc_attr(get_theme_mod('sidebar_width', 220)); ?>px;
            }
            .main-content .page-header {
                left: <?php echo esc_attr(get_theme_mod('sidebar_width', 220)); ?>px;
            }
        }
    </style>

    <!-- 自定义代码 -->
    <?php echo get_theme_mod('custom_head_code', ''); ?>
</head>

<body <?php body_class('home blog wp-theme-onenav sidebar_right mini-sidebar'); ?>>
    <?php wp_body_open(); ?>

    <!-- 页面加载指示器 -->
    <div id="page-loader" class="page-loader">
        <div class="loader-spinner">
            <div class="yinghe-loading"></div>
        </div>
    </div>

    <div id="sidebar" class="sticky sidebar-nav fade">
        <?php 
        // 渲染侧边栏导航组件
        yinghe_render_sidebar_nav([
            'show_domain_info' => get_theme_mod('show_domain_info', true),
            'domain_links' => get_option('yinghe_domain_links', [])
        ]); 
        ?>
    </div>