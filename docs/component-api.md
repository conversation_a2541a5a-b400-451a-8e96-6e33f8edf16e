# 硬核导航组件 API 文档
*WordPress 组件化开发 API 参考手册*

## 📋 目录

- [组件基础](#组件基础)
- [布局组件](#布局组件)
- [内容组件](#内容组件)
- [表单组件](#表单组件)
- [导航组件](#导航组件)
- [工具组件](#工具组件)
- [全局函数](#全局函数)
- [钩子系统](#钩子系统)

---

## 🔧 组件基础

### YingheAbstractComponent

所有组件的抽象基类，提供核心功能和接口。

#### 构造函数
```php
public function __construct(array $props = [])
```

**参数:**
- `$props` (array): 组件属性配置

**示例:**
```php
$component = new YingheSidebarNavComponent([
    'menu_location' => 'primary',
    'collapsible' => true,
    'class' => 'custom-sidebar'
]);
```

#### 核心方法

##### render()
```php
public function render(): string
```
渲染组件并返回HTML字符串。

**返回值:** (string) 组件的HTML输出

**示例:**
```php
$html = $component->render();
echo $html;
```

##### renderStatic()
```php
public static function renderStatic(array $props = []): string
```
静态方法直接渲染组件。

**参数:**
- `$props` (array): 组件属性

**返回值:** (string) 组件的HTML输出

**示例:**
```php
echo YingheSidebarNavComponent::renderStatic([
    'menu_location' => 'primary'
]);
```

#### 属性系统

##### 默认属性
```php
protected function getDefaultProps(): array
```
子类必须实现此方法定义默认属性。

##### 必需属性
```php
protected function getRequiredProps(): array
```
定义组件必需的属性。

##### 属性清理规则
```php
protected function getSanitizationRules(): array
```
定义属性的清理和验证规则。

**示例:**
```php
protected function getSanitizationRules(): array {
    return [
        'title' => 'sanitize_text_field',
        'url' => 'esc_url',
        'description' => 'wp_kses_post',
        'count' => 'absint'
    ];
}
```

#### CSS 和属性系统

##### 添加CSS类
```php
protected function addClass(string $class): void
```

##### 添加数据属性
```php
protected function addDataAttribute(string $key, string $value): void
```

**示例:**
```php
$this->addClass('featured');
$this->addDataAttribute('toggle', 'modal');
```

#### 缓存特性 (YingheCacheableComponent)

##### 启用缓存
```php
use YingheCacheableComponent;

protected int $cache_duration = HOUR_IN_SECONDS;
protected string $cache_group = 'custom_group';
```

##### 缓存方法
```php
public function getCachedRender(): string
public function clearCache(): bool
public static function clearAllCache(): void
```

**示例:**
```php
// 获取缓存内容
$html = $component->getCachedRender();

// 清除特定组件缓存
$component->clearCache();

// 清除所有组件缓存
YingheSidebarNavComponent::clearAllCache();
```

---

## 🏗️ 布局组件

### YingheSidebarNavComponent

侧边栏导航组件，提供多级菜单和响应式设计。

#### 属性配置

```php
$props = [
    'menu_location' => 'primary',           // 菜单位置
    'logo_config' => [                      // Logo配置
        'expanded' => '/path/to/logo.png',
        'collapsed' => '/path/to/small.png',
        'light' => '/path/to/light.png',
        'dark' => '/path/to/dark.png'
    ],
    'show_domain_info' => true,             // 显示域名信息
    'domain_links' => [                     // 域名链接
        [
            'url' => 'https://example.com',
            'title' => '主站',
            'text' => 'example.com'
        ]
    ],
    'sticky' => true,                       // 固定定位
    'collapsible' => true,                  // 可折叠
    'class' => 'custom-sidebar',            // 自定义CSS类
    'modifier' => 'dark'                    // 修饰符
];
```

#### 使用示例

```php
// 直接渲染
echo YingheSidebarNavComponent::renderStatic([
    'menu_location' => 'primary',
    'collapsible' => true
]);

// 实例化使用
$sidebar = new YingheSidebarNavComponent([
    'logo_config' => [
        'expanded' => get_theme_mod('logo_url')
    ]
]);
echo $sidebar->render();

// 在模板中使用助手函数
yinghe_render_sidebar_nav([
    'class' => 'main-sidebar'
]);
```

#### JavaScript API

组件自动注册JavaScript交互功能：

```javascript
// 平滑滚动到目标
document.querySelector('.sidebar-item a').click();

// 切换折叠状态
document.querySelector('.sidebar-more').click();

// 移动端菜单切换
document.getElementById('sidebar-switch').click();
```

### YingheHeaderSystemComponent

头部系统组件，包含导航栏、搜索区域和公告。

#### 属性配置

```php
$props = [
    'show_mini_header' => true,             // 显示迷你头部
    'show_big_header' => true,              // 显示大头部
    'search_config' => [                    // 搜索配置
        'enabled' => true,
        'engines' => [/* 搜索引擎配置 */],
        'placeholder' => '输入关键字搜索'
    ],
    'announcement_config' => [              // 公告配置
        'enabled' => true,
        'content' => '最新公告内容'
    ],
    'menu_items' => [                       // 菜单项配置
        'wechat' => [
            'icon' => 'io-wechat-o',
            'label' => '关注硬核',
            'image' => '/path/to/qr.png'
        ],
        'history' => [
            'icon' => 'io-clock1',
            'label' => '浏览记录'
        ]
    ]
];
```

#### JavaScript API

```javascript
// 搜索引擎切换
window.yingheSearch = {
    switchEngine: function(engineId),
    performSearch: function(query),
    getHistory: function()
};

// 浏览记录管理
window.yingheBrowsingHistory = {
    addItem: function(item),
    getHistory: function(),
    updateDisplay: function()
};
```

---

## 🎨 内容组件

### YingheWebsiteCardComponent

网站卡片组件，显示网站信息和交互功能。

#### 属性配置

```php
$props = [
    'site_id' => 123,                       // 网站ID
    'title' => '网站标题',                   // 标题
    'description' => '网站描述',             // 描述
    'url' => 'https://example.com',         // 链接
    'thumbnail' => '/path/to/thumb.jpg',     // 缩略图
    'favicon' => '/path/to/favicon.ico',     // 图标
    'category' => '工具',                    // 分类
    'tags' => ['tag1', 'tag2'],             // 标签
    'visit_count' => 100,                   // 访问次数
    'rating' => 4.5,                        // 评分
    'is_featured' => false,                 // 是否推荐
    'is_new' => true,                       // 是否新站
    'layout' => 'card',                     // 布局模式: card, list, grid
    'show_description' => true,             // 显示描述
    'show_stats' => true,                   // 显示统计
    'target' => '_blank',                   // 链接目标
    'class' => 'featured-site'              // 自定义类名
];
```

#### 使用示例

```php
// 简单使用
echo YingheWebsiteCardComponent::renderStatic([
    'site_id' => get_the_ID(),
    'title' => get_the_title(),
    'url' => get_field('site_url')
]);

// 完整配置
$card = new YingheWebsiteCardComponent([
    'site_id' => 123,
    'title' => '示例网站',
    'description' => '这是一个示例网站的描述',
    'url' => 'https://example.com',
    'thumbnail' => '/wp-content/uploads/thumb.jpg',
    'layout' => 'card',
    'is_featured' => true,
    'show_stats' => true
]);
echo $card->render();
```

#### 事件和回调

```javascript
// 卡片点击事件
document.addEventListener('yinghe:card:click', function(e) {
    const cardData = e.detail;
    console.log('卡片被点击:', cardData);
});

// 访问统计
document.addEventListener('yinghe:card:visit', function(e) {
    // 发送访问统计
});
```

### YingheCategorySectionComponent

分类区块组件，显示分类及其下的网站。

#### 属性配置

```php
$props = [
    'category_id' => 5,                     // 分类ID
    'title' => '分类标题',                   // 标题
    'description' => '分类描述',             // 描述
    'icon' => 'io-folder',                  // 图标
    'sites_per_page' => 12,                 // 每页网站数
    'layout' => 'grid',                     // 布局: grid, list
    'show_subcategories' => true,           // 显示子分类
    'show_count' => true,                   // 显示数量
    'order' => 'menu_order',                // 排序方式
    'order_direction' => 'ASC',             // 排序方向
    'load_more' => true,                    // 加载更多
    'ajax_load' => true,                    // Ajax加载
    'cache_duration' => HOUR_IN_SECONDS     // 缓存时长
];
```

---

## 📝 表单组件

### YingheSearchInterfaceComponent

高级搜索界面组件。

#### 属性配置

```php
$props = [
    'search_engines' => [                   // 搜索引擎配置
        [
            'id' => 'google',
            'name' => 'Google',
            'url' => 'https://google.com/search?q=%s',
            'placeholder' => '搜索关键词',
            'category' => 'general',
            'is_default' => true
        ]
    ],
    'show_suggestions' => true,             // 显示建议
    'show_history' => true,                 // 显示历史
    'max_suggestions' => 10,                // 最大建议数
    'placeholder' => '输入搜索关键词',        // 占位符
    'button_text' => '搜索',                // 按钮文本
    'layout' => 'horizontal',               // 布局: horizontal, vertical
    'style' => 'modern',                    // 样式: modern, classic
    'auto_focus' => true                    // 自动聚焦
];
```

#### JavaScript API

```javascript
// 搜索接口
window.yingheSearch = {
    // 执行搜索
    search: function(query, engine = null),
    
    // 添加搜索历史
    addHistory: function(query),
    
    // 获取搜索建议
    getSuggestions: function(query),
    
    // 切换搜索引擎
    setEngine: function(engineId),
    
    // 事件监听
    on: function(event, callback)
};

// 使用示例
yingheSearch.on('search', function(data) {
    console.log('搜索:', data.query, '引擎:', data.engine);
});

yingheSearch.search('WordPress 主题');
```

---

## 🧭 导航组件

### YingheBreadcrumbComponent

面包屑导航组件。

#### 属性配置

```php
$props = [
    'show_home' => true,                    // 显示首页
    'home_text' => '首页',                  // 首页文本
    'separator' => '/',                     // 分隔符
    'max_depth' => 5,                       // 最大深度
    'show_current' => true,                 // 显示当前页
    'schema' => true,                       // 结构化数据
    'class' => 'custom-breadcrumb'          // 自定义类名
];
```

### YinghePaginationComponent

分页组件。

#### 属性配置

```php
$props = [
    'total' => 100,                         // 总数
    'per_page' => 10,                       // 每页数量
    'current' => 1,                         // 当前页
    'show_numbers' => true,                 // 显示页码
    'show_prev_next' => true,               // 显示上下页
    'show_first_last' => true,              // 显示首末页
    'prev_text' => '上一页',                // 上一页文本
    'next_text' => '下一页',                // 下一页文本
    'ajax' => true,                         // Ajax分页
    'url_format' => '/page/%d/',            // URL格式
    'class' => 'custom-pagination'          // 自定义类名
];
```

---

## 🛠️ 工具组件

### YingheThemeSwitcherComponent

主题切换器组件。

#### 属性配置

```php
$props = [
    'themes' => ['light', 'dark', 'auto'],  // 可用主题
    'default_theme' => 'auto',              // 默认主题
    'show_labels' => true,                  // 显示标签
    'position' => 'header',                 // 位置
    'storage' => 'localStorage',            // 存储方式
    'animation' => true                     // 切换动画
];
```

#### JavaScript API

```javascript
// 主题切换器
window.yingheTheme = {
    // 设置主题
    setTheme: function(theme),
    
    // 获取当前主题
    getTheme: function(),
    
    // 切换主题
    toggle: function(),
    
    // 事件监听
    on: function(event, callback)
};

// 使用示例
yingheTheme.on('change', function(theme) {
    console.log('主题已切换为:', theme);
});

yingheTheme.setTheme('dark');
```

---

## 🌐 全局函数

### 渲染助手函数

```php
// 渲染侧边栏导航
yinghe_render_sidebar_nav(array $props = []): void

// 渲染头部系统
yinghe_render_header_system(array $props = []): void

// 渲染网站卡片
yinghe_render_website_card(array $props = []): void

// 渲染搜索界面
yinghe_render_search_interface(array $props = []): void

// 渲染分页
yinghe_render_pagination(array $props = []): void
```

### 获取组件函数

```php
// 获取组件HTML（不直接输出）
yinghe_get_sidebar_nav(array $props = []): string
yinghe_get_header_system(array $props = []): string
yinghe_get_website_card(array $props = []): string
```

### 工具函数

```php
// 获取组件注册中心实例
yinghe_get_component_registry(): YingheComponentRegistry

// 获取资源优化器实例
yinghe_get_asset_optimizer(): YingheAssetOptimizer

// 注册自定义组件
yinghe_register_component(string $name, string $class, array $config = []): void

// 检查组件是否存在
yinghe_component_exists(string $name): bool

// 获取组件配置
yinghe_get_component_config(string $name): array|null
```

---

## 🔗 钩子系统

### 组件生命周期钩子

```php
// 组件初始化后
do_action('yinghe_component_initialized_{component_name}', $component);

// 组件加载后
do_action('yinghe_component_loaded', $name, $path, $category);

// 所有组件加载完成
do_action('yinghe_all_components_loaded', $loaded_components);

// 组件渲染前
apply_filters('yinghe_before_render_{component_name}', $content, $props, $component);

// 组件渲染后
apply_filters('yinghe_after_render_{component_name}', $content, $props, $component);
```

### 资源管理钩子

```php
// 资源注册前
do_action('yinghe_before_enqueue_assets');

// 资源注册后
do_action('yinghe_after_enqueue_assets');

// 组件样式过滤
apply_filters('yinghe_component_styles_{component_name}', $styles, $component);

// 组件脚本过滤
apply_filters('yinghe_component_scripts_{component_name}', $scripts, $component);
```

### 缓存相关钩子

```php
// 组件缓存后
do_action('yinghe_component_cached', $component_name, $cache_key);

// 缓存清理前
do_action('yinghe_before_cache_clear', $cache_group);

// 缓存清理后
do_action('yinghe_after_cache_clear', $cache_group);
```

### 使用示例

```php
// 修改侧边栏导航的渲染内容
add_filter('yinghe_after_render_sidebar-nav', function($content, $props, $component) {
    if ($props['show_custom_footer']) {
        $content .= '<div class="custom-footer">自定义底部内容</div>';
    }
    return $content;
}, 10, 3);

// 在组件加载后执行自定义逻辑
add_action('yinghe_component_loaded', function($name, $path, $category) {
    if ($category === 'layout') {
        // 布局组件加载后的处理
        do_something();
    }
}, 10, 3);

// 修改组件默认样式
add_filter('yinghe_component_styles_website-card', function($styles, $component) {
    $styles .= '.yinghe-website-card { border-radius: 8px; }';
    return $styles;
}, 10, 2);
```

---

## 📚 扩展开发

### 创建自定义组件

```php
<?php
class MyCustomComponent extends YingheAbstractComponent {
    protected function getDefaultProps(): array {
        return [
            'title' => '',
            'content' => '',
            'show_border' => true
        ];
    }
    
    protected function getRequiredProps(): array {
        return ['title'];
    }
    
    protected function renderComponent(): void {
        ?>
        <div<?php echo $this->getAttributes(); ?>>
            <h3><?php echo esc_html($this->props['title']); ?></h3>
            <div class="content">
                <?php echo wp_kses_post($this->props['content']); ?>
            </div>
        </div>
        <?php
    }
    
    protected function getComponentStyles(): string {
        return '.my-custom-component { padding: 20px; }';
    }
}

// 注册组件
yinghe_register_component('my-custom', MyCustomComponent::class);
```

### 使用自定义组件

```php
// 渲染自定义组件
echo MyCustomComponent::renderStatic([
    'title' => '我的标题',
    'content' => '组件内容',
    'class' => 'featured'
]);
```

---

这个API文档涵盖了硬核导航主题组件系统的所有核心功能和使用方法。通过这些API，开发者可以轻松创建、使用和扩展组件。