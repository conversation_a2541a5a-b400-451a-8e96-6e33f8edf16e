# WordPress 组件化开发指南
*硬核导航主题 - 开发指南与最佳实践*

## 📋 目录

1. [开发环境设置](#开发环境设置)
2. [项目结构规范](#项目结构规范)
3. [组件开发最佳实践](#组件开发最佳实践)
4. [代码规范](#代码规范)
5. [性能优化](#性能优化)
6. [安全准则](#安全准则)
7. [测试指南](#测试指南)
8. [调试技巧](#调试技巧)
9. [版本控制](#版本控制)
10. [团队协作](#团队协作)

---

## 🛠️ 开发环境设置

### 系统要求

#### 基础环境
```bash
# PHP 环境
PHP >= 8.0
- ext-json
- ext-mbstring
- ext-mysqli
- ext-zip
- ext-curl

# WordPress
WordPress >= 6.0

# Node.js 环境
Node.js >= 16.0
npm >= 8.0

# 构建工具
Composer >= 2.0
Webpack >= 5.0 或 Vite >= 3.0
```

#### 推荐开发工具
```bash
# 代码编辑器
VS Code + 扩展包:
- PHP Intelephense
- WordPress Snippets
- Auto Rename Tag
- Prettier
- ESLint

# 调试工具
Xdebug >= 3.0
Query Monitor
Debug Bar
```

### 环境配置

#### 1. 克隆项目
```bash
cd /path/to/wordpress/wp-content/themes/
git clone <repository-url> yinhedaohang
cd yinhedaohang
```

#### 2. 安装依赖
```bash
# PHP 依赖
composer install

# Node.js 依赖
npm install

# 开发依赖
npm install --only=dev
```

#### 3. 环境变量配置
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置
nano .env
```

`.env` 文件示例：
```env
# 开发模式
WP_DEBUG=true
WP_DEBUG_LOG=true
WP_DEBUG_DISPLAY=false
SCRIPT_DEBUG=true

# 主题配置
YINGHE_VERSION=1.0.0
YINGHE_DEBUG=true
YINGHE_CACHE_ENABLED=false

# 构建配置
NODE_ENV=development
WEBPACK_DEV_SERVER=true
HOT_RELOAD=true

# API 配置
API_BASE_URL=https://api.example.com
API_TIMEOUT=30000
```

#### 4. 启动开发服务器
```bash
# 启动构建监听
npm run dev

# 或使用热重载
npm run dev:hot

# 后台运行
npm run dev -- --daemon
```

---

## 📁 项目结构规范

### 目录命名规范

```
yinhedaohang/
├── core/                    # 核心系统（单一职责）
├── components/              # 组件库（按类型分组）
├── assets/                  # 静态资源（源码与构建分离）
├── templates/               # 页面模板（遵循 WordPress 模板层级）
├── admin/                   # 后台功能（独立模块）
├── tests/                   # 测试文件（按类型分层）
├── docs/                    # 文档（完整文档体系）
├── build/                   # 构建配置（工具配置）
└── languages/               # 国际化（多语言支持）
```

### 文件命名规范

#### PHP 文件
```
class-{component-name}.php          # 组件类文件
abstract-{abstract-name}.php        # 抽象类文件
trait-{trait-name}.php              # 特性文件
interface-{interface-name}.php      # 接口文件
{template-name}.php                 # 模板文件
```

#### JavaScript 文件
```
{component-name}.js                 # 组件脚本
{module-name}.module.js             # 模块文件
{utility-name}.util.js              # 工具函数
{config-name}.config.js             # 配置文件
```

#### CSS/SCSS 文件
```
_{partial-name}.scss                # 部分文件（下划线开头）
{component-name}.scss               # 组件样式
{utility-name}.util.scss            # 工具样式
```

### 代码组织原则

#### 1. 单一职责原则
```php
// ✅ 正确：每个类只负责一个功能
class YingheAssetOptimizer {
    public function enqueueStyles(): void { }
    public function enqueueScripts(): void { }
    public function optimizeAssets(): void { }
}

// ❌ 错误：一个类承担多个职责
class YingheManager {
    public function enqueueAssets(): void { }
    public function handleAjax(): void { }
    public function manageSecurity(): void { }
    public function cacheData(): void { }
}
```

#### 2. 开放/封闭原则
```php
// ✅ 正确：通过继承扩展功能
abstract class YingheAbstractComponent {
    abstract protected function renderComponent(): void;
    
    // 可扩展的钩子系统
    protected function beforeRender(): string {
        return apply_filters("yinghe_before_render_{$this->component_name}", '');
    }
}

class YingheCustomComponent extends YingheAbstractComponent {
    protected function renderComponent(): void {
        // 具体实现
    }
}
```

#### 3. 依赖注入原则
```php
// ✅ 正确：依赖注入
class YingheComponentRenderer {
    private YingheAssetOptimizer $asset_optimizer;
    private YingheSecurityManager $security_manager;
    
    public function __construct(
        YingheAssetOptimizer $asset_optimizer,
        YingheSecurityManager $security_manager
    ) {
        $this->asset_optimizer = $asset_optimizer;
        $this->security_manager = $security_manager;
    }
}

// ❌ 错误：硬编码依赖
class YingheComponentRenderer {
    public function render(): void {
        $optimizer = new YingheAssetOptimizer(); // 硬编码
        $security = new YingheSecurityManager(); // 硬编码
    }
}
```

---

## 🧩 组件开发最佳实践

### 组件设计原则

#### 1. 组件原子性
```php
// ✅ 正确：原子化组件
class YingheButtonComponent extends YingheAbstractComponent {
    protected function getDefaultProps(): array {
        return [
            'text' => '',
            'type' => 'button',
            'size' => 'medium',
            'variant' => 'primary',
            'disabled' => false,
            'loading' => false
        ];
    }
    
    protected function renderComponent(): void {
        $classes = $this->buildButtonClasses();
        $attributes = $this->buildButtonAttributes();
        
        ?>
        <button <?php echo $attributes; ?> class="<?php echo $classes; ?>">
            <?php if ($this->props['loading']): ?>
                <span class="loading-spinner"></span>
            <?php endif; ?>
            <?php echo esc_html($this->props['text']); ?>
        </button>
        <?php
    }
}
```

#### 2. 组件复用性
```php
// ✅ 正确：可复用的卡片组件
class YingheCardComponent extends YingheAbstractComponent {
    protected function getDefaultProps(): array {
        return [
            'title' => '',
            'content' => '',
            'footer' => '',
            'image' => '',
            'link' => '',
            'shadow' => true,
            'border' => true
        ];
    }
    
    protected function renderComponent(): void {
        ?>
        <div<?php echo $this->getAttributes(); ?>>
            <?php $this->renderImage(); ?>
            <?php $this->renderHeader(); ?>
            <?php $this->renderContent(); ?>
            <?php $this->renderFooter(); ?>
        </div>
        <?php
    }
    
    private function renderImage(): void {
        if (!empty($this->props['image'])) {
            echo sprintf(
                '<img src="%s" alt="%s" class="card-image">',
                esc_url($this->props['image']),
                esc_attr($this->props['title'])
            );
        }
    }
}

// 使用示例：网站卡片继承基础卡片
class YingheWebsiteCardComponent extends YingheCardComponent {
    protected function getDefaultProps(): array {
        return array_merge(parent::getDefaultProps(), [
            'site_url' => '',
            'visit_count' => 0,
            'rating' => 0
        ]);
    }
}
```

#### 3. 组件可配置性
```php
class YingheDataTableComponent extends YingheAbstractComponent {
    protected function getDefaultProps(): array {
        return [
            'data' => [],
            'columns' => [],
            'sortable' => true,
            'filterable' => true,
            'paginated' => true,
            'per_page' => 10,
            'search' => true,
            'actions' => [],
            'row_actions' => [],
            'bulk_actions' => [],
            'responsive' => true,
            'striped' => true,
            'bordered' => false,
            'compact' => false
        ];
    }
}
```

### 组件生命周期管理

#### 1. 初始化阶段
```php
abstract class YingheAbstractComponent {
    public function __construct(array $props = []) {
        // 1. 解析组件名称
        $this->component_name = $this->resolveComponentName();
        
        // 2. 生成唯一ID
        $this->unique_id = $this->generateUniqueId();
        
        // 3. 处理属性
        $this->props = $this->parseProps($props);
        
        // 4. 组件初始化
        $this->init();
        
        // 5. 注册资源
        $this->registerAssets();
        
        // 6. 触发初始化钩子
        do_action("yinghe_component_initialized_{$this->component_name}", $this);
    }
}
```

#### 2. 渲染阶段
```php
public function render(): string {
    ob_start();
    
    try {
        // 1. 渲染前钩子
        echo $this->beforeRender();
        
        // 2. 主要渲染逻辑
        $this->renderComponent();
        
        // 3. 渲染后钩子
        echo $this->afterRender();
        
    } catch (Exception $e) {
        // 4. 错误处理
        $this->handleRenderError($e);
    }
    
    return ob_get_clean();
}
```

#### 3. 销毁阶段
```php
public function __destruct() {
    // 清理临时数据
    $this->cleanup();
    
    // 触发销毁钩子
    do_action("yinghe_component_destroyed_{$this->component_name}", $this);
}
```

### 状态管理

#### 1. 组件状态
```php
class YingheToggleComponent extends YingheAbstractComponent {
    private bool $is_open = false;
    
    protected function init(): void {
        $this->is_open = $this->props['default_open'] ?? false;
    }
    
    public function toggle(): void {
        $this->is_open = !$this->is_open;
        $this->addDataAttribute('state', $this->is_open ? 'open' : 'closed');
    }
    
    public function isOpen(): bool {
        return $this->is_open;
    }
}
```

#### 2. 全局状态
```php
class YingheGlobalState {
    private static array $state = [];
    
    public static function set(string $key, $value): void {
        self::$state[$key] = $value;
        do_action('yinghe_state_changed', $key, $value);
    }
    
    public static function get(string $key, $default = null) {
        return self::$state[$key] ?? $default;
    }
    
    public static function subscribe(string $key, callable $callback): void {
        add_action('yinghe_state_changed', function($changed_key, $value) use ($key, $callback) {
            if ($changed_key === $key) {
                $callback($value);
            }
        });
    }
}
```

---

## 📝 代码规范

### PHP 编码规范

#### 1. 类型声明
```php
// ✅ 正确：完整的类型声明
class YingheApiClient {
    private string $base_url;
    private int $timeout;
    private array $headers;
    
    public function __construct(
        string $base_url,
        int $timeout = 30,
        array $headers = []
    ) {
        $this->base_url = $base_url;
        $this->timeout = $timeout;
        $this->headers = $headers;
    }
    
    public function get(string $endpoint): ?array {
        // 实现
    }
    
    private function makeRequest(string $method, string $endpoint, array $data = []): array {
        // 实现
    }
}

// ❌ 错误：缺少类型声明
class YingheApiClient {
    private $base_url; // 缺少类型
    
    public function get($endpoint) { // 缺少参数和返回值类型
        // 实现
    }
}
```

#### 2. 错误处理
```php
// ✅ 正确：完善的错误处理
class YingheFileManager {
    public function readFile(string $path): string {
        if (!file_exists($path)) {
            throw new InvalidArgumentException("文件不存在: {$path}");
        }
        
        if (!is_readable($path)) {
            throw new RuntimeException("文件不可读: {$path}");
        }
        
        $content = file_get_contents($path);
        
        if ($content === false) {
            throw new RuntimeException("读取文件失败: {$path}");
        }
        
        return $content;
    }
    
    public function safeReadFile(string $path): ?string {
        try {
            return $this->readFile($path);
        } catch (Exception $e) {
            error_log("文件读取错误: " . $e->getMessage());
            return null;
        }
    }
}
```

#### 3. 文档注释
```php
/**
 * 网站卡片组件
 * 
 * 用于显示网站信息的卡片组件，支持多种布局和交互功能。
 * 
 * @package Yinghe
 * @subpackage Components
 * @since 1.0.0
 * 
 * @example
 * ```php
 * echo YingheWebsiteCardComponent::renderStatic([
 *     'title' => '网站标题',
 *     'url' => 'https://example.com',
 *     'layout' => 'card'
 * ]);
 * ```
 */
class YingheWebsiteCardComponent extends YingheAbstractComponent {
    /**
     * 获取默认属性
     * 
     * @since 1.0.0
     * @return array<string, mixed> 默认属性数组
     */
    protected function getDefaultProps(): array {
        return [
            'title' => '',              // 网站标题
            'url' => '',                // 网站链接
            'description' => '',        // 网站描述
            'thumbnail' => '',          // 缩略图URL
            'layout' => 'card',         // 布局类型: card|list|grid
            'target' => '_blank'        // 链接目标
        ];
    }
    
    /**
     * 渲染网站卡片
     * 
     * @since 1.0.0
     * @throws RuntimeException 当必需属性缺失时
     * @return void
     */
    protected function renderComponent(): void {
        // 实现
    }
}
```

### JavaScript 编码规范

#### 1. 模块化设计
```javascript
// ✅ 正确：ES6 模块化
// components/search/search-interface.js
export class SearchInterface {
    constructor(element, options = {}) {
        this.element = element;
        this.options = { ...this.getDefaultOptions(), ...options };
        this.init();
    }
    
    getDefaultOptions() {
        return {
            placeholder: '输入搜索关键词',
            autoFocus: true,
            debounceTime: 300
        };
    }
    
    init() {
        this.bindEvents();
        this.setupAutocomplete();
    }
    
    bindEvents() {
        this.element.addEventListener('input', this.debounce(
            this.handleInput.bind(this),
            this.options.debounceTime
        ));
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// main.js
import { SearchInterface } from './components/search/search-interface.js';

document.addEventListener('DOMContentLoaded', () => {
    const searchElement = document.querySelector('.search-interface');
    if (searchElement) {
        new SearchInterface(searchElement);
    }
});
```

#### 2. 事件管理
```javascript
// ✅ 正确：事件发布/订阅模式
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }
    
    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }
}

// 使用示例
const yingheEvents = new EventEmitter();

// 监听网站卡片点击
yingheEvents.on('card:click', (data) => {
    console.log('卡片被点击:', data);
    // 发送统计数据
    analytics.track('card_click', data);
});

// 触发事件
yingheEvents.emit('card:click', {
    title: '网站标题',
    url: 'https://example.com'
});
```

### SCSS 编码规范

#### 1. BEM 命名规范
```scss
// ✅ 正确：BEM 命名
.yinghe-website-card {
    display: block;
    padding: 20px;
    border-radius: 8px;
    
    // 元素
    &__image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 4px;
    }
    
    &__title {
        font-size: 18px;
        font-weight: 600;
        margin: 12px 0 8px;
        color: var(--text-primary);
    }
    
    &__description {
        font-size: 14px;
        color: var(--text-secondary);
        line-height: 1.5;
    }
    
    // 修饰符
    &--featured {
        border: 2px solid var(--color-primary);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    &--compact {
        padding: 12px;
        
        .yinghe-website-card__title {
            font-size: 16px;
        }
    }
    
    // 状态
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
    
    &.is-loading {
        opacity: 0.6;
        pointer-events: none;
    }
}

// ❌ 错误：不规范的命名
.websiteCard { // 不应使用驼峰命名
    .title { // 缺少命名空间
        .icon { // 嵌套过深
            color: red; // 硬编码颜色值
        }
    }
}
```

#### 2. 变量和混合
```scss
// ✅ 正确：系统化的变量管理
// abstracts/_variables.scss
:root {
    // 颜色系统
    --color-primary: #3b82f6;
    --color-primary-dark: #2563eb;
    --color-primary-light: #93c5fd;
    
    --color-secondary: #6b7280;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    
    // 文本颜色
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    // 间距系统
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    // 圆角
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    // 阴影
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

// abstracts/_mixins.scss
@mixin button-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:focus {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
    }
    
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

@mixin button-variant($bg-color, $text-color: white) {
    background-color: $bg-color;
    color: $text-color;
    
    &:hover:not(:disabled) {
        background-color: darken($bg-color, 10%);
    }
}

// 响应式混合
@mixin respond-to($breakpoint) {
    @if $breakpoint == mobile {
        @media (max-width: 767px) { @content; }
    }
    @if $breakpoint == tablet {
        @media (min-width: 768px) and (max-width: 1023px) { @content; }
    }
    @if $breakpoint == desktop {
        @media (min-width: 1024px) { @content; }
    }
}
```

---

## ⚡ 性能优化

### 前端性能优化

#### 1. 资源加载优化
```php
class YingheAssetOptimizer {
    public function enqueueAssets(): void {
        // 关键CSS内联
        $critical_css = $this->getCriticalCSS();
        if ($critical_css) {
            wp_add_inline_style('yinghe-critical', $critical_css);
        }
        
        // 非关键CSS延迟加载
        wp_enqueue_style(
            'yinghe-main',
            YINGHE_URL . '/assets/dist/css/main.css',
            [],
            YINGHE_VERSION,
            'print' // 使用 print 媒体类型延迟加载
        );
        
        // JavaScript 延迟加载
        wp_enqueue_script(
            'yinghe-main',
            YINGHE_URL . '/assets/dist/js/main.js',
            [],
            YINGHE_VERSION,
            true // 放在页脚
        );
        
        // 添加 defer 属性
        add_filter('script_loader_tag', [$this, 'addDeferAttribute'], 10, 2);
    }
    
    public function addDeferAttribute(string $tag, string $handle): string {
        $defer_scripts = ['yinghe-main', 'yinghe-components'];
        
        if (in_array($handle, $defer_scripts)) {
            return str_replace('<script ', '<script defer ', $tag);
        }
        
        return $tag;
    }
    
    private function getCriticalCSS(): string {
        // 获取当前页面的关键CSS
        $page_template = get_page_template_slug();
        $critical_file = YINGHE_PATH . "/assets/critical/{$page_template}.css";
        
        return file_exists($critical_file) ? file_get_contents($critical_file) : '';
    }
}
```

#### 2. 图片优化
```php
class YingheImageOptimizer {
    public function __construct() {
        add_filter('wp_get_attachment_image_attributes', [$this, 'addLazyLoading'], 10, 2);
        add_filter('the_content', [$this, 'addLazyLoadingToContent']);
    }
    
    public function addLazyLoading(array $attr, WP_Post $attachment): array {
        if (!is_admin() && !wp_is_mobile()) {
            $attr['loading'] = 'lazy';
            $attr['decoding'] = 'async';
        }
        
        return $attr;
    }
    
    public function generateResponsiveImage(int $attachment_id, array $sizes = []): string {
        $default_sizes = [
            'mobile' => 320,
            'tablet' => 768,
            'desktop' => 1200
        ];
        
        $sizes = array_merge($default_sizes, $sizes);
        $srcset = [];
        
        foreach ($sizes as $name => $width) {
            $image = wp_get_attachment_image_src($attachment_id, "custom-{$width}");
            if ($image) {
                $srcset[] = "{$image[0]} {$width}w";
            }
        }
        
        $src = wp_get_attachment_image_src($attachment_id, 'large')[0];
        
        return sprintf(
            '<img src="%s" srcset="%s" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" alt="%s" loading="lazy">',
            esc_url($src),
            esc_attr(implode(', ', $srcset)),
            esc_attr(get_post_meta($attachment_id, '_wp_attachment_image_alt', true))
        );
    }
}
```

### 后端性能优化

#### 1. 数据库查询优化
```php
class YingheSiteQueryOptimizer {
    public function getPopularSites(int $limit = 10, string $category = ''): array {
        global $wpdb;
        
        $cache_key = "popular_sites_{$limit}_{$category}";
        $cached_result = wp_cache_get($cache_key, 'yinghe_sites');
        
        if (false !== $cached_result) {
            return $cached_result;
        }
        
        // 优化的查询
        $sql = "
            SELECT p.ID, p.post_title, p.post_excerpt, 
                   pm.meta_value as visit_count,
                   t.name as category_name
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = 'visit_count'
            LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
            LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            LEFT JOIN {$wpdb->terms} t ON tt.term_id = t.term_id
            WHERE p.post_type = 'site'
              AND p.post_status = 'publish'
        ";
        
        $params = [];
        
        if (!empty($category)) {
            $sql .= " AND t.slug = %s";
            $params[] = $category;
        }
        
        $sql .= "
            ORDER BY CAST(pm.meta_value AS UNSIGNED) DESC
            LIMIT %d
        ";
        $params[] = $limit;
        
        $prepared_sql = $wpdb->prepare($sql, $params);
        $results = $wpdb->get_results($prepared_sql, ARRAY_A);
        
        // 缓存结果
        wp_cache_set($cache_key, $results, 'yinghe_sites', HOUR_IN_SECONDS);
        
        return $results;
    }
    
    public function invalidateCache(int $site_id): void {
        // 清除相关缓存
        wp_cache_delete("site_data_{$site_id}", 'yinghe_sites');
        wp_cache_flush_group('yinghe_sites');
    }
}
```

#### 2. 对象缓存
```php
class YingheCacheManager {
    private const CACHE_VERSION = '1.0';
    
    public function get(string $key, string $group = 'default') {
        $versioned_key = $this->getVersionedKey($key);
        return wp_cache_get($versioned_key, $group);
    }
    
    public function set(string $key, $data, string $group = 'default', int $expiration = 0): bool {
        $versioned_key = $this->getVersionedKey($key);
        return wp_cache_set($versioned_key, $data, $group, $expiration);
    }
    
    public function delete(string $key, string $group = 'default'): bool {
        $versioned_key = $this->getVersionedKey($key);
        return wp_cache_delete($versioned_key, $group);
    }
    
    public function flushGroup(string $group): bool {
        return wp_cache_flush_group($group);
    }
    
    private function getVersionedKey(string $key): string {
        return self::CACHE_VERSION . '_' . $key;
    }
    
    // 智能缓存
    public function remember(string $key, callable $callback, int $expiration = HOUR_IN_SECONDS, string $group = 'default') {
        $cached = $this->get($key, $group);
        
        if (false !== $cached) {
            return $cached;
        }
        
        $data = $callback();
        $this->set($key, $data, $group, $expiration);
        
        return $data;
    }
}

// 使用示例
$cache = new YingheCacheManager();

$popular_sites = $cache->remember(
    'popular_sites_tech',
    function() {
        return get_posts([
            'post_type' => 'site',
            'meta_query' => [
                [
                    'key' => 'category',
                    'value' => 'tech',
                    'compare' => '='
                ]
            ],
            'orderby' => 'meta_value_num',
            'meta_key' => 'visit_count',
            'order' => 'DESC',
            'posts_per_page' => 10
        ]);
    },
    HOUR_IN_SECONDS,
    'site_queries'
);
```

---

## 🔒 安全准则

### 输入验证和清理

#### 1. 用户输入验证
```php
class YingheInputValidator {
    public static function validateSiteData(array $data): array {
        $errors = [];
        $clean_data = [];
        
        // 验证网站标题
        if (empty($data['title'])) {
            $errors['title'] = '网站标题不能为空';
        } else {
            $clean_data['title'] = sanitize_text_field($data['title']);
            if (strlen($clean_data['title']) > 100) {
                $errors['title'] = '网站标题不能超过100个字符';
            }
        }
        
        // 验证网站URL
        if (empty($data['url'])) {
            $errors['url'] = '网站URL不能为空';
        } else {
            $clean_data['url'] = esc_url_raw($data['url']);
            if (!filter_var($clean_data['url'], FILTER_VALIDATE_URL)) {
                $errors['url'] = '请输入有效的URL地址';
            }
        }
        
        // 验证描述
        if (!empty($data['description'])) {
            $clean_data['description'] = wp_kses_post($data['description']);
            if (strlen($clean_data['description']) > 500) {
                $errors['description'] = '描述不能超过500个字符';
            }
        }
        
        // 验证分类
        if (!empty($data['category_id'])) {
            $category_id = absint($data['category_id']);
            if ($category_id > 0 && term_exists($category_id, 'site_category')) {
                $clean_data['category_id'] = $category_id;
            } else {
                $errors['category_id'] = '无效的分类ID';
            }
        }
        
        return [
            'data' => $clean_data,
            'errors' => $errors,
            'is_valid' => empty($errors)
        ];
    }
    
    public static function sanitizeSearchQuery(string $query): string {
        // 移除HTML标签
        $query = wp_strip_all_tags($query);
        
        // 限制长度
        $query = substr($query, 0, 100);
        
        // 移除特殊字符（保留中文、英文、数字、空格）
        $query = preg_replace('/[^\p{L}\p{N}\s]/u', '', $query);
        
        // 清理多余空格
        $query = preg_replace('/\s+/', ' ', trim($query));
        
        return $query;
    }
}
```

#### 2. CSRF 保护
```php
class YingheSecurityManager {
    public static function createNonce(string $action): string {
        return wp_create_nonce("yinghe_{$action}");
    }
    
    public static function verifyNonce(string $nonce, string $action): bool {
        return wp_verify_nonce($nonce, "yinghe_{$action}");
    }
    
    public static function requireNonce(string $action, string $nonce_field = '_yinghe_nonce'): void {
        $nonce = $_REQUEST[$nonce_field] ?? '';
        
        if (!self::verifyNonce($nonce, $action)) {
            wp_die('安全验证失败', 'Security Error', ['response' => 403]);
        }
    }
    
    public static function addNonceField(string $action, string $field_name = '_yinghe_nonce'): string {
        $nonce = self::createNonce($action);
        return sprintf('<input type="hidden" name="%s" value="%s">', 
                      esc_attr($field_name), 
                      esc_attr($nonce));
    }
}

// 使用示例
class YingheSiteAjaxHandler {
    public function __construct() {
        add_action('wp_ajax_save_site', [$this, 'handleSaveSite']);
        add_action('wp_ajax_nopriv_get_sites', [$this, 'handleGetSites']);
    }
    
    public function handleSaveSite(): void {
        // 验证权限
        if (!current_user_can('edit_posts')) {
            wp_die('权限不足');
        }
        
        // 验证 nonce
        YingheSecurityManager::requireNonce('save_site');
        
        // 验证和清理数据
        $validation = YingheInputValidator::validateSiteData($_POST);
        
        if (!$validation['is_valid']) {
            wp_send_json_error($validation['errors']);
        }
        
        // 保存数据
        $site_id = $this->saveSite($validation['data']);
        
        wp_send_json_success(['site_id' => $site_id]);
    }
}
```

#### 3. SQL 注入防护
```php
class YingheSiteRepository {
    public function findByCategory(string $category_slug, int $page = 1, int $per_page = 10): array {
        global $wpdb;
        
        $offset = ($page - 1) * $per_page;
        
        // 使用 prepare 方法防止 SQL 注入
        $sql = $wpdb->prepare("
            SELECT p.ID, p.post_title, p.post_content, p.post_excerpt,
                   pm1.meta_value as site_url,
                   pm2.meta_value as visit_count,
                   t.name as category_name
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
            INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            INNER JOIN {$wpdb->terms} t ON tt.term_id = t.term_id
            LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = 'site_url'
            LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = 'visit_count'
            WHERE p.post_type = 'site'
              AND p.post_status = 'publish'
              AND tt.taxonomy = 'site_category'
              AND t.slug = %s
            ORDER BY p.menu_order ASC, p.post_date DESC
            LIMIT %d OFFSET %d
        ", $category_slug, $per_page, $offset);
        
        return $wpdb->get_results($sql, ARRAY_A);
    }
    
    public function searchSites(string $keyword, array $categories = []): array {
        global $wpdb;
        
        $like = '%' . $wpdb->esc_like($keyword) . '%';
        
        $sql = "
            SELECT p.ID, p.post_title
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'site'
              AND p.post_status = 'publish'
              AND (p.post_title LIKE %s OR p.post_content LIKE %s)
        ";
        
        $params = [$like, $like];
        
        if (!empty($categories)) {
            $placeholders = implode(',', array_fill(0, count($categories), '%s'));
            $sql .= " AND p.ID IN (
                SELECT tr.object_id
                FROM {$wpdb->term_relationships} tr
                INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
                INNER JOIN {$wpdb->terms} t ON tt.term_id = t.term_id
                WHERE tt.taxonomy = 'site_category'
                  AND t.slug IN ({$placeholders})
            )";
            
            $params = array_merge($params, $categories);
        }
        
        $sql .= " ORDER BY p.post_title ASC LIMIT 20";
        
        return $wpdb->get_results($wpdb->prepare($sql, $params), ARRAY_A);
    }
}
```

---

## 🧪 测试指南

### 单元测试

#### 1. PHPUnit 配置
```xml
<!-- phpunit.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="tests/bootstrap.php"
         backupGlobals="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true">
    
    <testsuites>
        <testsuite name="Yinghe Theme Test Suite">
            <directory>tests/unit</directory>
            <directory>tests/integration</directory>
        </testsuite>
    </testsuites>
    
    <filter>
        <whitelist>
            <directory suffix=".php">core/</directory>
            <directory suffix=".php">components/</directory>
            <exclude>
                <directory>vendor/</directory>
                <directory>tests/</directory>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
```

#### 2. 组件测试示例
```php
<?php
// tests/unit/components/test-website-card-component.php

class TestWebsiteCardComponent extends WP_UnitTestCase {
    
    public function setUp(): void {
        parent::setUp();
        
        // 创建测试数据
        $this->site_id = $this->factory->post->create([
            'post_type' => 'site',
            'post_title' => '测试网站',
            'post_content' => '这是一个测试网站的描述'
        ]);
        
        update_post_meta($this->site_id, 'site_url', 'https://example.com');
        update_post_meta($this->site_id, 'visit_count', 100);
    }
    
    public function test_component_renders_basic_card(): void {
        $component = new YingheWebsiteCardComponent([
            'site_id' => $this->site_id,
            'title' => '测试网站',
            'url' => 'https://example.com'
        ]);
        
        $html = $component->render();
        
        // 验证基本结构
        $this->assertStringContainsString('yinghe-website-card', $html);
        $this->assertStringContainsString('测试网站', $html);
        $this->assertStringContainsString('https://example.com', $html);
    }
    
    public function test_component_handles_missing_required_props(): void {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('组件 website-card 缺少必需属性: title');
        
        new YingheWebsiteCardComponent([
            'url' => 'https://example.com'
            // 缺少 title
        ]);
    }
    
    public function test_component_sanitizes_props(): void {
        $component = new YingheWebsiteCardComponent([
            'title' => '<script>alert("xss")</script>安全测试',
            'url' => 'javascript:alert("xss")',
            'description' => '<p>正常内容</p><script>alert("xss")</script>'
        ]);
        
        $html = $component->render();
        
        // 验证 XSS 防护
        $this->assertStringNotContainsString('<script>', $html);
        $this->assertStringNotContainsString('javascript:', $html);
        $this->assertStringContainsString('安全测试', $html);
        $this->assertStringContainsString('<p>正常内容</p>', $html);
    }
    
    public function test_component_caching(): void {
        $component = new class(['title' => 'Test']) extends YingheWebsiteCardComponent {
            use YingheCacheableComponent;
        };
        
        // 第一次渲染
        $html1 = $component->getCachedRender();
        
        // 第二次渲染（应该从缓存获取）
        $html2 = $component->getCachedRender();
        
        $this->assertEquals($html1, $html2);
        
        // 验证缓存键生成
        $this->assertNotEmpty($component->getCacheKey());
    }
    
    public function tearDown(): void {
        wp_delete_post($this->site_id, true);
        parent::tearDown();
    }
}
```

### 集成测试

#### 1. 页面渲染测试
```php
<?php
// tests/integration/test-page-rendering.php

class TestPageRendering extends WP_UnitTestCase {
    
    public function test_homepage_renders_correctly(): void {
        // 创建测试分类和网站
        $category_id = $this->factory->term->create([
            'taxonomy' => 'site_category',
            'name' => '工具类'
        ]);
        
        $sites = [];
        for ($i = 0; $i < 5; $i++) {
            $site_id = $this->factory->post->create([
                'post_type' => 'site',
                'post_title' => "测试网站 {$i}",
            ]);
            
            wp_set_post_terms($site_id, [$category_id], 'site_category');
            $sites[] = $site_id;
        }
        
        // 访问首页
        $this->go_to(home_url('/'));
        
        // 获取页面内容
        ob_start();
        load_template(get_home_template());
        $content = ob_get_clean();
        
        // 验证页面结构
        $this->assertStringContainsString('yinghe-sidebar-nav', $content);
        $this->assertStringContainsString('yinghe-header-system', $content);
        $this->assertStringContainsString('工具类', $content);
        
        // 验证网站卡片
        foreach ($sites as $site_id) {
            $title = get_the_title($site_id);
            $this->assertStringContainsString($title, $content);
        }
    }
    
    public function test_search_functionality(): void {
        // 创建测试网站
        $site_id = $this->factory->post->create([
            'post_type' => 'site',
            'post_title' => 'WordPress 开发工具',
            'post_content' => '专业的 WordPress 开发和调试工具'
        ]);
        
        // 模拟搜索请求
        $_GET['s'] = 'WordPress';
        $this->go_to(home_url('/?s=WordPress'));
        
        // 执行搜索
        $query = new WP_Query([
            'post_type' => 'site',
            's' => 'WordPress'
        ]);
        
        $this->assertTrue($query->have_posts());
        $this->assertEquals(1, $query->post_count);
        $this->assertEquals($site_id, $query->posts[0]->ID);
    }
}
```

### JavaScript 测试

#### 1. Jest 配置
```javascript
// jest.config.js
module.exports = {
    testEnvironment: 'jsdom',
    setupFilesAfterEnv: ['<rootDir>/tests/js/setup.js'],
    testMatch: ['<rootDir>/tests/js/**/*.test.js'],
    collectCoverageFrom: [
        'assets/src/js/**/*.js',
        '!assets/src/js/**/*.config.js'
    ],
    coverageDirectory: 'tests/coverage',
    moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/assets/src/js/$1'
    }
};
```

#### 2. 组件 JavaScript 测试
```javascript
// tests/js/components/search-interface.test.js
import { SearchInterface } from '@/components/search/search-interface.js';

describe('SearchInterface', () => {
    let container;
    let searchInterface;
    
    beforeEach(() => {
        // 设置DOM
        document.body.innerHTML = `
            <div class="search-interface">
                <input type="text" class="search-input" placeholder="搜索...">
                <button class="search-button">搜索</button>
                <div class="search-results"></div>
            </div>
        `;
        
        container = document.querySelector('.search-interface');
        searchInterface = new SearchInterface(container);
    });
    
    afterEach(() => {
        document.body.innerHTML = '';
    });
    
    test('初始化组件', () => {
        expect(searchInterface.element).toBe(container);
        expect(searchInterface.options.placeholder).toBe('输入搜索关键词');
    });
    
    test('处理输入事件', (done) => {
        const input = container.querySelector('.search-input');
        
        // 监听搜索事件
        searchInterface.on('search', (data) => {
            expect(data.query).toBe('test query');
            done();
        });
        
        // 模拟用户输入
        input.value = 'test query';
        input.dispatchEvent(new Event('input'));
    });
    
    test('防抖功能', (done) => {
        const input = container.querySelector('.search-input');
        let searchCount = 0;
        
        searchInterface.on('search', () => {
            searchCount++;
        });
        
        // 快速输入多次
        input.value = 't';
        input.dispatchEvent(new Event('input'));
        
        input.value = 'te';
        input.dispatchEvent(new Event('input'));
        
        input.value = 'test';
        input.dispatchEvent(new Event('input'));
        
        // 等待防抖时间
        setTimeout(() => {
            expect(searchCount).toBe(1); // 只触发一次搜索
            done();
        }, 350);
    });
});
```

### E2E 测试

#### 1. Playwright 配置
```javascript
// playwright.config.js
const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
    testDir: './tests/e2e',
    fullyParallel: true,
    forbidOnly: !!process.env.CI,
    retries: process.env.CI ? 2 : 0,
    workers: process.env.CI ? 1 : undefined,
    reporter: 'html',
    use: {
        baseURL: 'http://localhost:8000',
        trace: 'on-first-retry',
    },
    
    projects: [
        {
            name: 'chromium',
            use: { ...devices['Desktop Chrome'] },
        },
        {
            name: 'firefox',
            use: { ...devices['Desktop Firefox'] },
        },
        {
            name: 'webkit',
            use: { ...devices['Desktop Safari'] },
        },
        {
            name: 'Mobile Chrome',
            use: { ...devices['Pixel 5'] },
        },
    ],
});
```

#### 2. E2E 测试示例
```javascript
// tests/e2e/homepage.spec.js
const { test, expect } = require('@playwright/test');

test.describe('首页功能', () => {
    
    test('页面正常加载', async ({ page }) => {
        await page.goto('/');
        
        // 验证页面标题
        await expect(page).toHaveTitle(/硬核导航/);
        
        // 验证主要组件存在
        await expect(page.locator('.yinghe-sidebar-nav')).toBeVisible();
        await expect(page.locator('.yinghe-header-system')).toBeVisible();
        await expect(page.locator('.yinghe-main-content')).toBeVisible();
    });
    
    test('侧边栏导航功能', async ({ page }) => {
        await page.goto('/');
        
        // 点击分类菜单
        const categoryLink = page.locator('.sidebar-item a').first();
        await categoryLink.click();
        
        // 验证页面滚动到对应区域
        const targetSection = page.locator('[id^="term-"]').first();
        await expect(targetSection).toBeInViewport();
        
        // 验证激活状态
        await expect(categoryLink.locator('..')).toHaveClass(/active/);
    });
    
    test('搜索功能', async ({ page }) => {
        await page.goto('/');
        
        // 在搜索框输入关键词
        const searchInput = page.locator('#search-text');
        await searchInput.fill('WordPress');
        
        // 选择搜索引擎
        const searchEngine = page.locator('input[name="type"]').first();
        await searchEngine.check();
        
        // 提交搜索（会打开新页面）
        const [newPage] = await Promise.all([
            page.waitForEvent('popup'),
            page.locator('.search-submit').click()
        ]);
        
        // 验证新页面URL包含搜索参数
        expect(newPage.url()).toContain('WordPress');
    });
    
    test('主题切换功能', async ({ page }) => {
        await page.goto('/');
        
        // 点击主题切换按钮
        await page.locator('.theme-switcher').click();
        
        // 验证暗色主题应用
        await expect(page.locator('html')).toHaveAttribute('data-theme', 'dark');
        
        // 验证颜色变化
        const backgroundColor = await page.locator('body').evaluate(
            el => getComputedStyle(el).backgroundColor
        );
        expect(backgroundColor).not.toBe('rgb(255, 255, 255)'); // 不是白色
    });
    
    test('响应式设计', async ({ page }) => {
        await page.goto('/');
        
        // 切换到移动端视口
        await page.setViewportSize({ width: 375, height: 667 });
        
        // 验证侧边栏在移动端是隐藏的
        await expect(page.locator('.yinghe-sidebar-nav')).not.toBeVisible();
        
        // 点击菜单按钮显示侧边栏
        await page.locator('#sidebar-switch').click();
        await expect(page.locator('.yinghe-sidebar-nav')).toBeVisible();
        
        // 点击遮罩关闭侧边栏
        await page.locator('.sidebar-overlay').click();
        await expect(page.locator('.yinghe-sidebar-nav')).not.toBeVisible();
    });
    
    test('网站卡片交互', async ({ page }) => {
        await page.goto('/');
        
        // 悬停在网站卡片上
        const firstCard = page.locator('.yinghe-website-card').first();
        await firstCard.hover();
        
        // 验证悬停效果
        const transform = await firstCard.evaluate(
            el => getComputedStyle(el).transform
        );
        expect(transform).not.toBe('none');
        
        // 点击卡片（会打开新页面）
        const cardLink = firstCard.locator('a').first();
        const [newPage] = await Promise.all([
            page.waitForEvent('popup'),
            cardLink.click()
        ]);
        
        // 验证新页面正常打开
        await expect(newPage).toHaveURL(/^https?:\/\//);
    });
});
```

---

## 🐛 调试技巧

### PHP 调试

#### 1. 调试配置
```php
// wp-config.php 调试配置
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);
define('SAVEQUERIES', true);

// 自定义调试函数
if (!function_exists('yinghe_debug')) {
    function yinghe_debug($data, $label = 'DEBUG', $die = false) {
        if (!WP_DEBUG) {
            return;
        }
        
        $output = sprintf(
            "[%s] %s: %s\n",
            date('Y-m-d H:i:s'),
            $label,
            is_array($data) || is_object($data) ? print_r($data, true) : $data
        );
        
        error_log($output);
        
        if (WP_DEBUG_DISPLAY || $die) {
            echo '<pre>' . esc_html($output) . '</pre>';
        }
        
        if ($die) {
            die();
        }
    }
}
```

#### 2. 性能调试
```php
class YinghePerformanceDebugger {
    private static array $timers = [];
    private static array $memory_usage = [];
    
    public static function startTimer(string $name): void {
        self::$timers[$name] = [
            'start' => microtime(true),
            'memory_start' => memory_get_usage()
        ];
    }
    
    public static function endTimer(string $name): array {
        if (!isset(self::$timers[$name])) {
            return [];
        }
        
        $timer = self::$timers[$name];
        $end_time = microtime(true);
        $end_memory = memory_get_usage();
        
        $result = [
            'duration' => round(($end_time - $timer['start']) * 1000, 2), // ms
            'memory_used' => $end_memory - $timer['memory_start'],
            'memory_peak' => memory_get_peak_usage() - $timer['memory_start']
        ];
        
        yinghe_debug($result, "TIMER: {$name}");
        
        return $result;
    }
    
    public static function memoryCheckpoint(string $name): void {
        self::$memory_usage[$name] = [
            'current' => memory_get_usage(),
            'peak' => memory_get_peak_usage()
        ];
    }
    
    public static function getMemoryReport(): array {
        return self::$memory_usage;
    }
}

// 使用示例
YinghePerformanceDebugger::startTimer('component_render');
$component = new YingheWebsiteCardComponent($props);
$html = $component->render();
YinghePerformanceDebugger::endTimer('component_render');
```

#### 3. 查询调试
```php
class YingheQueryDebugger {
    public static function logQueries(): void {
        if (!defined('SAVEQUERIES') || !SAVEQUERIES) {
            return;
        }
        
        global $wpdb;
        
        $total_time = 0;
        $slow_queries = [];
        
        foreach ($wpdb->queries as $query) {
            $total_time += $query[1];
            
            // 记录慢查询（超过0.1秒）
            if ($query[1] > 0.1) {
                $slow_queries[] = [
                    'query' => $query[0],
                    'time' => $query[1],
                    'stack' => $query[2]
                ];
            }
        }
        
        yinghe_debug([
            'total_queries' => count($wpdb->queries),
            'total_time' => round($total_time, 4),
            'slow_queries_count' => count($slow_queries),
            'slow_queries' => $slow_queries
        ], 'QUERY_DEBUG');
    }
}

// 在页面底部添加查询调试
add_action('wp_footer', [YingheQueryDebugger::class, 'logQueries'], 999);
```

### JavaScript 调试

#### 1. 调试工具
```javascript
// 全局调试对象
window.YingheDebug = {
    // 组件调试
    components: new Map(),
    
    // 注册组件
    registerComponent(name, instance) {
        this.components.set(name, instance);
        console.log(`🧩 组件注册: ${name}`, instance);
    },
    
    // 获取组件
    getComponent(name) {
        return this.components.get(name);
    },
    
    // 列出所有组件
    listComponents() {
        console.table([...this.components.entries()]);
    },
    
    // 性能监控
    performance: {
        marks: new Map(),
        
        mark(name) {
            const timestamp = performance.now();
            this.marks.set(name, timestamp);
            console.log(`⏱️ 性能标记: ${name} - ${timestamp.toFixed(2)}ms`);
        },
        
        measure(name, startMark, endMark = null) {
            const start = this.marks.get(startMark);
            const end = endMark ? this.marks.get(endMark) : performance.now();
            
            if (start && end) {
                const duration = end - start;
                console.log(`📊 性能测量: ${name} - ${duration.toFixed(2)}ms`);
                return duration;
            }
        }
    },
    
    // 事件调试
    events: {
        listeners: new Map(),
        
        on(element, event, handler, label = '') {
            element.addEventListener(event, handler);
            
            const key = `${element.tagName}#${element.id || 'no-id'}.${event}`;
            if (!this.listeners.has(key)) {
                this.listeners.set(key, []);
            }
            
            this.listeners.get(key).push({
                handler,
                label,
                timestamp: new Date().toISOString()
            });
            
            console.log(`🎯 事件监听器添加: ${key} ${label ? `(${label})` : ''}`);
        },
        
        listListeners() {
            console.table([...this.listeners.entries()]);
        }
    }
};

// 开发环境下的全局错误处理
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    window.addEventListener('error', (event) => {
        console.error('🚨 JavaScript 错误:', {
            message: event.error.message,
            filename: event.filename,
            line: event.lineno,
            column: event.colno,
            stack: event.error.stack
        });
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.error('🚨 未处理的 Promise 拒绝:', event.reason);
    });
}
```

#### 2. 组件调试
```javascript
// 调试版本的组件基类
class DebugComponent {
    constructor(element, options = {}) {
        this.element = element;
        this.options = options;
        this.debugName = this.constructor.name;
        
        // 注册到全局调试对象
        YingheDebug.registerComponent(this.debugName, this);
        
        // 添加调试属性
        this.element.dataset.debugComponent = this.debugName;
        
        this.debug('组件初始化', { element, options });
        
        this.init();
    }
    
    debug(message, data = null) {
        if (console.group) {
            console.group(`🧩 ${this.debugName}: ${message}`);
            if (data) {
                console.log('数据:', data);
            }
            console.log('元素:', this.element);
            console.trace('调用栈');
            console.groupEnd();
        } else {
            console.log(`🧩 ${this.debugName}: ${message}`, data);
        }
    }
    
    measurePerformance(name, fn) {
        const startMark = `${this.debugName}-${name}-start`;
        const endMark = `${this.debugName}-${name}-end`;
        
        YingheDebug.performance.mark(startMark);
        const result = fn();
        YingheDebug.performance.mark(endMark);
        YingheDebug.performance.measure(name, startMark, endMark);
        
        return result;
    }
}

// 使用示例
class SearchInterface extends DebugComponent {
    init() {
        this.debug('开始初始化搜索接口');
        
        this.measurePerformance('绑定事件', () => {
            this.bindEvents();
        });
        
        this.debug('搜索接口初始化完成');
    }
    
    handleSearch(query) {
        this.debug('执行搜索', { query });
        
        this.measurePerformance('搜索执行', () => {
            // 搜索逻辑
        });
    }
}
```

---

## 📦 版本控制

### Git 工作流

#### 1. 分支策略
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/sidebar-navigation      # 侧边栏导航功能
feature/search-interface       # 搜索界面功能
feature/theme-switcher         # 主题切换功能

# 修复分支
hotfix/security-fix            # 紧急安全修复
bugfix/card-display-issue      # 卡片显示问题修复

# 发布分支
release/v1.0.0                 # 版本发布准备
```

#### 2. 提交规范
```bash
# 提交类型
feat:     # 新功能
fix:      # 修复bug
docs:     # 文档更新
style:    # 代码格式化
refactor: # 重构
test:     # 测试相关
chore:    # 构建工具或辅助工具的变动

# 提交格式
<type>(<scope>): <subject>

<body>

<footer>

# 示例
feat(components): 添加网站卡片组件

- 实现基本的卡片布局
- 支持多种显示模式
- 添加悬停交互效果
- 集成缓存机制

Closes #123
```

#### 3. Git Hooks
```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "🔍 运行代码检查..."

# PHP 语法检查
find . -name "*.php" -not -path "./vendor/*" -exec php -l {} \; | grep -v "No syntax errors"
if [ $? -eq 0 ]; then
    echo "❌ PHP 语法错误"
    exit 1
fi

# PHP 代码规范检查
./vendor/bin/phpcs --standard=WordPress --ignore=vendor/,node_modules/ .
if [ $? -ne 0 ]; then
    echo "❌ PHP 代码规范检查失败"
    exit 1
fi

# JavaScript 检查
npm run lint:js
if [ $? -ne 0 ]; then
    echo "❌ JavaScript 检查失败"
    exit 1
fi

# SCSS 检查
npm run lint:scss
if [ $? -ne 0 ]; then
    echo "❌ SCSS 检查失败"
    exit 1
fi

echo "✅ 所有检查通过"
```

#### 4. 版本标记
```bash
# package.json 版本管理
{
  "name": "yinhedaohang-theme",
  "version": "1.0.0",
  "scripts": {
    "version:patch": "npm version patch && git push --tags",
    "version:minor": "npm version minor && git push --tags",
    "version:major": "npm version major && git push --tags"
  }
}

# 自动生成 CHANGELOG
npm install -g conventional-changelog-cli
conventional-changelog -p angular -i CHANGELOG.md -s
```

---

## 👥 团队协作

### 代码审查

#### 1. Pull Request 模板
```markdown
<!-- .github/pull_request_template.md -->

## 📝 变更说明

### 变更类型
- [ ] 新功能
- [ ] Bug 修复
- [ ] 文档更新
- [ ] 重构
- [ ] 性能优化
- [ ] 其他

### 变更内容
简要描述本次变更的内容和目的。

### 影响范围
- [ ] 前端组件
- [ ] 后端逻辑
- [ ] 数据库结构
- [ ] API 接口
- [ ] 配置文件

### 测试情况
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成
- [ ] 浏览器兼容性测试

### 截图/演示
如果是 UI 相关变更，请提供截图或 GIF 演示。

### 相关 Issue
Closes #(issue number)

### 检查清单
- [ ] 代码遵循项目规范
- [ ] 已添加/更新相关文档
- [ ] 已添加/更新测试用例
- [ ] 已测试在不同环境下的兼容性
- [ ] 无性能回归
- [ ] 无安全隐患

### 备注
其他需要说明的内容。
```

#### 2. 代码审查检查清单
```markdown
## 代码审查检查清单

### 📋 通用检查
- [ ] 代码逻辑清晰，易于理解
- [ ] 变量和函数命名有意义
- [ ] 代码复用性良好
- [ ] 无重复代码
- [ ] 错误处理完善
- [ ] 性能考虑合理

### 🔒 安全检查
- [ ] 用户输入已验证和清理
- [ ] SQL 查询使用预处理语句
- [ ] XSS 防护到位
- [ ] CSRF 保护完善
- [ ] 权限检查正确

### 🚀 性能检查
- [ ] 数据库查询优化
- [ ] 缓存策略合理
- [ ] 资源加载优化
- [ ] 内存使用合理
- [ ] 无性能瓶颈

### 🧪 测试检查
- [ ] 测试覆盖率足够
- [ ] 测试用例有意义
- [ ] 边界条件测试
- [ ] 错误场景测试
- [ ] 集成测试完善

### 📱 兼容性检查
- [ ] 浏览器兼容性
- [ ] 响应式设计
- [ ] 移动端适配
- [ ] WordPress 版本兼容
- [ ] PHP 版本兼容
```

### 文档维护

#### 1. 文档结构
```
docs/
├── README.md                   # 项目总览
├── CONTRIBUTING.md             # 贡献指南
├── CHANGELOG.md                # 变更日志
├── API.md                      # API 文档
├── DEPLOYMENT.md               # 部署文档
├── development/                # 开发文档
│   ├── getting-started.md      # 快速开始
│   ├── coding-standards.md     # 编码规范
│   ├── testing.md              # 测试指南
│   └── debugging.md            # 调试指南
├── user-guide/                 # 用户指南
│   ├── installation.md         # 安装说明
│   ├── configuration.md        # 配置说明
│   └── customization.md        # 自定义指南
└── api-reference/              # API 参考
    ├── components.md           # 组件 API
    ├── hooks.md                # 钩子 API
    └── functions.md            # 函数 API
```

#### 2. 文档自动化
```yaml
# .github/workflows/docs.yml
name: 文档自动化

on:
  push:
    branches: [main]
    paths: ['docs/**']

jobs:
  deploy-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: 设置 Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: 安装依赖
        run: npm install
        
      - name: 构建文档
        run: npm run docs:build
        
      - name: 部署到 GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/.vuepress/dist
```

### 持续集成

#### 1. GitHub Actions 配置
```yaml
# .github/workflows/ci.yml
name: 持续集成

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version: [8.0, 8.1, 8.2]
        wordpress-version: [6.0, 6.1, 6.2]
    
    steps:
      - uses: actions/checkout@v2
      
      - name: 设置 PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: mysqli, zip, gd
          
      - name: 缓存 Composer 依赖
        uses: actions/cache@v2
        with:
          path: vendor
          key: composer-${{ hashFiles('composer.lock') }}
          
      - name: 安装 PHP 依赖
        run: composer install --prefer-dist --no-progress
        
      - name: 设置 WordPress 测试环境
        run: |
          bash bin/install-wp-tests.sh wordpress_test root '' localhost ${{ matrix.wordpress-version }}
          
      - name: 运行 PHP 测试
        run: ./vendor/bin/phpunit
        
      - name: 代码覆盖率报告
        run: ./vendor/bin/phpunit --coverage-clover=coverage.xml
        
      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v1
        with:
          file: ./coverage.xml

  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v2
      
      - name: 设置 Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          cache: 'npm'
          
      - name: 安装依赖
        run: npm ci
        
      - name: 运行 ESLint
        run: npm run lint:js
        
      - name: 运行 Stylelint
        run: npm run lint:scss
        
      - name: 运行 Jest 测试
        run: npm run test:unit
        
      - name: 构建资源
        run: npm run build
        
      - name: 运行 E2E 测试
        run: npm run test:e2e
```

这个开发指南涵盖了从环境搭建到团队协作的所有关键环节，为硬核导航主题的开发提供了完整的最佳实践指导。