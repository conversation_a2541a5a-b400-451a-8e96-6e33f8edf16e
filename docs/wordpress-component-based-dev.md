# WordPress Component-Based Development Rules

## 核心理念
在 WordPress 中实现类似 Vue 单文件组件的开发模式，每个 PHP 文件都是一个完整的、可复用的 UI 组件。

## 组件结构规范

### 基础组件模板
```php
<?php
/**
 * Component: ComponentName
 * Description: Brief description of the component
 * Props: prop1 (string), prop2 (array), prop3 (bool)
 */

// Component Logic (PHP)
class ComponentName {
    private $props;
    private $state;
    
    public function __construct($props = []) {
        $this->props = wp_parse_args($props, $this->getDefaultProps());
        $this->state = $this->initState();
        $this->init();
    }
    
    private function getDefaultProps() {
        return [
            'class' => '',
            'id' => '',
            'data' => []
        ];
    }
    
    private function initState() {
        return [
            'isActive' => false,
            'items' => []
        ];
    }
    
    private function init() {
        // Component initialization logic
        add_action('wp_enqueue_scripts', [$this, 'enqueueAssets']);
    }
    
    public function enqueueAssets() {
        // Enqueue component-specific styles and scripts
        wp_add_inline_style('theme-style', $this->getStyles());
        wp_add_inline_script('theme-script', $this->getScripts());
    }
    
    public function render() {
        ob_start();
        $this->template();
        return ob_get_clean();
    }
    
    // Component Template (HTML)
    private function template() {
        ?>
        <div class="component-name <?php echo esc_attr($this->props['class']); ?>" 
             id="<?php echo esc_attr($this->props['id']); ?>">
            <!-- Component HTML structure -->
            <h2><?php echo esc_html($this->props['title'] ?? ''); ?></h2>
            <div class="component-content">
                <?php $this->renderContent(); ?>
            </div>
        </div>
        <?php
    }
    
    private function renderContent() {
        // Dynamic content rendering
        foreach ($this->props['data'] as $item) {
            echo '<div class="item">' . esc_html($item) . '</div>';
        }
    }
    
    // Component Styles (CSS)
    private function getStyles() {
        return "
            .component-name {
                padding: 20px;
                background: #f5f5f5;
                border-radius: 8px;
            }
            .component-name h2 {
                margin: 0 0 15px;
                color: #333;
            }
            .component-content {
                display: grid;
                gap: 10px;
            }
        ";
    }
    
    // Component Scripts (JS)
    private function getScripts() {
        return "
            (function() {
                document.querySelectorAll('.component-name').forEach(function(component) {
                    // Component-specific JavaScript
                    component.addEventListener('click', function(e) {
                        console.log('Component clicked');
                    });
                });
            })();
        ";
    }
}

// Helper function for easy usage
function render_component($props = []) {
    $component = new ComponentName($props);
    echo $component->render();
}

// Static render method
function get_component($props = []) {
    $component = new ComponentName($props);
    return $component->render();
}
?>
```

## 实际组件示例

### 1. 文章卡片组件 (components/post-card.php)
```php
<?php
/**
 * Component: PostCard
 * Description: Displays a single post as a card
 * Props: post_id, show_excerpt, show_author, show_date
 */

class PostCard {
    private $props;
    private $post;
    
    public function __construct($props = []) {
        $this->props = wp_parse_args($props, [
            'post_id' => 0,
            'show_excerpt' => true,
            'show_author' => true,
            'show_date' => true,
            'excerpt_length' => 20,
            'class' => ''
        ]);
        
        $this->post = get_post($this->props['post_id']);
    }
    
    public function render() {
        if (!$this->post) {
            return '';
        }
        
        ob_start();
        ?>
        <article class="post-card <?php echo esc_attr($this->props['class']); ?>" 
                 data-post-id="<?php echo esc_attr($this->post->ID); ?>">
            
            <?php if (has_post_thumbnail($this->post->ID)) : ?>
                <div class="post-card__thumbnail">
                    <?php echo get_the_post_thumbnail($this->post->ID, 'medium'); ?>
                </div>
            <?php endif; ?>
            
            <div class="post-card__content">
                <h3 class="post-card__title">
                    <a href="<?php echo get_permalink($this->post->ID); ?>">
                        <?php echo esc_html($this->post->post_title); ?>
                    </a>
                </h3>
                
                <?php if ($this->props['show_excerpt']) : ?>
                    <div class="post-card__excerpt">
                        <?php echo wp_trim_words($this->post->post_content, $this->props['excerpt_length']); ?>
                    </div>
                <?php endif; ?>
                
                <div class="post-card__meta">
                    <?php if ($this->props['show_author']) : ?>
                        <span class="post-card__author">
                            <?php echo get_the_author_meta('display_name', $this->post->post_author); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($this->props['show_date']) : ?>
                        <time class="post-card__date" datetime="<?php echo get_the_date('c', $this->post->ID); ?>">
                            <?php echo get_the_date('', $this->post->ID); ?>
                        </time>
                    <?php endif; ?>
                </div>
            </div>
            
            <style>
                .post-card {
                    display: flex;
                    gap: 20px;
                    padding: 20px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    transition: transform 0.2s;
                }
                .post-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                }
                .post-card__thumbnail {
                    flex-shrink: 0;
                    width: 120px;
                    height: 120px;
                    overflow: hidden;
                    border-radius: 4px;
                }
                .post-card__thumbnail img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                .post-card__content {
                    flex: 1;
                }
                .post-card__title {
                    margin: 0 0 10px;
                    font-size: 1.25rem;
                }
                .post-card__title a {
                    text-decoration: none;
                    color: #333;
                }
                .post-card__excerpt {
                    margin: 0 0 15px;
                    color: #666;
                    line-height: 1.6;
                }
                .post-card__meta {
                    display: flex;
                    gap: 15px;
                    font-size: 0.875rem;
                    color: #999;
                }
            </style>
        </article>
        <?php
        return ob_get_clean();
    }
}

// Helper functions
function render_post_card($props = []) {
    $component = new PostCard($props);
    echo $component->render();
}

function get_post_card($props = []) {
    $component = new PostCard($props);
    return $component->render();
}
?>
```

### 2. 导航菜单组件 (components/nav-menu.php)
```php
<?php
/**
 * Component: NavMenu
 * Description: Responsive navigation menu component
 * Props: menu_id, theme_location, container_class, mobile_breakpoint
 */

class NavMenu {
    private $props;
    private static $instance_count = 0;
    private $instance_id;
    
    public function __construct($props = []) {
        self::$instance_count++;
        $this->instance_id = 'nav-menu-' . self::$instance_count;
        
        $this->props = wp_parse_args($props, [
            'menu_id' => '',
            'theme_location' => 'primary',
            'container_class' => '',
            'mobile_breakpoint' => '768px',
            'show_mobile_toggle' => true
        ]);
        
        add_action('wp_footer', [$this, 'printScripts']);
    }
    
    public function render() {
        ob_start();
        ?>
        <nav class="nav-menu <?php echo esc_attr($this->props['container_class']); ?>" 
             id="<?php echo esc_attr($this->instance_id); ?>">
            
            <?php if ($this->props['show_mobile_toggle']) : ?>
                <button class="nav-menu__toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            <?php endif; ?>
            
            <?php
            wp_nav_menu([
                'theme_location' => $this->props['theme_location'],
                'menu_id' => $this->props['menu_id'],
                'container' => 'div',
                'container_class' => 'nav-menu__wrapper',
                'menu_class' => 'nav-menu__list',
                'fallback_cb' => false,
                'depth' => 2,
                'walker' => new Custom_Nav_Walker()
            ]);
            ?>
            
            <style>
                #<?php echo $this->instance_id; ?> {
                    position: relative;
                }
                
                #<?php echo $this->instance_id; ?> .nav-menu__toggle {
                    display: none;
                    background: none;
                    border: none;
                    cursor: pointer;
                    padding: 10px;
                }
                
                #<?php echo $this->instance_id; ?> .nav-menu__toggle span {
                    display: block;
                    width: 25px;
                    height: 3px;
                    background: #333;
                    margin: 5px 0;
                    transition: 0.3s;
                }
                
                #<?php echo $this->instance_id; ?> .nav-menu__list {
                    list-style: none;
                    margin: 0;
                    padding: 0;
                    display: flex;
                    gap: 30px;
                }
                
                #<?php echo $this->instance_id; ?> .nav-menu__list a {
                    text-decoration: none;
                    color: #333;
                    padding: 5px 0;
                    border-bottom: 2px solid transparent;
                    transition: 0.3s;
                }
                
                #<?php echo $this->instance_id; ?> .nav-menu__list a:hover,
                #<?php echo $this->instance_id; ?> .nav-menu__list .current-menu-item > a {
                    border-color: #0073aa;
                }
                
                @media (max-width: <?php echo $this->props['mobile_breakpoint']; ?>) {
                    #<?php echo $this->instance_id; ?> .nav-menu__toggle {
                        display: block;
                    }
                    
                    #<?php echo $this->instance_id; ?> .nav-menu__wrapper {
                        position: absolute;
                        top: 100%;
                        left: 0;
                        right: 0;
                        background: white;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        max-height: 0;
                        overflow: hidden;
                        transition: max-height 0.3s;
                    }
                    
                    #<?php echo $this->instance_id; ?> .nav-menu__wrapper.is-open {
                        max-height: 500px;
                    }
                    
                    #<?php echo $this->instance_id; ?> .nav-menu__list {
                        flex-direction: column;
                        gap: 0;
                        padding: 20px;
                    }
                    
                    #<?php echo $this->instance_id; ?> .nav-menu__list li {
                        border-bottom: 1px solid #eee;
                        padding: 10px 0;
                    }
                }
            </style>
        </nav>
        <?php
        return ob_get_clean();
    }
    
    public function printScripts() {
        ?>
        <script>
        (function() {
            const nav = document.getElementById('<?php echo $this->instance_id; ?>');
            if (!nav) return;
            
            const toggle = nav.querySelector('.nav-menu__toggle');
            const wrapper = nav.querySelector('.nav-menu__wrapper');
            
            if (toggle && wrapper) {
                toggle.addEventListener('click', function() {
                    wrapper.classList.toggle('is-open');
                    toggle.classList.toggle('is-active');
                });
                
                // Close menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!nav.contains(e.target)) {
                        wrapper.classList.remove('is-open');
                        toggle.classList.remove('is-active');
                    }
                });
            }
        })();
        </script>
        <?php
    }
}

// Custom Nav Walker (如果需要更复杂的菜单结构)
class Custom_Nav_Walker extends Walker_Nav_Menu {
    // 自定义菜单输出
}

// Helper functions
function render_nav_menu($props = []) {
    $component = new NavMenu($props);
    echo $component->render();
}
?>
```

## 组件使用方法

### 在模板中使用
```php
// single.php 或其他模板文件
<?php
// 加载组件
require_once get_template_directory() . '/components/post-card.php';
require_once get_template_directory() . '/components/nav-menu.php';

// 使用导航组件
render_nav_menu([
    'theme_location' => 'primary',
    'container_class' => 'main-navigation',
    'mobile_breakpoint' => '992px'
]);

// 使用文章卡片组件
$recent_posts = get_posts(['numberposts' => 5]);
foreach ($recent_posts as $post) {
    render_post_card([
        'post_id' => $post->ID,
        'show_excerpt' => true,
        'excerpt_length' => 30,
        'class' => 'featured-post'
    ]);
}
?>
```

### 组件自动加载器
```php
// functions.php
class ComponentLoader {
    private static $loaded_components = [];
    
    public static function load($component_name) {
        if (isset(self::$loaded_components[$component_name])) {
            return;
        }
        
        $component_file = get_template_directory() . '/components/' . $component_name . '.php';
        
        if (file_exists($component_file)) {
            require_once $component_file;
            self::$loaded_components[$component_name] = true;
        }
    }
    
    public static function autoload() {
        $components_dir = get_template_directory() . '/components/';
        $component_files = glob($components_dir . '*.php');
        
        foreach ($component_files as $file) {
            require_once $file;
        }
    }
}

// 自动加载所有组件
add_action('after_setup_theme', function() {
    ComponentLoader::autoload();
});
```

## 高级组件示例

### 3. 表单组件 (components/contact-form.php)
```php
<?php
/**
 * Component: ContactForm
 * Description: AJAX-powered contact form with validation
 * Props: fields, submit_text, success_message, email_to
 */

class ContactForm {
    private $props;
    private $form_id;
    
    public function __construct($props = []) {
        $this->form_id = 'contact-form-' . wp_generate_uuid4();
        
        $this->props = wp_parse_args($props, [
            'fields' => $this->getDefaultFields(),
            'submit_text' => 'Send Message',
            'success_message' => 'Thank you! Your message has been sent.',
            'email_to' => get_option('admin_email')
        ]);
        
        add_action('wp_ajax_submit_contact_form', [$this, 'handleSubmit']);
        add_action('wp_ajax_nopriv_submit_contact_form', [$this, 'handleSubmit']);
    }
    
    private function getDefaultFields() {
        return [
            ['name' => 'name', 'type' => 'text', 'label' => 'Name', 'required' => true],
            ['name' => 'email', 'type' => 'email', 'label' => 'Email', 'required' => true],
            ['name' => 'message', 'type' => 'textarea', 'label' => 'Message', 'required' => true]
        ];
    }
    
    public function render() {
        ob_start();
        ?>
        <div class="contact-form-component" id="<?php echo esc_attr($this->form_id); ?>">
            <form class="contact-form" method="post">
                <?php wp_nonce_field('contact_form_submit', 'contact_nonce'); ?>
                
                <?php foreach ($this->props['fields'] as $field) : ?>
                    <div class="form-field">
                        <label for="<?php echo esc_attr($field['name']); ?>">
                            <?php echo esc_html($field['label']); ?>
                            <?php if (!empty($field['required'])) : ?>
                                <span class="required">*</span>
                            <?php endif; ?>
                        </label>
                        
                        <?php if ($field['type'] === 'textarea') : ?>
                            <textarea 
                                name="<?php echo esc_attr($field['name']); ?>"
                                id="<?php echo esc_attr($field['name']); ?>"
                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                            ></textarea>
                        <?php else : ?>
                            <input 
                                type="<?php echo esc_attr($field['type']); ?>"
                                name="<?php echo esc_attr($field['name']); ?>"
                                id="<?php echo esc_attr($field['name']); ?>"
                                <?php echo !empty($field['required']) ? 'required' : ''; ?>
                            />
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
                
                <div class="form-actions">
                    <button type="submit" class="submit-button">
                        <?php echo esc_html($this->props['submit_text']); ?>
                    </button>
                </div>
                
                <div class="form-message"></div>
            </form>
            
            <style>
                #<?php echo $this->form_id; ?> {
                    max-width: 600px;
                    margin: 0 auto;
                }
                
                #<?php echo $this->form_id; ?> .contact-form {
                    background: #f9f9f9;
                    padding: 30px;
                    border-radius: 8px;
                }
                
                #<?php echo $this->form_id; ?> .form-field {
                    margin-bottom: 20px;
                }
                
                #<?php echo $this->form_id; ?> label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: 600;
                }
                
                #<?php echo $this->form_id; ?> input,
                #<?php echo $this->form_id; ?> textarea {
                    width: 100%;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 16px;
                }
                
                #<?php echo $this->form_id; ?> textarea {
                    min-height: 120px;
                    resize: vertical;
                }
                
                #<?php echo $this->form_id; ?> .required {
                    color: #e74c3c;
                }
                
                #<?php echo $this->form_id; ?> .submit-button {
                    background: #0073aa;
                    color: white;
                    padding: 12px 30px;
                    border: none;
                    border-radius: 4px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: background 0.3s;
                }
                
                #<?php echo $this->form_id; ?> .submit-button:hover {
                    background: #005a87;
                }
                
                #<?php echo $this->form_id; ?> .submit-button:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
                
                #<?php echo $this->form_id; ?> .form-message {
                    margin-top: 20px;
                    padding: 15px;
                    border-radius: 4px;
                    display: none;
                }
                
                #<?php echo $this->form_id; ?> .form-message.success {
                    background: #d4edda;
                    color: #155724;
                    display: block;
                }
                
                #<?php echo $this->form_id; ?> .form-message.error {
                    background: #f8d7da;
                    color: #721c24;
                    display: block;
                }
            </style>
            
            <script>
            (function() {
                const form = document.querySelector('#<?php echo $this->form_id; ?> .contact-form');
                const submitButton = form.querySelector('.submit-button');
                const messageDiv = form.querySelector('.form-message');
                
                form.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    // Disable submit button
                    submitButton.disabled = true;
                    submitButton.textContent = 'Sending...';
                    
                    // Clear previous messages
                    messageDiv.className = 'form-message';
                    messageDiv.textContent = '';
                    
                    // Prepare form data
                    const formData = new FormData(form);
                    formData.append('action', 'submit_contact_form');
                    
                    try {
                        const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                            method: 'POST',
                            body: formData
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            messageDiv.className = 'form-message success';
                            messageDiv.textContent = '<?php echo esc_js($this->props['success_message']); ?>';
                            form.reset();
                        } else {
                            messageDiv.className = 'form-message error';
                            messageDiv.textContent = result.data || 'An error occurred. Please try again.';
                        }
                    } catch (error) {
                        messageDiv.className = 'form-message error';
                        messageDiv.textContent = 'Network error. Please try again.';
                    } finally {
                        submitButton.disabled = false;
                        submitButton.textContent = '<?php echo esc_js($this->props['submit_text']); ?>';
                    }
                });
            })();
            </script>
        </div>
        <?php
        return ob_get_clean();
    }
    
    public function handleSubmit() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['contact_nonce'], 'contact_form_submit')) {
            wp_send_json_error('Invalid request');
        }
        
        // Validate and sanitize data
        $data = [];
        foreach ($this->props['fields'] as $field) {
            $value = sanitize_text_field($_POST[$field['name']] ?? '');
            
            if (!empty($field['required']) && empty($value)) {
                wp_send_json_error('Please fill in all required fields');
            }
            
            $data[$field['name']] = $value;
        }
        
        // Send email
        $subject = 'New Contact Form Submission';
        $message = $this->formatEmailMessage($data);
        $headers = ['Content-Type: text/html; charset=UTF-8'];
        
        $sent = wp_mail($this->props['email_to'], $subject, $message, $headers);
        
        if ($sent) {
            wp_send_json_success('Message sent successfully');
        } else {
            wp_send_json_error('Failed to send message');
        }
    }
    
    private function formatEmailMessage($data) {
        $message = '<h2>New Contact Form Submission</h2>';
        foreach ($data as $key => $value) {
            $label = ucfirst(str_replace('_', ' ', $key));
            $message .= '<p><strong>' . $label . ':</strong> ' . nl2br(esc_html($value)) . '</p>';
        }
        return $message;
    }
}

// Helper function
function render_contact_form($props = []) {
    $component = new ContactForm($props);
    echo $component->render();
}
?>
```

## 组件开发规范

### 1. 文件命名
- 使用 kebab-case: `post-card.php`, `nav-menu.php`
- 放在 `components/` 目录下

### 2. 类命名
- 使用 PascalCase: `PostCard`, `NavMenu`
- 类名与文件名对应

### 3. Props 处理
- 使用 `wp_parse_args()` 设置默认值
- 在构造函数注释中列出所有 props

### 4. 样式处理
- 组件样式内联或使用 `wp_add_inline_style()`
- 使用唯一的类名或 ID 避免冲突
- 支持响应式设计

### 5. JavaScript 处理
- 使用 IIFE 避免全局污染
- 事件委托处理动态内容
- 支持多实例

### 6. 安全性
- 所有输出使用适当的转义函数
- 表单提交验证 nonce
- 数据验证和清理

## 目录结构建议
```
theme-name/
├── components/
│   ├── common/
│   │   ├── button.php
│   │   ├── modal.php
│   │   └── loader.php
│   ├── layout/
│   │   ├── header.php
│   │   ├── footer.php
│   │   └── sidebar.php
│   ├── content/
│   │   ├── post-card.php
│   │   ├── post-grid.php
│   │   └── author-box.php
│   └── forms/
│       ├── contact-form.php
│       ├── search-form.php
│       └── newsletter-form.php
├── functions.php
├── component-loader.php
└── templates/
    └── ... (使用组件的模板文件)
```

## 使用建议

1. **保持组件独立性** - 每个组件应该是自包含的
2. **避免组件间依赖** - 通过 props 传递数据
3. **复用通用组件** - 创建基础组件库
4. **遵循 WordPress 标准** - 使用 WordPress 函数和 API
5. **性能优化** - 避免重复加载样式和脚本
6. **文档化** - 为每个组件编写清晰的文档注释