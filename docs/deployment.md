# WordPress 主题部署文档
*硬核导航主题 - 生产环境部署指南*

## 📋 目录

1. [部署前准备](#部署前准备)
2. [环境要求](#环境要求)
3. [部署方式](#部署方式)
4. [配置优化](#配置优化)
5. [性能监控](#性能监控)
6. [安全加固](#安全加固)
7. [备份策略](#备份策略)
8. [故障排除](#故障排除)
9. [版本回滚](#版本回滚)
10. [维护清单](#维护清单)

---

## 🚀 部署前准备

### 环境检查清单

#### 服务器要求
```bash
# 基础环境检查
□ PHP >= 8.0
□ MySQL/MariaDB >= 5.7/10.3
□ Web Server (Apache/Nginx)
□ SSL 证书配置
□ 域名解析正确
□ 服务器性能足够

# PHP 扩展检查
php -m | grep -E "(mysqli|json|mbstring|zip|curl|gd|imagick)"

# 内存和执行时间检查
php -i | grep -E "(memory_limit|max_execution_time|upload_max_filesize)"
```

#### WordPress 环境
```bash
# WordPress 版本检查
□ WordPress >= 6.0
□ 数据库连接正常
□ 文件权限正确
□ wp-config.php 配置完整
□ 必需插件已安装

# 权限设置
find /path/to/wordpress/ -type d -exec chmod 755 {} \;
find /path/to/wordpress/ -type f -exec chmod 644 {} \;
chmod 600 wp-config.php
```

### 构建准备

#### 1. 依赖安装
```bash
# 进入主题目录
cd /path/to/wp-content/themes/yinhedaohang

# 安装 PHP 依赖
composer install --no-dev --optimize-autoloader

# 安装 Node.js 依赖
npm ci --only=production

# 或者使用 yarn
yarn install --production
```

#### 2. 资源构建
```bash
# 生产环境构建
npm run build:production

# 或者使用自定义构建脚本
npm run build -- --mode=production --optimize
```

#### 3. 配置文件检查
```bash
# 检查环境变量
cp .env.example .env.production
nano .env.production

# 生产环境配置示例
WP_DEBUG=false
WP_DEBUG_LOG=false
WP_DEBUG_DISPLAY=false
SCRIPT_DEBUG=false
YINGHE_CACHE_ENABLED=true
YINGHE_MINIFY_ASSETS=true
```

---

## 🖥️ 环境要求

### 最低系统要求

#### 服务器规格
```
CPU: 2 核心
内存: 4GB RAM
存储: 20GB SSD
带宽: 100Mbps

推荐配置:
CPU: 4 核心
内存: 8GB RAM
存储: 50GB SSD
带宽: 1Gbps
```

#### 软件版本
```
操作系统: Ubuntu 20.04+ / CentOS 8+ / Debian 10+
Web服务器: Nginx 1.18+ / Apache 2.4+
PHP: 8.0+ (推荐 8.1+)
数据库: MySQL 8.0+ / MariaDB 10.5+
缓存: Redis 6.0+ / Memcached 1.6+
```

#### PHP 配置要求
```ini
; php.ini 推荐配置
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 64M
post_max_size = 64M
max_file_uploads = 20

; OPcache 配置
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1
```

### 数据库优化配置

#### MySQL/MariaDB 配置
```ini
# my.cnf 优化配置
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

max_connections = 200
max_allowed_packet = 64M
tmp_table_size = 64M
max_heap_table_size = 64M

query_cache_type = 1
query_cache_size = 128M
query_cache_limit = 1M

slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

---

## 🚀 部署方式

### 方式一：手动部署

#### 1. 文件上传
```bash
# 压缩主题文件
tar -czf yinhedaohang-v1.0.0.tar.gz yinhedaohang/

# 上传到服务器
scp yinhedaohang-v1.0.0.tar.gz user@server:/tmp/

# 在服务器上解压
ssh user@server
cd /var/www/html/wp-content/themes/
sudo tar -xzf /tmp/yinhedaohang-v1.0.0.tar.gz
sudo chown -R www-data:www-data yinhedaohang/
```

#### 2. 数据库导入
```bash
# 导出开发环境数据库
mysqldump -u username -p database_name > yinghe_dev.sql

# 导入到生产环境
mysql -u username -p production_db < yinghe_dev.sql

# 更新站点URL
mysql -u username -p production_db
UPDATE wp_options SET option_value = 'https://yourdomain.com' WHERE option_name = 'home';
UPDATE wp_options SET option_value = 'https://yourdomain.com' WHERE option_name = 'siteurl';
```

### 方式二：Git 部署

#### 1. Git Hook 自动部署
```bash
# 在服务器上设置 Git Hook
# /var/repo/yinhedaohang.git/hooks/post-receive

#!/bin/bash
cd /var/www/html/wp-content/themes/yinhedaohang
git --git-dir=/var/repo/yinhedaohang.git --work-tree=/var/www/html/wp-content/themes/yinhedaohang checkout -f

# 安装依赖
composer install --no-dev --optimize-autoloader
npm ci --only=production

# 构建资源
npm run build:production

# 设置权限
chown -R www-data:www-data .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 清除缓存
wp cache flush --path=/var/www/html
```

#### 2. 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

THEME_PATH="/var/www/html/wp-content/themes/yinhedaohang"
BACKUP_PATH="/var/backups/yinhedaohang"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🚀 开始部署硬核导航主题..."

# 1. 创建备份
echo "📦 创建备份..."
mkdir -p $BACKUP_PATH
cp -r $THEME_PATH $BACKUP_PATH/backup_$TIMESTAMP

# 2. 拉取最新代码
echo "📥 拉取最新代码..."
cd $THEME_PATH
git pull origin main

# 3. 安装依赖
echo "📦 安装依赖..."
composer install --no-dev --optimize-autoloader
npm ci --only=production

# 4. 构建资源
echo "🏗️ 构建资源..."
npm run build:production

# 5. 优化文件
echo "⚡ 优化文件..."
# 清理开发文件
rm -rf node_modules/.cache
rm -rf tests/
rm -rf .git/

# 6. 设置权限
echo "🔐 设置权限..."
chown -R www-data:www-data .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 7. 清除缓存
echo "🧹 清除缓存..."
wp cache flush --path=/var/www/html
wp rewrite flush --path=/var/www/html

# 8. 健康检查
echo "🏥 健康检查..."
if curl -f -s -o /dev/null https://yourdomain.com; then
    echo "✅ 部署成功！"
else
    echo "❌ 部署失败，开始回滚..."
    cp -r $BACKUP_PATH/backup_$TIMESTAMP/* $THEME_PATH/
    exit 1
fi

echo "🎉 部署完成！"
```

### 方式三：CI/CD 自动部署

#### GitHub Actions 部署
```yaml
# .github/workflows/deploy.yml
name: 部署到生产环境

on:
  push:
    tags:
      - 'v*'

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
        
      - name: 设置 PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
          
      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: 安装依赖
        run: |
          composer install --no-dev --optimize-autoloader
          npm ci --only=production
          
      - name: 构建资源
        run: npm run build:production
        
      - name: 创建部署包
        run: |
          rm -rf node_modules tests .git .github
          tar -czf yinhedaohang-${{ github.ref_name }}.tar.gz .
          
      - name: 部署到服务器
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /var/www/html/wp-content/themes
            
            # 备份当前版本
            if [ -d "yinhedaohang" ]; then
              mv yinhedaohang yinhedaohang_backup_$(date +%Y%m%d_%H%M%S)
            fi
            
            # 下载新版本
            wget ${{ secrets.DEPLOY_URL }}/yinhedaohang-${{ github.ref_name }}.tar.gz
            tar -xzf yinhedaohang-${{ github.ref_name }}.tar.gz -C yinhedaohang
            
            # 设置权限
            chown -R www-data:www-data yinhedaohang
            
            # 清除缓存
            wp cache flush --path=/var/www/html
            
      - name: 健康检查
        run: |
          sleep 30
          curl -f https://yourdomain.com || exit 1
          
      - name: 通知部署结果
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

---

## ⚙️ 配置优化

### Web 服务器配置

#### Nginx 配置
```nginx
# /etc/nginx/sites-available/yinghe.conf

server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    root /var/www/html;
    index index.php index.html;
    
    # SSL 配置
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
    }
    
    # WordPress 规则
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
    
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 缓存配置
        fastcgi_cache_bypass $skip_cache;
        fastcgi_no_cache $skip_cache;
        fastcgi_cache WORDPRESS;
        fastcgi_cache_valid 200 60m;
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~* /(?:uploads|files)/.*\.php$ {
        deny all;
    }
    
    # 性能优化
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    location = /robots.txt {
        log_not_found off;
        access_log off;
        allow all;
    }
}

# FastCGI 缓存配置
fastcgi_cache_path /var/cache/nginx levels=1:2 keys_zone=WORDPRESS:100m inactive=60m;
fastcgi_cache_key "$scheme$request_method$host$request_uri";
fastcgi_cache_use_stale error timeout invalid_header http_500;
fastcgi_ignore_headers Cache-Control Expires Set-Cookie;
```

### WordPress 配置优化

#### wp-config.php 生产配置
```php
<?php
// wp-config.php 生产环境配置

// 数据库配置
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASSWORD', 'your_password');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// 安全密钥（使用 https://api.wordpress.org/secret-key/1.1/salt/ 生成）
define('AUTH_KEY',         'your-unique-auth-key');
define('SECURE_AUTH_KEY',  'your-unique-secure-auth-key');
define('LOGGED_IN_KEY',    'your-unique-logged-in-key');
define('NONCE_KEY',        'your-unique-nonce-key');
define('AUTH_SALT',        'your-unique-auth-salt');
define('SECURE_AUTH_SALT', 'your-unique-secure-auth-salt');
define('LOGGED_IN_SALT',   'your-unique-logged-in-salt');
define('NONCE_SALT',       'your-unique-nonce-salt');

// 数据库表前缀
$table_prefix = 'wp_';

// 调试配置（生产环境关闭）
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', false);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', false);

// 安全配置
define('DISALLOW_FILE_EDIT', true);
define('DISALLOW_FILE_MODS', true);
define('FORCE_SSL_ADMIN', true);
define('WP_POST_REVISIONS', 3);
define('AUTOSAVE_INTERVAL', 300);
define('EMPTY_TRASH_DAYS', 30);

// 缓存配置
define('WP_CACHE', true);
define('COMPRESS_CSS', true);
define('COMPRESS_SCRIPTS', true);
define('CONCATENATE_SCRIPTS', true);
define('ENFORCE_GZIP', true);

// 内存限制
define('WP_MEMORY_LIMIT', '512M');
define('WP_MAX_MEMORY_LIMIT', '1024M');

// 文件系统
define('FS_METHOD', 'direct');

// 数据库优化
define('WP_ALLOW_REPAIR', false);

// 加载 WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}
require_once(ABSPATH . 'wp-settings.php');
```

---

## 📊 性能监控

### 服务器监控

#### 1. 系统监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

LOG_FILE="/var/log/yinghe-monitor.log"
THRESHOLD_CPU=80
THRESHOLD_MEMORY=80
THRESHOLD_DISK=90

# 获取当前时间
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# CPU 使用率
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
CPU_USAGE=${CPU_USAGE%.*}

# 内存使用率
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
MEMORY_USAGE=${MEMORY_USAGE%.*}

# 磁盘使用率
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | cut -d'%' -f1)

# 记录监控数据
echo "[$TIMESTAMP] CPU: ${CPU_USAGE}%, Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}%" >> $LOG_FILE

# 检查阈值并发送警报
if [ $CPU_USAGE -gt $THRESHOLD_CPU ]; then
    echo "[$TIMESTAMP] 警告: CPU 使用率过高 (${CPU_USAGE}%)" >> $LOG_FILE
    # 发送邮件警报
    echo "CPU 使用率过高: ${CPU_USAGE}%" | mail -s "服务器警报" <EMAIL>
fi

if [ $MEMORY_USAGE -gt $THRESHOLD_MEMORY ]; then
    echo "[$TIMESTAMP] 警告: 内存使用率过高 (${MEMORY_USAGE}%)" >> $LOG_FILE
    echo "内存使用率过高: ${MEMORY_USAGE}%" | mail -s "服务器警报" <EMAIL>
fi

if [ $DISK_USAGE -gt $THRESHOLD_DISK ]; then
    echo "[$TIMESTAMP] 警告: 磁盘使用率过高 (${DISK_USAGE}%)" >> $LOG_FILE
    echo "磁盘使用率过高: ${DISK_USAGE}%" | mail -s "服务器警报" <EMAIL>
fi

# 检查 WordPress 站点可用性
if ! curl -f -s -o /dev/null https://yourdomain.com; then
    echo "[$TIMESTAMP] 错误: 网站无法访问" >> $LOG_FILE
    echo "网站无法访问" | mail -s "网站警报" <EMAIL>
fi
```

---

## 🔒 安全加固

### 文件权限设置

```bash
# WordPress 标准权限设置
find /var/www/html/ -type d -exec chmod 755 {} \;
find /var/www/html/ -type f -exec chmod 644 {} \;

# wp-config.php 特殊权限
chmod 600 /var/www/html/wp-config.php

# 上传目录权限
chmod 755 /var/www/html/wp-content/uploads/
find /var/www/html/wp-content/uploads/ -type d -exec chmod 755 {} \;
find /var/www/html/wp-content/uploads/ -type f -exec chmod 644 {} \;

# 主题目录权限
chown -R www-data:www-data /var/www/html/wp-content/themes/yinhedaohang/
```

---

## 💾 备份策略

### 自动备份脚本

#### 1. 完整备份脚本
```bash
#!/bin/bash
# backup.sh - 完整备份脚本

# 配置变量
BACKUP_DIR="/var/backups/yinhedaohang"
SITE_DIR="/var/www/html"
DB_NAME="your_database"
DB_USER="your_username"
DB_PASS="your_password"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR/{files,database}

echo "开始备份: $TIMESTAMP"

# 1. 备份文件
echo "备份网站文件..."
tar -czf $BACKUP_DIR/files/site_backup_$TIMESTAMP.tar.gz \
    --exclude='wp-content/cache/*' \
    --exclude='wp-content/uploads/backup-guard/*' \
    --exclude='*.log' \
    -C $SITE_DIR .

if [ $? -eq 0 ]; then
    echo "✅ 文件备份完成"
else
    echo "❌ 文件备份失败"
    exit 1
fi

# 2. 备份数据库
echo "备份数据库..."
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/database/db_backup_$TIMESTAMP.sql.gz

if [ $? -eq 0 ]; then
    echo "✅ 数据库备份完成"
else
    echo "❌ 数据库备份失败"
    exit 1
fi

# 3. 清理旧备份
echo "清理旧备份文件..."
find $BACKUP_DIR/files -name "site_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR/database -name "db_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "🎉 备份完成!"
```

---

## ↩️ 版本回滚

### 快速回滚脚本

```bash
#!/bin/bash
# rollback.sh - 版本回滚脚本

THEME_PATH="/var/www/html/wp-content/themes/yinhedaohang"
BACKUP_PATH="/var/backups/yinhedaohang"

# 列出可用的备份版本
list_versions() {
    echo "可用的备份版本:"
    echo "主题文件备份:"
    ls -la $BACKUP_PATH/backup_* 2>/dev/null | tail -10
}

# 回滚到指定版本
rollback_to_version() {
    local version=$1
    local backup_dir="$BACKUP_PATH/backup_$version"
    
    if [ ! -d "$backup_dir" ]; then
        echo "❌ 备份版本不存在: $version"
        exit 1
    fi
    
    echo "⚠️  准备回滚到版本: $version"
    read -p "确认回滚? (y/N): " confirm
    
    if [ "$confirm" != "y" ]; then
        echo "回滚已取消"
        exit 0
    fi
    
    echo "开始回滚..."
    
    # 停止相关服务
    systemctl stop nginx
    systemctl stop php8.1-fpm
    
    # 回滚主题文件
    rm -rf $THEME_PATH/*
    cp -r $backup_dir/* $THEME_PATH/
    chown -R www-data:www-data $THEME_PATH
    
    # 重启服务
    systemctl start php8.1-fpm
    systemctl start nginx
    
    echo "✅ 回滚成功！"
}

case $1 in
    "list")
        list_versions
        ;;
    "version")
        rollback_to_version $2
        ;;
    *)
        echo "用法: $0 {list|version <version>}"
        exit 1
        ;;
esac
```

---

## ✅ 维护清单

### 日常维护任务

#### 每日检查清单
```markdown
## 每日维护清单

### 网站状态检查
- [ ] 网站首页正常访问
- [ ] 主要功能页面正常
- [ ] 搜索功能正常
- [ ] 主题切换功能正常
- [ ] 移动端适配正常

### 服务器监控
- [ ] 服务器 CPU 使用率 < 70%
- [ ] 内存使用率 < 80%
- [ ] 磁盘使用率 < 85%
- [ ] 网站响应时间 < 2秒

### 安全检查
- [ ] 检查错误日志异常
- [ ] 检查登录尝试异常
- [ ] 检查文件完整性
- [ ] 确认备份正常执行
```

这个全面的部署文档涵盖了从部署准备到维护管理的各个环节，确保硬核导航主题能够稳定、安全地运行在生产环境中。