# Vue.js 组件集成开发指南

## 概述

本文档详细说明如何在硬核导航 WordPress 主题中集成 Vue.js 组件，实现现代化的前端交互体验，同时保持与现有 PHP 组件系统的无缝协作。

## 架构设计

### 混合架构原则
- **服务端渲染 (SSR)**: PHP 组件负责首屏渲染和 SEO
- **客户端增强**: Vue.js 组件提供动态交互和实时更新
- **渐进式增强**: 确保在 JavaScript 禁用时功能仍可用
- **数据同步**: PHP 和 Vue 组件共享统一的数据源

### 技术栈
```
WordPress PHP (后端) ←→ REST API ←→ Vue.js 3 (前端)
       ↓                                    ↓
   Twig/Blade 模板                    Vue SFC 组件
       ↓                                    ↓
   服务端渲染                         客户端交互
```

## 开发环境配置

### 1. 依赖安装
```json
{
  "name": "yinghe-vue-components",
  "version": "1.0.0",
  "dependencies": {
    "vue": "^3.3.0",
    "@vue/reactivity": "^3.3.0",
    "axios": "^1.5.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.3.0",
    "vite": "^4.4.0",
    "typescript": "^5.0.0",
    "@types/wordpress__api-fetch": "^6.0.0"
  }
}
```

### 2. Vite 构建配置
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  root: 'assets/src',
  base: '/wp-content/themes/yinhedaohang/assets/dist/',
  build: {
    outDir: '../dist',
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'assets/src/main.js'),
        components: path.resolve(__dirname, 'assets/src/components.js')
      },
      output: {
        entryFileNames: '[name].[hash].js',
        chunkFileNames: '[name].[hash].js',
        assetFileNames: '[name].[hash].[ext]'
      }
    }
  },
  define: {
    'process.env': {
      WP_REST_URL: JSON.stringify('/wp-json/yinghe/v1/'),
      WP_NONCE: JSON.stringify('<?php echo wp_create_nonce("wp_rest"); ?>')
    }
  }
})
```

## 组件集成模式

### 1. 嵌入式组件 (Embedded Components)
PHP 渲染基础结构，Vue 接管交互逻辑

```php
<?php
// PHP 组件 - WebsiteCard.php
class YingheWebsiteCardComponent extends YingheAbstractComponent {
    protected function renderComponent(): void {
        ?>
        <div 
            class="website-card-wrapper" 
            data-vue-component="WebsiteCard"
            data-props="<?php echo esc_attr(json_encode($this->getVueProps())); ?>"
        >
            <!-- 服务端渲染的基础内容 -->
            <article class="website-card" data-site-id="<?php echo $this->props['site_id']; ?>">
                <?php $this->renderCardContent(); ?>
            </article>
            
            <!-- Vue 组件挂载点 -->
            <div id="vue-card-<?php echo $this->props['site_id']; ?>"></div>
        </div>
        <?php
    }
    
    private function getVueProps(): array {
        return [
            'siteId' => $this->props['site_id'],
            'initialData' => [
                'title' => $this->props['title'],
                'url' => $this->props['url'],
                'visitCount' => $this->props['visit_count'],
                'likeCount' => $this->props['like_count'],
                'isLiked' => $this->isUserLiked()
            ],
            'apiEndpoints' => [
                'like' => rest_url('yinghe/v1/sites/' . $this->props['site_id'] . '/like'),
                'visit' => rest_url('yinghe/v1/sites/' . $this->props['site_id'] . '/visit')
            ]
        ];
    }
}
```

```vue
<!-- Vue 组件 - WebsiteCard.vue -->
<template>
  <div class="vue-website-card" v-if="mounted">
    <!-- 交互层：点赞、分享、统计更新 -->
    <div class="card-interactions">
      <button 
        @click="toggleLike" 
        :class="['like-btn', { active: isLiked }]"
        :disabled="liking"
      >
        <i :class="likeIconClass"></i>
        <span>{{ likeCount }}</span>
      </button>
      
      <button @click="share" class="share-btn">
        <i class="icon-share"></i>
        <span>分享</span>
      </button>
      
      <div class="stats-realtime">
        <span class="visit-count">
          <i class="icon-eye"></i>
          {{ formatNumber(visitCount) }}
        </span>
      </div>
    </div>
    
    <!-- 实时评论系统 -->
    <div v-if="showComments" class="comments-section">
      <CommentList :site-id="siteId" />
      <CommentForm :site-id="siteId" @comment-added="onCommentAdded" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useWebsiteStore } from '@/stores/website'
import { useSiteStats } from '@/composables/useSiteStats'

interface Props {
  siteId: number
  initialData: {
    title: string
    url: string
    visitCount: number
    likeCount: number
    isLiked: boolean
  }
  apiEndpoints: {
    like: string
    visit: string
  }
}

const props = defineProps<Props>()
const websiteStore = useWebsiteStore()
const { trackVisit, trackLike } = useSiteStats()

const mounted = ref(false)
const liking = ref(false)
const isLiked = ref(props.initialData.isLiked)
const likeCount = ref(props.initialData.likeCount)
const visitCount = ref(props.initialData.visitCount)
const showComments = ref(false)

const likeIconClass = computed(() => ({
  'icon-heart': isLiked.value,
  'icon-heart-outline': !isLiked.value
}))

const toggleLike = async () => {
  if (liking.value) return
  
  liking.value = true
  try {
    const result = await trackLike(props.siteId, !isLiked.value)
    if (result.success) {
      isLiked.value = !isLiked.value
      likeCount.value = result.data.like_count
      
      // 同步到 PHP 端
      await websiteStore.updateSiteStats(props.siteId, {
        like_count: likeCount.value
      })
    }
  } catch (error) {
    console.error('点赞失败:', error)
  } finally {
    liking.value = false
  }
}

const share = async () => {
  if (navigator.share) {
    try {
      await navigator.share({
        title: props.initialData.title,
        url: props.initialData.url
      })
    } catch (error) {
      console.log('分享取消或失败')
    }
  } else {
    // 备用分享方法
    await navigator.clipboard.writeText(props.initialData.url)
    // 显示 toast 提示
  }
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const onCommentAdded = (comment: any) => {
  // 处理新评论添加
}

onMounted(() => {
  mounted.value = true
  // 记录页面访问
  trackVisit(props.siteId)
})
</script>

<style scoped>
.vue-website-card {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.card-interactions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
  pointer-events: all;
}

.like-btn, .share-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.like-btn:hover, .share-btn:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.like-btn.active {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fca5a5;
}
</style>
```

### 2. 独立式组件 (Standalone Components)
完全由 Vue 管理的功能模块

```vue
<!-- SearchFilter.vue -->
<template>
  <div class="search-filter-panel">
    <div class="search-input-group">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜索网站..."
        @input="debounceSearch"
        class="search-input"
      />
      <button @click="clearSearch" v-if="searchQuery" class="clear-btn">
        <i class="icon-x"></i>
      </button>
    </div>
    
    <div class="filter-controls">
      <select v-model="selectedCategory" @change="applyFilters">
        <option value="">所有分类</option>
        <option v-for="cat in categories" :key="cat.id" :value="cat.id">
          {{ cat.name }}
        </option>
      </select>
      
      <select v-model="sortBy" @change="applyFilters">
        <option value="latest">最新添加</option>
        <option value="popular">热门推荐</option>
        <option value="rating">评分最高</option>
        <option value="visits">访问最多</option>
      </select>
      
      <div class="view-mode-toggle">
        <button
          v-for="mode in viewModes"
          :key="mode.value"
          @click="changeViewMode(mode.value)"
          :class="['mode-btn', { active: viewMode === mode.value }]"
        >
          <i :class="mode.icon"></i>
        </button>
      </div>
    </div>
    
    <div class="filter-tags" v-if="activeTags.length">
      <span v-for="tag in activeTags" :key="tag" class="filter-tag">
        {{ tag }}
        <button @click="removeTag(tag)" class="tag-remove">×</button>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { debounce } from 'lodash-es'
import { useWebsiteStore } from '@/stores/website'

const websiteStore = useWebsiteStore()

const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('latest')
const viewMode = ref('card')
const activeTags = ref<string[]>([])

const categories = computed(() => websiteStore.categories)
const viewModes = [
  { value: 'card', icon: 'icon-grid' },
  { value: 'list', icon: 'icon-list' },
  { value: 'compact', icon: 'icon-menu' }
]

const debounceSearch = debounce(() => {
  applyFilters()
}, 300)

const applyFilters = () => {
  websiteStore.updateFilters({
    search: searchQuery.value,
    category: selectedCategory.value,
    sort: sortBy.value,
    tags: activeTags.value
  })
}

const clearSearch = () => {
  searchQuery.value = ''
  applyFilters()
}

const changeViewMode = (mode: string) => {
  viewMode.value = mode
  websiteStore.setViewMode(mode)
  
  // 通知 PHP 端更新布局
  document.dispatchEvent(new CustomEvent('yinghe:viewModeChanged', {
    detail: { mode }
  }))
}

const removeTag = (tag: string) => {
  activeTags.value = activeTags.value.filter(t => t !== tag)
  applyFilters()
}

watch([selectedCategory, sortBy], () => {
  applyFilters()
})
</script>
```

### 3. 混合式组件 (Hybrid Components)
PHP 和 Vue 共同渲染的复杂组件

```php
<?php
// PHP 部分 - WebsiteGrid.php
class YingheWebsiteGridComponent extends YingheAbstractComponent {
    protected function renderComponent(): void {
        ?>
        <div 
            id="website-grid-<?php echo $this->component_id; ?>"
            class="website-grid-container"
            data-vue-component="WebsiteGrid"
            data-initial-sites="<?php echo esc_attr(json_encode($this->getInitialSites())); ?>"
            data-pagination="<?php echo esc_attr(json_encode($this->getPaginationData())); ?>"
        >
            <!-- 服务端渲染的初始内容 -->
            <div class="grid-items-ssr">
                <?php foreach ($this->getInitialSites() as $site): ?>
                    <?php echo yinghe_get_website_card($site); ?>
                <?php endforeach; ?>
            </div>
            
            <!-- Vue 管理的动态内容区域 -->
            <div class="grid-items-vue" style="display: none;"></div>
            
            <!-- 加载状态 -->
            <div class="loading-placeholder" style="display: none;">
                <?php for ($i = 0; $i < 6; $i++): ?>
                    <div class="skeleton-card"></div>
                <?php endfor; ?>
            </div>
            
            <!-- 分页控件 -->
            <div class="pagination-container"></div>
        </div>
        <?php
    }
    
    private function getInitialSites(): array {
        // 获取首页网站数据
        return $this->website_query->get_sites([
            'posts_per_page' => 12,
            'paged' => 1
        ]);
    }
    
    private function getPaginationData(): array {
        return [
            'current_page' => 1,
            'total_pages' => $this->website_query->get_total_pages(),
            'total_sites' => $this->website_query->get_total_sites(),
            'per_page' => 12
        ];
    }
}
```

```vue
<!-- WebsiteGrid.vue -->
<template>
  <div class="vue-website-grid">
    <!-- 搜索过滤器 -->
    <SearchFilter @filters-changed="onFiltersChanged" />
    
    <!-- 网站网格 -->
    <div class="grid-content">
      <TransitionGroup name="grid-item" tag="div" class="grid-items">
        <WebsiteCardVue
          v-for="site in filteredSites"
          :key="`vue-${site.id}`"
          :site-data="site"
          :view-mode="viewMode"
          @site-updated="onSiteUpdated"
        />
      </TransitionGroup>
      
      <!-- 无结果状态 -->
      <div v-if="!loading && filteredSites.length === 0" class="empty-state">
        <i class="icon-search-x large"></i>
        <h3>未找到相关网站</h3>
        <p>尝试调整搜索条件或浏览其他分类</p>
        <button @click="clearFilters" class="btn-primary">
          清除筛选条件
        </button>
      </div>
      
      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more-container">
        <button
          @click="loadMore"
          :disabled="loading"
          class="load-more-btn"
        >
          <span v-if="!loading">加载更多</span>
          <span v-else>
            <i class="icon-spinner spinning"></i>
            加载中...
          </span>
        </button>
      </div>
    </div>
    
    <!-- 无限滚动触发器 -->
    <div ref="loadTrigger" class="load-trigger"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWebsiteStore } from '@/stores/website'
import { useInfiniteScroll } from '@/composables/useInfiniteScroll'
import WebsiteCardVue from './WebsiteCardVue.vue'
import SearchFilter from './SearchFilter.vue'

interface Props {
  initialSites: any[]
  pagination: {
    current_page: number
    total_pages: number
    total_sites: number
    per_page: number
  }
}

const props = defineProps<Props>()
const websiteStore = useWebsiteStore()

const loading = ref(false)
const currentPage = ref(props.pagination.current_page)
const viewMode = ref('card')
const loadTrigger = ref<HTMLElement>()

// 初始化存储
onMounted(() => {
  websiteStore.initializeSites(props.initialSites)
  websiteStore.setPagination(props.pagination)
  
  // 隐藏 SSR 内容，显示 Vue 内容
  const ssrContainer = document.querySelector('.grid-items-ssr')
  const vueContainer = document.querySelector('.grid-items-vue')
  
  if (ssrContainer && vueContainer) {
    ssrContainer.style.display = 'none'
    vueContainer.style.display = 'block'
  }
})

const filteredSites = computed(() => websiteStore.filteredSites)
const hasMore = computed(() => currentPage.value < props.pagination.total_pages)

const onFiltersChanged = async (filters: any) => {
  loading.value = true
  currentPage.value = 1
  
  try {
    await websiteStore.applySiteFilters(filters)
  } catch (error) {
    console.error('筛选失败:', error)
  } finally {
    loading.value = false
  }
}

const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  currentPage.value++
  
  try {
    await websiteStore.loadMoreSites(currentPage.value)
  } catch (error) {
    console.error('加载更多失败:', error)
    currentPage.value--
  } finally {
    loading.value = false
  }
}

const onSiteUpdated = (siteData: any) => {
  websiteStore.updateSite(siteData)
}

const clearFilters = () => {
  websiteStore.clearFilters()
}

// 无限滚动
const { enable: enableInfiniteScroll, disable: disableInfiniteScroll } = 
  useInfiniteScroll(loadTrigger, loadMore, {
    threshold: 100,
    enabled: computed(() => hasMore.value && !loading.value)
  })

onMounted(() => {
  enableInfiniteScroll()
})

onUnmounted(() => {
  disableInfiniteScroll()
})
</script>

<style scoped>
.vue-website-grid {
  min-height: 400px;
}

.grid-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.grid-item-enter-active,
.grid-item-leave-active {
  transition: all 0.3s ease;
}

.grid-item-enter-from,
.grid-item-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state .large {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.load-more-container {
  text-align: center;
  margin-top: 40px;
}

.load-more-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: #2563eb;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.load-trigger {
  height: 1px;
  margin-bottom: 100px;
}
</style>
```

## 状态管理

### Pinia Store 配置
```typescript
// stores/website.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/utils/api'

export const useWebsiteStore = defineStore('website', () => {
  // 状态
  const sites = ref<Website[]>([])
  const categories = ref<Category[]>([])
  const tags = ref<Tag[]>([])
  const filters = ref<FilterState>({
    search: '',
    category: '',
    sort: 'latest',
    tags: []
  })
  const pagination = ref<PaginationState>({
    current_page: 1,
    total_pages: 1,
    total_sites: 0,
    per_page: 12
  })
  const viewMode = ref<'card' | 'list' | 'compact'>('card')
  const loading = ref(false)

  // 计算属性
  const filteredSites = computed(() => {
    let result = sites.value

    // 搜索过滤
    if (filters.value.search) {
      const query = filters.value.search.toLowerCase()
      result = result.filter(site => 
        site.title.toLowerCase().includes(query) ||
        site.description.toLowerCase().includes(query) ||
        site.category.toLowerCase().includes(query)
      )
    }

    // 分类过滤
    if (filters.value.category) {
      result = result.filter(site => site.category_id === filters.value.category)
    }

    // 标签过滤
    if (filters.value.tags.length > 0) {
      result = result.filter(site => 
        filters.value.tags.some(tag => site.tags.includes(tag))
      )
    }

    // 排序
    switch (filters.value.sort) {
      case 'popular':
        result.sort((a, b) => b.visit_count - a.visit_count)
        break
      case 'rating':
        result.sort((a, b) => b.rating - a.rating)
        break
      case 'latest':
        result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        break
    }

    return result
  })

  // 动作
  const initializeSites = (initialSites: Website[]) => {
    sites.value = initialSites
  }

  const setPagination = (paginationData: PaginationState) => {
    pagination.value = { ...paginationData }
  }

  const loadMoreSites = async (page: number) => {
    loading.value = true
    try {
      const response = await api.get('/sites', {
        params: {
          page,
          per_page: pagination.value.per_page,
          ...filters.value
        }
      })
      
      sites.value.push(...response.data.sites)
      pagination.value = response.data.pagination
    } catch (error) {
      console.error('加载更多网站失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const applySiteFilters = async (newFilters: Partial<FilterState>) => {
    filters.value = { ...filters.value, ...newFilters }
    
    loading.value = true
    try {
      const response = await api.get('/sites', {
        params: {
          page: 1,
          per_page: pagination.value.per_page,
          ...filters.value
        }
      })
      
      sites.value = response.data.sites
      pagination.value = response.data.pagination
    } catch (error) {
      console.error('应用筛选失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateSite = (updatedSite: Website) => {
    const index = sites.value.findIndex(site => site.id === updatedSite.id)
    if (index !== -1) {
      sites.value[index] = { ...sites.value[index], ...updatedSite }
    }
  }

  const updateSiteStats = async (siteId: number, stats: Partial<SiteStats>) => {
    try {
      await api.patch(`/sites/${siteId}/stats`, stats)
      updateSite({ id: siteId, ...stats } as Website)
    } catch (error) {
      console.error('更新网站统计失败:', error)
      throw error
    }
  }

  const setViewMode = (mode: 'card' | 'list' | 'compact') => {
    viewMode.value = mode
    
    // 持久化到本地存储
    localStorage.setItem('yinghe_view_mode', mode)
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      category: '',
      sort: 'latest',
      tags: []
    }
  }

  return {
    // 状态
    sites,
    categories,
    tags,
    filters,
    pagination,
    viewMode,
    loading,
    
    // 计算属性
    filteredSites,
    
    // 动作
    initializeSites,
    setPagination,
    loadMoreSites,
    applySiteFilters,
    updateSite,
    updateSiteStats,
    setViewMode,
    clearFilters
  }
})

// 类型定义
interface Website {
  id: number
  title: string
  description: string
  url: string
  thumbnail: string
  favicon: string
  category: string
  category_id: string
  tags: string[]
  visit_count: number
  like_count: number
  rating: number
  is_featured: boolean
  is_new: boolean
  created_at: string
}

interface Category {
  id: string
  name: string
  count: number
}

interface Tag {
  id: string
  name: string
  count: number
}

interface FilterState {
  search: string
  category: string
  sort: 'latest' | 'popular' | 'rating' | 'visits'
  tags: string[]
}

interface PaginationState {
  current_page: number
  total_pages: number
  total_sites: number
  per_page: number
}

interface SiteStats {
  visit_count?: number
  like_count?: number
  rating?: number
}
```

## API 集成

### WordPress REST API 扩展
```php
<?php
// includes/api/class-yinghe-rest-api.php

class YingheRestApi {
    
    public function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);
    }
    
    public function register_routes() {
        register_rest_route('yinghe/v1', '/sites', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [$this, 'get_sites'],
                'permission_callback' => '__return_true',
                'args' => [
                    'page' => [
                        'default' => 1,
                        'sanitize_callback' => 'absint'
                    ],
                    'per_page' => [
                        'default' => 12,
                        'sanitize_callback' => 'absint'
                    ],
                    'search' => [
                        'default' => '',
                        'sanitize_callback' => 'sanitize_text_field'
                    ],
                    'category' => [
                        'default' => '',
                        'sanitize_callback' => 'sanitize_text_field'
                    ],
                    'sort' => [
                        'default' => 'latest',
                        'sanitize_callback' => 'sanitize_text_field'
                    ]
                ]
            ]
        ]);
        
        register_rest_route('yinghe/v1', '/sites/(?P<id>\d+)/like', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [$this, 'toggle_site_like'],
                'permission_callback' => [$this, 'check_like_permission'],
                'args' => [
                    'action_type' => [
                        'required' => true,
                        'enum' => ['like', 'unlike']
                    ]
                ]
            ]
        ]);
        
        register_rest_route('yinghe/v1', '/sites/(?P<id>\d+)/visit', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [$this, 'record_site_visit'],
                'permission_callback' => '__return_true'
            ]
        ]);
        
        register_rest_route('yinghe/v1', '/sites/(?P<id>\d+)/stats', [
            [
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => [$this, 'update_site_stats'],
                'permission_callback' => [$this, 'check_admin_permission']
            ]
        ]);
    }
    
    public function get_sites(WP_REST_Request $request): WP_REST_Response {
        $page = $request->get_param('page');
        $per_page = min($request->get_param('per_page'), 100); // 限制最大数量
        $search = $request->get_param('search');
        $category = $request->get_param('category');
        $sort = $request->get_param('sort');
        
        $args = [
            'post_type' => 'yinghe_site',
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'paged' => $page
        ];
        
        // 搜索查询
        if (!empty($search)) {
            $args['s'] = $search;
        }
        
        // 分类筛选
        if (!empty($category)) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'site_category',
                    'field' => 'slug',
                    'terms' => $category
                ]
            ];
        }
        
        // 排序
        switch ($sort) {
            case 'popular':
                $args['meta_key'] = 'visit_count';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'rating':
                $args['meta_key'] = 'site_rating';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'latest':
            default:
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
        }
        
        $query = new WP_Query($args);
        $sites = [];
        
        foreach ($query->posts as $post) {
            $sites[] = $this->prepare_site_data($post);
        }
        
        $response_data = [
            'sites' => $sites,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $query->max_num_pages,
                'total_sites' => $query->found_posts,
                'per_page' => $per_page
            ]
        ];
        
        return new WP_REST_Response($response_data, 200);
    }
    
    public function toggle_site_like(WP_REST_Request $request): WP_REST_Response {
        $site_id = $request->get_param('id');
        $action_type = $request->get_param('action_type');
        $user_id = get_current_user_id();
        
        if (!$user_id) {
            return new WP_REST_Response([
                'success' => false,
                'message' => '需要登录才能点赞'
            ], 401);
        }
        
        $current_likes = (int) get_post_meta($site_id, 'like_count', true);
        $user_likes = get_user_meta($user_id, 'liked_sites', true) ?: [];
        
        if ($action_type === 'like') {
            if (!in_array($site_id, $user_likes)) {
                $user_likes[] = $site_id;
                $current_likes++;
                update_user_meta($user_id, 'liked_sites', $user_likes);
                update_post_meta($site_id, 'like_count', $current_likes);
                
                // 触发钩子
                do_action('yinghe_site_liked', $site_id, $user_id);
            }
        } else {
            $key = array_search($site_id, $user_likes);
            if ($key !== false) {
                unset($user_likes[$key]);
                $current_likes = max(0, $current_likes - 1);
                update_user_meta($user_id, 'liked_sites', array_values($user_likes));
                update_post_meta($site_id, 'like_count', $current_likes);
                
                // 触发钩子
                do_action('yinghe_site_unliked', $site_id, $user_id);
            }
        }
        
        return new WP_REST_Response([
            'success' => true,
            'data' => [
                'like_count' => $current_likes,
                'is_liked' => in_array($site_id, $user_likes)
            ]
        ], 200);
    }
    
    public function record_site_visit(WP_REST_Request $request): WP_REST_Response {
        $site_id = $request->get_param('id');
        
        // 防止重复计数（基于 IP 和时间）
        $visitor_ip = $_SERVER['REMOTE_ADDR'];
        $visit_key = "visit_{$site_id}_{$visitor_ip}";
        
        if (!get_transient($visit_key)) {
            $current_visits = (int) get_post_meta($site_id, 'visit_count', true);
            $current_visits++;
            
            update_post_meta($site_id, 'visit_count', $current_visits);
            set_transient($visit_key, true, HOUR_IN_SECONDS); // 1小时内不重复计数
            
            // 记录访问日志
            $this->log_visit($site_id, $visitor_ip);
            
            // 触发钩子
            do_action('yinghe_site_visited', $site_id, $visitor_ip);
            
            return new WP_REST_Response([
                'success' => true,
                'data' => ['visit_count' => $current_visits]
            ], 200);
        }
        
        return new WP_REST_Response([
            'success' => true,
            'message' => '访问已记录'
        ], 200);
    }
    
    private function prepare_site_data(WP_Post $post): array {
        $meta = get_post_meta($post->ID);
        
        return [
            'id' => $post->ID,
            'title' => $post->post_title,
            'description' => $post->post_excerpt ?: wp_trim_words($post->post_content, 20),
            'url' => $meta['site_url'][0] ?? '',
            'thumbnail' => get_the_post_thumbnail_url($post->ID, 'medium') ?: '',
            'favicon' => $meta['site_favicon'][0] ?? '',
            'category' => yinghe_get_post_primary_category($post->ID),
            'category_id' => yinghe_get_post_primary_category_id($post->ID),
            'tags' => yinghe_get_post_tags($post->ID),
            'visit_count' => (int) ($meta['visit_count'][0] ?? 0),
            'like_count' => (int) ($meta['like_count'][0] ?? 0),
            'rating' => (float) ($meta['site_rating'][0] ?? 0),
            'is_featured' => (bool) ($meta['is_featured'][0] ?? false),
            'is_new' => strtotime($post->post_date) > strtotime('-7 days'),
            'created_at' => $post->post_date
        ];
    }
    
    private function log_visit(int $site_id, string $ip): void {
        global $wpdb;
        
        $wpdb->insert(
            $wpdb->prefix . 'yinghe_visit_logs',
            [
                'site_id' => $site_id,
                'visitor_ip' => $ip,
                'visit_time' => current_time('mysql'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'referer' => $_SERVER['HTTP_REFERER'] ?? ''
            ],
            ['%d', '%s', '%s', '%s', '%s']
        );
    }
    
    private function check_like_permission(): bool {
        return is_user_logged_in();
    }
    
    private function check_admin_permission(): bool {
        return current_user_can('manage_options');
    }
}

new YingheRestApi();
```

## 组件通信

### 自定义事件系统
```typescript
// utils/eventBus.ts
import { ref } from 'vue'

interface EventCallback {
  (...args: any[]): void
}

class EventBus {
  private events: Map<string, EventCallback[]> = new Map()
  
  on(event: string, callback: EventCallback): void {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event)!.push(callback)
  }
  
  off(event: string, callback?: EventCallback): void {
    if (!this.events.has(event)) return
    
    if (!callback) {
      this.events.delete(event)
      return
    }
    
    const callbacks = this.events.get(event)!
    const index = callbacks.indexOf(callback)
    if (index > -1) {
      callbacks.splice(index, 1)
    }
  }
  
  emit(event: string, ...args: any[]): void {
    if (!this.events.has(event)) return
    
    this.events.get(event)!.forEach(callback => {
      try {
        callback(...args)
      } catch (error) {
        console.error(`Error in event handler for "${event}":`, error)
      }
    })
  }
  
  once(event: string, callback: EventCallback): void {
    const onceCallback = (...args: any[]) => {
      callback(...args)
      this.off(event, onceCallback)
    }
    this.on(event, onceCallback)
  }
}

export const eventBus = new EventBus()

// 全局事件类型
export interface GlobalEvents {
  'site:liked': { siteId: number; isLiked: boolean; likeCount: number }
  'site:visited': { siteId: number; visitCount: number }
  'filter:changed': { filters: any }
  'view:changed': { mode: 'card' | 'list' | 'compact' }
  'search:performed': { query: string; results: number }
}

// 类型安全的事件发射器
export const emitEvent = <K extends keyof GlobalEvents>(
  event: K,
  data: GlobalEvents[K]
): void => {
  eventBus.emit(event, data)
}

// 类型安全的事件监听器
export const onEvent = <K extends keyof GlobalEvents>(
  event: K,
  callback: (data: GlobalEvents[K]) => void
): void => {
  eventBus.on(event, callback)
}
```

### PHP 与 Vue 数据同步
```javascript
// utils/dataSync.js
class DataSync {
  constructor() {
    this.syncQueue = []
    this.syncing = false
    this.syncInterval = 5000 // 5秒同步一次
    
    // 启动同步定时器
    setInterval(() => this.processSyncQueue(), this.syncInterval)
    
    // 页面卸载前同步
    window.addEventListener('beforeunload', () => this.processSyncQueue())
  }
  
  // 添加同步任务
  queueSync(type, data) {
    this.syncQueue.push({
      type,
      data,
      timestamp: Date.now()
    })
    
    // 立即同步高优先级操作
    if (['like', 'visit'].includes(type)) {
      this.processSyncQueue()
    }
  }
  
  // 处理同步队列
  async processSyncQueue() {
    if (this.syncing || this.syncQueue.length === 0) return
    
    this.syncing = true
    const batch = [...this.syncQueue]
    this.syncQueue = []
    
    try {
      // 按类型分组
      const grouped = batch.reduce((acc, item) => {
        if (!acc[item.type]) acc[item.type] = []
        acc[item.type].push(item.data)
        return acc
      }, {})
      
      // 批量同步到服务器
      await Promise.all([
        this.syncLikes(grouped.like || []),
        this.syncVisits(grouped.visit || []),
        this.syncStats(grouped.stats || [])
      ])
      
      // 同步到 PHP 端（通过隐藏表单）
      this.syncToPhp(batch)
      
    } catch (error) {
      console.error('数据同步失败:', error)
      // 重新加入队列
      this.syncQueue.unshift(...batch)
    } finally {
      this.syncing = false
    }
  }
  
  // 同步点赞数据
  async syncLikes(likes) {
    if (likes.length === 0) return
    
    const response = await fetch('/wp-json/yinghe/v1/sync/likes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': window.yingheConfig.nonce
      },
      body: JSON.stringify({ likes })
    })
    
    if (!response.ok) {
      throw new Error('点赞同步失败')
    }
  }
  
  // 同步访问数据
  async syncVisits(visits) {
    if (visits.length === 0) return
    
    const response = await fetch('/wp-json/yinghe/v1/sync/visits', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': window.yingheConfig.nonce
      },
      body: JSON.stringify({ visits })
    })
    
    if (!response.ok) {
      throw new Error('访问同步失败')
    }
  }
  
  // 同步到 PHP 端（用于服务端渲染）
  syncToPhp(data) {
    const form = document.createElement('form')
    form.style.display = 'none'
    form.method = 'POST'
    form.action = window.location.href
    
    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = 'yinghe_sync_data'
    input.value = JSON.stringify(data)
    
    form.appendChild(input)
    document.body.appendChild(form)
    
    // 触发 PHP 端处理
    const event = new CustomEvent('yinghe:syncData', {
      detail: data
    })
    document.dispatchEvent(event)
    
    document.body.removeChild(form)
  }
}

export const dataSync = new DataSync()
```

## 性能优化

### 组件懒加载
```typescript
// composables/useLazyComponent.ts
import { defineAsyncComponent, ref, onMounted, onUnmounted } from 'vue'

export function useLazyComponent(importFn: () => Promise<any>) {
  const isVisible = ref(false)
  const target = ref<HTMLElement>()
  
  let observer: IntersectionObserver | null = null
  
  const component = defineAsyncComponent({
    loader: importFn,
    loadingComponent: {
      template: '<div class="component-loading">Loading...</div>'
    },
    errorComponent: {
      template: '<div class="component-error">Error loading component</div>'
    },
    delay: 200,
    timeout: 10000
  })
  
  onMounted(() => {
    if (!target.value) return
    
    observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          isVisible.value = true
          observer?.disconnect()
        }
      },
      { threshold: 0.1 }
    )
    
    observer.observe(target.value)
  })
  
  onUnmounted(() => {
    observer?.disconnect()
  })
  
  return {
    component,
    isVisible,
    target
  }
}
```

### 虚拟滚动
```vue
<!-- components/VirtualList.vue -->
<template>
  <div ref="container" class="virtual-list" @scroll="onScroll">
    <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
    
    <div class="virtual-list-content" :style="contentStyle">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        :style="{ height: itemHeight + 'px' }"
        class="virtual-list-item"
      >
        <slot :item="item" :index="item.index"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  containerHeight: number
}

const props = defineProps<Props>()

const container = ref<HTMLElement>()
const scrollTop = ref(0)
const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))
const totalHeight = computed(() => props.items.length * props.itemHeight)

const startIndex = computed(() => {
  return Math.floor(scrollTop.value / props.itemHeight)
})

const endIndex = computed(() => {
  return Math.min(startIndex.value + visibleCount.value + 1, props.items.length)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value).map((item, index) => ({
    ...item,
    index: startIndex.value + index
  }))
})

const contentStyle = computed(() => ({
  transform: `translateY(${startIndex.value * props.itemHeight}px)`
}))

const onScroll = (e: Event) => {
  scrollTop.value = (e.target as HTMLElement).scrollTop
}
</script>

<style scoped>
.virtual-list {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.virtual-list-phantom {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
</style>
```

## 构建和部署

### 构建脚本
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:prod": "NODE_ENV=production vite build",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit",
    "lint": "eslint src --ext .vue,.js,.ts",
    "format": "prettier --write src"
  }
}
```

### WordPress 集成脚本
```php
<?php
// includes/class-yinghe-vue-integration.php

class YingheVueIntegration {
    
    private $manifest_path;
    private $manifest_data;
    
    public function __construct() {
        $this->manifest_path = get_template_directory() . '/assets/dist/manifest.json';
        $this->load_manifest();
        
        add_action('wp_enqueue_scripts', [$this, 'enqueue_vue_assets']);
        add_action('wp_footer', [$this, 'init_vue_components']);
    }
    
    private function load_manifest(): void {
        if (file_exists($this->manifest_path)) {
            $this->manifest_data = json_decode(file_get_contents($this->manifest_path), true);
        }
    }
    
    public function enqueue_vue_assets(): void {
        if (!$this->manifest_data) return;
        
        $template_uri = get_template_directory_uri();
        
        // 主应用文件
        if (isset($this->manifest_data['main.js'])) {
            wp_enqueue_script(
                'yinghe-vue-main',
                $template_uri . '/assets/dist/' . $this->manifest_data['main.js']['file'],
                [],
                null,
                true
            );
        }
        
        // CSS 文件
        if (isset($this->manifest_data['main.css'])) {
            wp_enqueue_style(
                'yinghe-vue-styles',
                $template_uri . '/assets/dist/' . $this->manifest_data['main.css']['file'],
                [],
                null
            );
        }
        
        // 组件包
        if (isset($this->manifest_data['components.js'])) {
            wp_enqueue_script(
                'yinghe-vue-components',
                $template_uri . '/assets/dist/' . $this->manifest_data['components.js']['file'],
                ['yinghe-vue-main'],
                null,
                true
            );
        }
        
        // 传递配置到前端
        wp_localize_script('yinghe-vue-main', 'yingheConfig', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'restUrl' => rest_url('yinghe/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'userId' => get_current_user_id(),
            'isLoggedIn' => is_user_logged_in(),
            'siteUrl' => home_url(),
            'assetsUrl' => $template_uri . '/assets/dist/',
            'debug' => defined('WP_DEBUG') && WP_DEBUG
        ]);
    }
    
    public function init_vue_components(): void {
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Vue 组件
            if (window.YingheVue) {
                window.YingheVue.initComponents();
            }
        });
        </script>
        <?php
    }
}

new YingheVueIntegration();
```

## 最佳实践

### 1. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 通过 props 和 slots 提供灵活性
- **类型安全**: 使用 TypeScript 定义清晰的接口
- **性能优化**: 合理使用 computed 和 watch

### 2. 状态管理规范
- **本地状态**: 组件内部使用 ref/reactive
- **共享状态**: 使用 Pinia store
- **持久化**: 重要状态同步到 localStorage/sessionStorage
- **服务端同步**: 关键数据及时同步到 WordPress

### 3. 错误处理
```typescript
// utils/errorHandler.ts
export class VueErrorHandler {
  static install(app: any) {
    app.config.errorHandler = (error: Error, instance: any, info: string) => {
      console.error('Vue Error:', error)
      console.error('Component:', instance)
      console.error('Info:', info)
      
      // 发送错误报告
      this.reportError(error, info)
    }
  }
  
  static reportError(error: Error, context: string) {
    if (window.yingheConfig?.debug) {
      console.error('Error reported:', { error, context })
      return
    }
    
    // 生产环境下发送错误报告
    fetch('/wp-json/yinghe/v1/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': window.yingheConfig.nonce
      },
      body: JSON.stringify({
        message: error.message,
        stack: error.stack,
        context: context,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      })
    }).catch(console.error)
  }
}
```

### 4. 测试策略
```typescript
// tests/components/WebsiteCard.test.ts
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import WebsiteCard from '@/components/WebsiteCard.vue'

describe('WebsiteCard', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  
  it('renders website data correctly', () => {
    const wrapper = mount(WebsiteCard, {
      props: {
        siteId: 1,
        initialData: {
          title: 'Test Site',
          url: 'https://example.com',
          visitCount: 100,
          likeCount: 50,
          isLiked: false
        }
      }
    })
    
    expect(wrapper.text()).toContain('Test Site')
    expect(wrapper.find('.visit-count').text()).toContain('100')
    expect(wrapper.find('.like-btn').classes()).not.toContain('active')
  })
  
  it('handles like button click', async () => {
    const wrapper = mount(WebsiteCard, {
      props: {
        siteId: 1,
        initialData: {
          title: 'Test Site',
          url: 'https://example.com',
          visitCount: 100,
          likeCount: 50,
          isLiked: false
        }
      }
    })
    
    await wrapper.find('.like-btn').trigger('click')
    
    // 验证状态变化
    expect(wrapper.vm.isLiked).toBe(true)
    expect(wrapper.find('.like-btn').classes()).toContain('active')
  })
})
```

## 总结

这个 Vue.js 集成方案实现了：

1. **渐进式增强**: 基于 PHP 组件的服务端渲染，Vue 提供交互增强
2. **性能优化**: 虚拟滚动、懒加载、代码分割等优化技术
3. **类型安全**: 完整的 TypeScript 支持和类型定义
4. **状态管理**: Pinia store 管理复杂状态，与 WordPress 数据同步
5. **开发体验**: 热重载、ESLint、Prettier 等开发工具
6. **生产就绪**: 完整的构建流程和错误处理机制

通过这种混合架构，我们既保持了 WordPress 的 SEO 友好性和服务端渲染优势，又获得了现代 Vue.js 应用的交互体验和开发效率。