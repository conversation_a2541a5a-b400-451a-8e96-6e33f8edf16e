<?php
/**
 * 网站卡片组件使用示例
 * 
 * 这个文件展示了如何使用 YingheWebsiteCardComponent 组件
 * 
 * @package YingheTheme
 * @subpackage Examples
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 确保抽象基类已加载
require_once get_template_directory() . '/includes/abstract-class-component.php';

// 加载网站卡片组件
require_once get_template_directory() . '/components/content/website-card-component.php';

?>

<div class="components-demo-page">
    <h1>网站卡片组件演示</h1>
    
    <section class="demo-section">
        <h2>1. 基本使用</h2>
        <div class="demo-grid">
            <?php
            // 最简单的使用方式
            echo YingheWebsiteCardComponent::render_static([
                'title' => 'WordPress 官网',
                'url' => 'https://wordpress.org',
                'description' => 'WordPress 是世界上最受欢迎的网站建设工具'
            ]);
            ?>
        </div>
    </section>
    
    <section class="demo-section">
        <h2>2. 完整功能展示</h2>
        <div class="demo-grid">
            <?php
            // 带完整功能的卡片
            echo YingheWebsiteCardComponent::render_static([
                'title' => 'GitHub',
                'url' => 'https://github.com',
                'description' => '全球最大的代码托管平台，开发者的首选工具',
                'thumbnail' => '/wp-content/themes/yinhedaohang/static/picture/logo-black.webp',
                'category' => '开发工具',
                'tags' => ['代码', '开源', '协作'],
                'visit_count' => 125680,
                'rating' => 4.8,
                'is_featured' => true,
                'is_hot' => true,
                'layout' => 'default'
            ]);
            ?>
        </div>
    </section>
    
    <section class="demo-section">
        <h2>3. 不同布局展示</h2>
        
        <h3>紧凑布局</h3>
        <div class="demo-grid compact-grid">
            <?php
            $sites = [
                ['title' => '百度', 'url' => 'https://baidu.com', 'category' => '搜索引擎', 'visit_count' => 98765],
                ['title' => '谷歌', 'url' => 'https://google.com', 'category' => '搜索引擎', 'visit_count' => 156432],
                ['title' => '微博', 'url' => 'https://weibo.com', 'category' => '社交媒体', 'visit_count' => 76543],
            ];
            
            foreach ($sites as $site) {
                echo YingheWebsiteCardComponent::render_static(array_merge($site, [
                    'layout' => 'compact',
                    'show_description' => false
                ]));
            }
            ?>
        </div>
        
        <h3>列表布局</h3>
        <div class="demo-list">
            <?php
            $detailed_sites = [
                [
                    'title' => 'Stack Overflow',
                    'url' => 'https://stackoverflow.com',
                    'description' => '程序员问答社区，解决编程问题的最佳平台',
                    'category' => '技术社区',
                    'tags' => ['编程', '问答', '社区'],
                    'visit_count' => 234567,
                    'rating' => 4.6,
                    'is_hot' => true
                ],
                [
                    'title' => 'CodePen',
                    'url' => 'https://codepen.io',
                    'description' => '前端代码在线编辑器，展示和分享你的创意',
                    'category' => '开发工具',
                    'tags' => ['前端', '编辑器', '分享'],
                    'visit_count' => 89234,
                    'rating' => 4.4,
                    'is_new' => true
                ]
            ];
            
            foreach ($detailed_sites as $site) {
                echo YingheWebsiteCardComponent::render_static(array_merge($site, [
                    'layout' => 'list'
                ]));
            }
            ?>
        </div>
        
        <h3>特色布局</h3>
        <div class="demo-featured">
            <?php
            echo YingheWebsiteCardComponent::render_static([
                'title' => 'Vue.js',
                'url' => 'https://vuejs.org',
                'description' => '渐进式 JavaScript 框架，易学易用，性能优异',
                'thumbnail' => '/wp-content/themes/yinhedaohang/static/picture/logo-white-wide.webp',
                'category' => 'JavaScript框架',
                'tags' => ['Vue', 'JavaScript', '前端'],
                'visit_count' => 345678,
                'rating' => 4.9,
                'is_featured' => true,
                'layout' => 'featured'
            ]);
            ?>
        </div>
    </section>
    
    <section class="demo-section">
        <h2>4. 状态展示</h2>
        <div class="demo-grid">
            <?php
            // 新站点
            echo YingheWebsiteCardComponent::render_static([
                'title' => '新兴网站',
                'url' => 'https://example-new.com',
                'description' => '这是一个新上线的网站',
                'is_new' => true,
                'visit_count' => 123
            ]);
            
            // 热门网站
            echo YingheWebsiteCardComponent::render_static([
                'title' => '热门网站',
                'url' => 'https://example-hot.com',
                'description' => '这是一个热门的网站',
                'is_hot' => true,
                'visit_count' => 999999
            ]);
            
            // 离线网站
            echo YingheWebsiteCardComponent::render_static([
                'title' => '离线网站',
                'url' => 'https://example-offline.com',
                'description' => '这个网站暂时无法访问',
                'is_offline' => true,
                'clickable' => false
            ]);
            ?>
        </div>
    </section>
    
    <section class="demo-section">
        <h2>5. 自定义样式</h2>
        <div class="demo-grid">
            <?php
            // 自定义CSS类
            echo YingheWebsiteCardComponent::render_static([
                'title' => '自定义样式卡片',
                'url' => 'https://example-custom.com',
                'description' => '这个卡片使用了自定义CSS类',
                'class' => 'custom-border',
                'modifier' => 'special'
            ]);
            ?>
        </div>
    </section>
    
    <section class="demo-section">
        <h2>6. 编程方式使用</h2>
        <div class="demo-grid">
            <?php
            // 实例化方式
            $card = new YingheWebsiteCardComponent([
                'title' => '实例化卡片',
                'url' => 'https://example-instance.com',
                'description' => '这个卡片是通过实例化创建的',
                'track_visit' => true
            ]);
            
            echo $card->render();
            ?>
        </div>
    </section>
    
    <section class="demo-section">
        <h2>7. 辅助函数使用</h2>
        <div class="demo-grid">
            <?php
            // 使用辅助函数
            yinghe_render_website_card([
                'title' => '辅助函数卡片',
                'url' => 'https://example-helper.com',
                'description' => '这个卡片是通过辅助函数创建的'
            ]);
            ?>
        </div>
    </section>
</div>

<style>
/* 演示页面样式 */
.components-demo-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-section {
    margin-bottom: 40px;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 30px;
}

.demo-section h2 {
    color: #1f2937;
    margin-bottom: 20px;
    font-size: 24px;
}

.demo-section h3 {
    color: #374151;
    margin: 20px 0 16px 0;
    font-size: 18px;
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.compact-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.demo-list {
    max-width: 800px;
}

.demo-featured {
    max-width: 500px;
}

/* 自定义样式示例 */
.yinghe-website-card.custom-border {
    border: 3px solid #3b82f6;
}

.yinghe-website-card.yinghe-website-card--special {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.yinghe-website-card--special .card-title {
    color: white;
}

.yinghe-website-card--special .card-description p {
    color: rgba(255, 255, 255, 0.9);
}

@media (max-width: 768px) {
    .demo-grid {
        grid-template-columns: 1fr;
    }
    
    .compact-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}
</style>

<script>
// 演示页面交互
document.addEventListener('DOMContentLoaded', function() {
    console.log('网站卡片组件演示页面已加载');
    
    // 监听卡片点击事件
    document.addEventListener('yinghe:card:click', function(e) {
        console.log('卡片被点击:', e.detail);
        
        // 显示通知（如果有通知系统）
        if (window.showNotification) {
            window.showNotification(`访问: ${e.detail.title}`, 'info');
        }
    });
    
    // 统计组件使用情况
    const cards = document.querySelectorAll('.yinghe-website-card');
    console.log(`页面上共有 ${cards.length} 个网站卡片组件`);
    
    // 统计不同布局的使用情况
    const layouts = ['default', 'compact', 'list', 'featured'];
    layouts.forEach(layout => {
        const count = document.querySelectorAll(`.yinghe-website-card.layout-${layout}`).length;
        if (count > 0) {
            console.log(`${layout} 布局: ${count} 个`);
        }
    });
});
</script>