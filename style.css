/*
Theme Name: 硬核指南
Theme URI: https://yinghezhinan.com
Description: 专注收录免费且优质的影音娱乐网站导航主题，支持组件化开发架构。
Author: 硬核指南
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: yinghe
Domain Path: /languages
Tags: navigation, responsive, sites-directory, component-based
Requires at least: 5.8
Tested up to: 6.4
Requires PHP: 7.4

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned with others.
*/

/**
 * 硬核指南主题 - 样式表
 * 
 * 这个文件包含主题的基础样式和组件样式
 * 更多样式在 assets/dist/css/ 目录中的编译文件中
 */

/* ==========================================================================
   基础重置和变量
   ========================================================================== */

:root {
    /* 品牌色彩 */
    --yinghe-primary: #283593;
    --yinghe-secondary: #667eea;
    --yinghe-accent: #764ba2;
    
    /* 功能色彩 */
    --yinghe-success: #10b981;
    --yinghe-warning: #f59e0b;
    --yinghe-error: #ef4444;
    --yinghe-info: #3b82f6;
    
    /* 中性色彩 */
    --yinghe-gray-50: #f9fafb;
    --yinghe-gray-100: #f3f4f6;
    --yinghe-gray-200: #e5e7eb;
    --yinghe-gray-300: #d1d5db;
    --yinghe-gray-400: #9ca3af;
    --yinghe-gray-500: #6b7280;
    --yinghe-gray-600: #4b5563;
    --yinghe-gray-700: #374151;
    --yinghe-gray-800: #1f2937;
    --yinghe-gray-900: #111827;
    
    /* 布局参数 */
    --yinghe-sidebar-width: 220px;
    --yinghe-sidebar-collapsed-width: 110px;
    --yinghe-header-height: 60px;
    --yinghe-border-radius: 8px;
    --yinghe-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --yinghe-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    
    /* 动画 */
    --yinghe-transition: all 0.3s ease;
    --yinghe-transition-fast: all 0.15s ease;
}

/* 深色模式变量 */
.io-black-mode {
    --yinghe-bg-primary: #0f172a;
    --yinghe-bg-secondary: #1e293b;
    --yinghe-bg-tertiary: #334155;
    --yinghe-text-primary: #f1f5f9;
    --yinghe-text-secondary: #cbd5e1;
    --yinghe-text-muted: #94a3b8;
    --yinghe-border-color: #334155;
}

/* 浅色模式变量 */
.io-grey-mode {
    --yinghe-bg-primary: #ffffff;
    --yinghe-bg-secondary: #f8fafc;
    --yinghe-bg-tertiary: #f1f5f9;
    --yinghe-text-primary: #0f172a;
    --yinghe-text-secondary: #334155;
    --yinghe-text-muted: #64748b;
    --yinghe-border-color: #e2e8f0;
}

/* ==========================================================================
   基础样式
   ========================================================================== */

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    line-height: 1.6;
    color: var(--yinghe-text-primary);
    background-color: var(--yinghe-bg-primary);
    margin: 0;
    padding: 0;
    transition: var(--yinghe-transition);
}

* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

a {
    color: var(--yinghe-primary);
    text-decoration: none;
    transition: var(--yinghe-transition-fast);
}

a:hover {
    color: var(--yinghe-secondary);
}

/* ==========================================================================
   WordPress 对齐类
   ========================================================================== */

.alignleft {
    float: left;
    margin-right: 1.5em;
    margin-bottom: 1.5em;
}

.alignright {
    float: right;
    margin-left: 1.5em;
    margin-bottom: 1.5em;
}

.aligncenter {
    clear: both;
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1.5em;
}

/* ==========================================================================
   辅助类
   ========================================================================== */

.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute !important;
    width: 1px;
    word-wrap: normal !important;
}

.screen-reader-text:focus {
    background-color: var(--yinghe-bg-secondary);
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    clip-path: none;
    color: var(--yinghe-text-primary);
    display: block;
    font-size: 0.875rem;
    font-weight: 700;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}

/* ==========================================================================
   组件基础样式
   ========================================================================== */

.yinghe-component {
    position: relative;
}

.yinghe-component--loading {
    opacity: 0.6;
    pointer-events: none;
}

.yinghe-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(var(--yinghe-primary), 0.3);
    border-radius: 50%;
    border-top-color: var(--yinghe-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ==========================================================================
   响应式布局
   ========================================================================== */

.container-fluid.customize-width {
    max-width: 1900px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
}

.main-content {
    min-height: 100vh;
    transition: var(--yinghe-transition);
}

@media (min-width: 768px) {
    .main-content {
        margin-left: var(--yinghe-sidebar-width);
    }
    
    .main-content .page-header {
        left: var(--yinghe-sidebar-width);
    }
    
    .sidebar-nav.collapsed + .main-content {
        margin-left: var(--yinghe-sidebar-collapsed-width);
    }
    
    .sidebar-nav.collapsed + .main-content .page-header {
        left: var(--yinghe-sidebar-collapsed-width);
    }
}

@media (max-width: 767px) {
    .main-content {
        margin-left: 0;
    }
    
    .main-content .page-header {
        left: 0;
    }
}

/* ==========================================================================
   打印样式
   ========================================================================== */

@media print {
    .sidebar-nav,
    .header-menu,
    .toggle-item,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }
    
    a {
        text-decoration: underline;
        color: #000;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }
    
    .url-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* ==========================================================================
   主题切换动画
   ========================================================================== */

* {
    transition: background-color 0.3s ease, 
                color 0.3s ease, 
                border-color 0.3s ease,
                box-shadow 0.3s ease;
}

/* 禁用不必要的动画 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}