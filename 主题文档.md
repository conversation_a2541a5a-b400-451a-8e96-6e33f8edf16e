# 硬核指南 WordPress 主题文档

## 项目概述

**主题名称**: 硬核指南 (<PERSON><PERSON>)  
**版本**: 1.0.0  
**作者**: 硬核指南团队  
**官方网站**: https://yinghezhinan.com  
**主题类型**: 网站导航、目录类主题

### 主题简介
硬核指南是一个专注于收录免费且优质的影音娱乐网站导航的WordPress主题。该主题采用组件化开发架构，支持深色/浅色主题切换，具有响应式设计和现代化的用户界面。

## 技术栈

### 前端技术
- **CSS框架**: Bootstrap 5.x
- **JavaScript库**: 
  - jQuery 3.x
  - Swiper (轮播组件)
  - Clipboard.js (复制功能)
  - LazyLoad (图片懒加载)
  - Theia Sticky Sidebar (侧边栏固定)
- **图标字体**: 自定义字体图标 (font_4152982_f00lvbu68l)
- **CSS预处理器**: 原生CSS + CSS变量

### 后端技术
- **WordPress版本**: 5.8+
- **PHP版本**: 7.4+
- **数据库**: MySQL 5.7+

### 开发架构
- **组件化开发**: 基于自定义组件系统
- **模块化**: 功能模块独立封装
- **响应式设计**: 移动端优先
- **主题系统**: 支持深色/浅色模式切换

## 项目结构

```
yinhedaohang/
├── components/                    # 组件目录
│   ├── content/                  # 内容组件
│   │   ├── website-card/         # 网站卡片组件
│   │   │   ├── class-website-card.php
│   │   │   └── website-card-component.php
│   │   └── website-card.php       # 网站卡片组件(旧版)
│   ├── layout/                   # 布局组件
│   │   ├── header-system.php     # 头部系统
│   │   └── sidebar-nav.php       # 侧边栏导航
│   └── utilities/                # 工具组件
│       └── theme-switcher.php    # 主题切换器
├── includes/                     # 核心类库
│   ├── abstract-class-component.php    # 组件抽象基类
│   ├── class-ajax-handler.php         # AJAX处理器
│   ├── class-asset-manager.php        # 资源管理器
│   ├── class-component-loader.php     # 组件加载器
│   ├── class-customizer.php          # 主题定制器
│   ├── class-security.php            # 安全类
│   └── theme-helpers.php             # 主题助手函数
├── assets/                       # 静态资源
│   ├── css/                       # 样式文件
│   │   ├── bootstrap.min.css
│   │   ├── font_4152982_f00lvbu68l.css
│   │   ├── style.css
│   │   ├── style.min.css
│   │   └── swiper-bundle.min.css
│   ├── js/                        # JavaScript文件
│   │   ├── app.min.js
│   │   ├── bootstrap.min.js
│   │   ├── clipboard.min.js
│   │   ├── install-pwa.js
│   │   ├── jquery.min.js
│   │   ├── lazyload.min.js
│   │   ├── popper.min.js
│   │   ├── stat.js
│   │   ├── swiper-bundle.min.js
│   │   ├── theia-sticky-sidebar.js
│   │   └── yinghe.js
│   ├── fonts/                     # 字体文件
│   │   ├── font_4152982_f00lvbu68l.ttf
│   │   ├── font_4152982_f00lvbu68l.woff
│   │   └── font_4152982_f00lvbu68l.woff2
│   ├── images/                    # 图片资源
│   │   ├── alipay.svg
│   │   ├── wechat.svg
│   │   └── ...
│   └── vendor/                    # 第三方库
│       └── bootstrap/             # Bootstrap相关文件
├── docs/                         # 开发文档
│   ├── wordpress-component-based-dev.md    # 组件化开发指南
│   ├── wordpress-dev-rules-for-claude-code.md  # 开发规范
│   ├── wordpress-ai-dev-complete.md    # 架构理论
│   └── ...
├── examples/                     # 示例代码
│   └── website-card-demo.php
├── data/                         # 数据文件
│   ├── init.json
│   └── model_pricing.json
├── claude-relay-service/          # Claude中继服务
│   ├── src/
│   ├── config/
│   └── ...
├── LICENSE                       # GPL许可证文件
├── 404.php                       # 404错误页面
├── archive.php                   # 归档页面
├── comments.php                  # 评论模板
├── functions.php                 # 主题函数文件
├── header.php                    # 头部模板
├── footer.php                    # 底部模板
├── index.php                     # 主页模板
├── page.php                      # 页面模板
├── search.php                    # 搜索结果页面
├── sidebar.php                   # 侧边栏模板
├── single.php                    # 文章页面
├── style.css                     # 主题样式文件
└── CLAUDE.md                     # Claude开发指令
```

## 核心功能

### 1. 组件化系统
- **Website Card组件**: 网站卡片展示组件
- **Header System组件**: 头部导航系统
- **Sidebar Nav组件**: 侧边栏导航
- **Theme Switcher组件**: 主题切换器

### 2. 主题系统
- **深色模式**: 适合夜间使用
- **浅色模式**: 适合白天使用
- **自动切换**: 根据系统设置自动切换
- **平滑过渡**: 主题切换动画效果

### 3. 响应式设计
- **移动端优化**: 适配各种移动设备
- **平板适配**: 优化的平板显示效果
- **桌面端**: 完整的桌面体验
- **断点设计**: 768px、992px、1200px等关键断点

### 4. 交互功能
- **搜索功能**: 网站搜索和筛选
- **分类导航**: 按分类浏览网站
- **复制链接**: 一键复制网站链接
- **分享功能**: 支持多种分享方式
- **PWA支持**: 渐进式Web应用
- **Claude Relay Service**: AI助手中继服务
- **访问统计**: 详细的网站访问数据分析
- **搜索引擎集成**: 支持多个搜索引擎的分类查询

## 开发规范

### 1. 组件开发规范
- 必须继承 `Abstract_Component` 基类
- 遵循单一职责原则
- 支持参数化配置
- 实现标准的生命周期方法

### 2. 命名规范
- **类名**: PascalCase (如: `Website_Card`)
- **函数名**: snake_case (如: `render_website_card`)
- **变量名**: snake_case (如: `$website_data`)
- **CSS类名**: kebab-case (如: `website-card`)
- **文件名**: snake_case (如: `website_card.php`)

### 3. 文件组织
- 组件文件放在 `components/` 目录
- 每个组件一个文件夹
- 相关资源放在组件目录内
- 共享资源放在 `static/` 目录

### 4. 安全规范
- 所有用户输入必须过滤
- 使用WordPress内置函数处理数据
- 实现CSRF保护
- 定期更新依赖库

## 部署要求

### 服务器要求
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP**: 7.4 或更高版本
- **MySQL**: 5.7 或更高版本
- **WordPress**: 5.8 或更高版本

### PHP扩展要求
- `mbstring`
- `curl`
- `gd`
- `json`
- `openssl`
- `xml`

### 推荐配置
- **内存限制**: 128MB 或更高
- **上传文件大小**: 10MB 或更高
- **执行时间**: 30秒 或更高
- **启用Gzip压缩**
- **配置浏览器缓存**

## 安装指南

### 1. 上传主题
1. 将主题文件夹上传到 `/wp-content/themes/` 目录
2. 在WordPress后台"外观" → "主题"中激活主题
3. 按照配置向导完成设置

### 2. 配置要求
1. 设置固定链接结构
2. 配置分类目录
3. 上传网站数据
4. 测试所有功能

### 3. 优化设置
1. 启用缓存插件
2. 配置CDN加速
3. 设置图片优化
4. 配置SEO插件

## 维护指南

### 1. 日常维护
- 定期备份网站数据
- 更新WordPress核心
- 更新主题和插件
- 监控网站性能

### 2. 安全维护
- 定期更改密码
- 启用SSL证书
- 限制登录尝试
- 监控恶意访问

### 3. 性能优化
- 优化图片大小
- 启用浏览器缓存
- 压缩CSS和JavaScript
- 使用CDN加速

## 故障排除

### 常见问题
1. **主题无法激活**: 检查PHP版本和扩展
2. **样式错乱**: 检查文件权限和路径
3. **功能异常**: 查看错误日志
4. **性能问题**: 检查服务器配置

### 调试方法
1. 启用WordPress调试模式
2. 查看浏览器控制台
3. 检查服务器错误日志
4. 使用开发者工具分析

## 开发工具

### 1. 本地开发环境
- **Local**: 推荐的本地开发环境
- **XAMPP**: 跨平台开发环境
- **Docker**: 容器化开发环境

### 2. 开发工具
- **VS Code**: 推荐的代码编辑器
- **WordPress插件**: Query Monitor, Debug Bar
- **浏览器扩展**: WordPress Switcher, User Agent Switcher

### 3. 版本控制
- **Git**: 版本控制系统
- **GitHub**: 代码托管平台
- **GitLab**: 自托管Git平台

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础功能
- 支持组件化开发
- 响应式设计
- 深色/浅色主题切换

## 贡献指南

### 1. 代码贡献
1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 发起Pull Request

### 2. 问题反馈
1. 在GitHub Issues中提交问题
2. 详细描述问题现象
3. 提供复现步骤
4. 附上相关截图

### 3. 功能建议
1. 在Discussions中讨论新功能
2. 详细描述需求场景
3. 提供实现方案
4. 参与技术讨论

## 许可证

本项目采用GPL v2或更高版本许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

- **官方网站**: https://yinghezhinan.com
- **技术支持**: <EMAIL>
- **GitHub**: https://github.com/yinghezhinan/theme
- **文档**: https://docs.yinghezhinan.com

---

*最后更新: 2025年8月*