# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.*
!.env.example

# Claude specific directories
.claude/

# Data directory (contains sensitive information)
data/
!data/.gitkeep

# Redis data directory
redis_data/

# Logs directory
logs/
*.log
startup.log
app.log

# Configuration files (may contain sensitive data)
config/config.js
!config/config.example.js

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*~

# Archive files (unless specifically needed)
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Application specific files
# JWT secrets and encryption keys
secrets/
keys/
certs/

# Database dumps
*.sql
*.db
*.sqlite
*.sqlite3

# Redis dumps
dump.rdb
appendonly.aof

# PM2 files
ecosystem.config.js
.pm2/

# Docker files (keep main ones, ignore volumes)
.docker/
docker-volumes/

# Monitoring data
prometheus/
grafana/

# Test files and coverage
test-results/
coverage/
.nyc_output/

# Documentation build
docs/build/
docs/dist/

# Deployment files
deploy/
.deploy/

# Package lock files (choose one)
# Uncomment the one you DON'T want to track
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local development files
.local/
local/

# Debug files
debug.log
error.log
access.log

# Session files
sessions/

# Upload directories
uploads/
files/

# Cache directories
.cache/
cache/

# Build artifacts
build/
dist/
out/

# Runtime files
*.sock