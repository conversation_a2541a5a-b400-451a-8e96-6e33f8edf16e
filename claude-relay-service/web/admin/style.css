:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --surface-color: rgba(255, 255, 255, 0.95);
    --glass-color: rgba(255, 255, 255, 0.1);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: rgba(255, 255, 255, 0.2);
}

/* 通用transition - 仅应用于特定元素 */
body, div, button, input, select, textarea, table, tr, td, th, span, p, h1, h2, h3, h4, h5, h6 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(240, 147, 251, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.glass {
    background: var(--glass-color);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-strong {
    background: var(--surface-color);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tab-btn {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tab-btn:hover::before {
    left: 100%;
}

.tab-btn.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: 
        0 10px 15px -3px rgba(102, 126, 234, 0.3),
        0 4px 6px -2px rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

.card {
    background: var(--surface-color);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 24px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn {
    font-weight: 500;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: 
        0 10px 15px -3px rgba(102, 126, 234, 0.3),
        0 4px 6px -2px rgba(102, 126, 234, 0.05);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 
        0 20px 25px -5px rgba(102, 126, 234, 0.3),
        0 10px 10px -5px rgba(102, 126, 234, 0.1);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
    box-shadow: 
        0 10px 15px -3px rgba(16, 185, 129, 0.3),
        0 4px 6px -2px rgba(16, 185, 129, 0.05);
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 
        0 20px 25px -5px rgba(16, 185, 129, 0.3),
        0 10px 10px -5px rgba(16, 185, 129, 0.1);
}

.btn-danger {
    background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
    color: white;
    box-shadow: 
        0 10px 15px -3px rgba(239, 68, 68, 0.3),
        0 4px 6px -2px rgba(239, 68, 68, 0.05);
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 
        0 20px 25px -5px rgba(239, 68, 68, 0.3),
        0 10px 10px -5px rgba(239, 68, 68, 0.1);
}

.form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 10px 15px -3px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

.table-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.table-row {
    transition: all 0.2s ease;
}

.table-row:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.005);
}

.modal {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
}

.header-title {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 2px solid white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
    transition: all 0.3s ease;
}

.slide-up-enter-from {
    opacity: 0;
    transform: translateY(30px);
}

.slide-up-leave-to {
    opacity: 0;
    transform: translateY(-30px);
}

.toast {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    min-width: 320px;
    max-width: 500px;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.toast-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.toast-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.toast-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

[v-cloak] { 
    display: none; 
}



/* 自定义滚动条样式 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) rgba(102, 126, 234, 0.05);
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.4) 100%);
    border-radius: 10px;
    transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 100%);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

/* 弹窗滚动内容样式 */
.modal-scroll-content {
    max-height: calc(90vh - 160px);
    overflow-y: auto;
    padding-right: 8px;
}

@media (max-width: 768px) {
    .glass, .glass-strong {
        margin: 16px;
        border-radius: 20px;
    }
    
    .stat-card {
        padding: 16px;
    }
    
    .tab-btn {
        font-size: 14px;
        padding: 12px 8px;
    }
    
    .modal-scroll-content {
        max-height: calc(85vh - 120px);
    }
}

/* 版本更新提醒动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* 用户菜单下拉框优化 */
.user-menu-dropdown {
    min-width: 240px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}