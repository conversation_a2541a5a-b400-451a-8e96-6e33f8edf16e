<?php
/**
 * 硬核指南主题 - 主题功能文件
 * 
 * @package YingheTheme
 * @subpackage Functions
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 定义主题常量
 */
define('YINGHE_THEME_VERSION', '1.0.0');
define('YINGHE_THEME_DIR', get_template_directory());
define('YINGHE_THEME_URI', get_template_directory_uri());
define('YINGHE_INCLUDES_DIR', YINGHE_THEME_DIR . '/includes');
define('YINGHE_COMPONENTS_DIR', YINGHE_THEME_DIR . '/components');
define('YINGHE_ASSETS_URI', YINGHE_THEME_URI . '/assets');

/**
 * 加载核心类文件
 */
require_once YINGHE_INCLUDES_DIR . '/abstract-class-component.php';
require_once YINGHE_INCLUDES_DIR . '/class-component-loader.php';
require_once YINGHE_INCLUDES_DIR . '/class-asset-manager.php';
require_once YINGHE_INCLUDES_DIR . '/class-ajax-handler.php';
require_once YINGHE_INCLUDES_DIR . '/class-security.php';
require_once YINGHE_INCLUDES_DIR . '/class-customizer.php';
require_once YINGHE_INCLUDES_DIR . '/theme-helpers.php';

/**
 * 主题设置类
 */
class YingheTheme {
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('after_setup_theme', [$this, 'theme_setup']);
        add_action('init', [$this, 'init']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_head', [$this, 'add_ajax_config'], 1);
        add_action('widgets_init', [$this, 'widgets_init']);
        
        // 初始化核心类
        $this->init_core_classes();
    }
    
    /**
     * 主题设置
     */
    public function theme_setup() {
        // 启用主题功能
        add_theme_support('post-thumbnails');
        add_theme_support('html5', [
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
            'script',
            'style'
        ]);
        add_theme_support('customize-selective-refresh-widgets');
        add_theme_support('responsive-embeds');
        add_theme_support('wp-block-styles');
        add_theme_support('align-wide');
        
        // 设置内容宽度
        if (!isset($content_width)) {
            $content_width = 1200;
        }
        
        // 注册导航菜单
        register_nav_menus([
            'primary' => __('主导航菜单', 'yinghe'),
            'footer' => __('底部菜单', 'yinghe'),
        ]);
        
        // 添加图片尺寸
        add_image_size('site-icon', 64, 64, true);
        add_image_size('site-icon-large', 128, 128, true);
        
        // 国际化支持
        load_theme_textdomain('yinghe', get_template_directory() . '/languages');
    }
    
    /**
     * 初始化
     */
    public function init() {
        // 注册自定义文章类型
        $this->register_post_types();
        
        // 注册分类法
        $this->register_taxonomies();
        
        // 创建数据库表
        $this->create_database_tables();
    }
    
    /**
     * 注册自定义文章类型
     */
    private function register_post_types() {
        // 网站收录文章类型
        register_post_type('sites', [
            'labels' => [
                'name' => __('网站收录', 'yinghe'),
                'singular_name' => __('网站', 'yinghe'),
                'add_new' => __('添加网站', 'yinghe'),
                'add_new_item' => __('添加新网站', 'yinghe'),
                'edit_item' => __('编辑网站', 'yinghe'),
                'new_item' => __('新网站', 'yinghe'),
                'view_item' => __('查看网站', 'yinghe'),
                'search_items' => __('搜索网站', 'yinghe'),
                'not_found' => __('未找到网站', 'yinghe'),
                'not_found_in_trash' => __('回收站中未找到网站', 'yinghe'),
            ],
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'rewrite' => ['slug' => 'sites'],
            'capability_type' => 'post',
            'has_archive' => true,
            'hierarchical' => false,
            'menu_position' => 20,
            'menu_icon' => 'dashicons-admin-links',
            'supports' => ['title', 'editor', 'thumbnail', 'custom-fields', 'excerpt'],
            'show_in_rest' => true,
        ]);
    }
    
    /**
     * 注册分类法
     */
    private function register_taxonomies() {
        // 网站分类
        register_taxonomy('site_category', 'sites', [
            'labels' => [
                'name' => __('网站分类', 'yinghe'),
                'singular_name' => __('分类', 'yinghe'),
                'search_items' => __('搜索分类', 'yinghe'),
                'all_items' => __('所有分类', 'yinghe'),
                'parent_item' => __('父级分类', 'yinghe'),
                'parent_item_colon' => __('父级分类:', 'yinghe'),
                'edit_item' => __('编辑分类', 'yinghe'),
                'update_item' => __('更新分类', 'yinghe'),
                'add_new_item' => __('添加新分类', 'yinghe'),
                'new_item_name' => __('新分类名称', 'yinghe'),
                'menu_name' => __('网站分类', 'yinghe'),
            ],
            'hierarchical' => true,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => ['slug' => 'category'],
            'show_in_rest' => true,
        ]);
        
        // 网站标签
        register_taxonomy('sitetag', 'sites', [
            'labels' => [
                'name' => __('网站标签', 'yinghe'),
                'singular_name' => __('标签', 'yinghe'),
                'search_items' => __('搜索标签', 'yinghe'),
                'popular_items' => __('热门标签', 'yinghe'),
                'all_items' => __('所有标签', 'yinghe'),
                'edit_item' => __('编辑标签', 'yinghe'),
                'update_item' => __('更新标签', 'yinghe'),
                'add_new_item' => __('添加新标签', 'yinghe'),
                'new_item_name' => __('新标签名称', 'yinghe'),
                'separate_items_with_commas' => __('用逗号分隔标签', 'yinghe'),
                'add_or_remove_items' => __('添加或移除标签', 'yinghe'),
                'choose_from_most_used' => __('从最常用的标签中选择', 'yinghe'),
                'menu_name' => __('网站标签', 'yinghe'),
            ],
            'hierarchical' => false,
            'public' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => ['slug' => 'sitetag'],
            'show_in_rest' => true,
        ]);
    }
    
    /**
     * 创建数据库表
     */
    private function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // 访问统计表
        $visits_table = $wpdb->prefix . 'yinghe_site_visits';
        $visits_sql = "CREATE TABLE $visits_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            site_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NULL,
            user_ip varchar(45) NOT NULL,
            user_agent text,
            user_fingerprint varchar(64),
            visit_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            visit_duration int(11) DEFAULT NULL,
            page_url text,
            referer_url text,
            device_type enum('desktop','mobile','tablet','other') DEFAULT 'other',
            browser_name varchar(50),
            browser_version varchar(20),
            os_name varchar(50),
            os_version varchar(20),
            country_code char(2),
            region varchar(100),
            city varchar(100),
            click_count int(11) DEFAULT 1,
            bounce boolean DEFAULT FALSE,
            converted boolean DEFAULT FALSE,
            screen_resolution varchar(20),
            viewport_size varchar(20),
            color_depth int(3),
            timezone varchar(50),
            utm_source varchar(100),
            utm_medium varchar(100),
            utm_campaign varchar(100),
            utm_term varchar(100),
            utm_content varchar(100),
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_site_id (site_id),
            INDEX idx_user_id (user_id),
            INDEX idx_visit_time (visit_time),
            INDEX idx_user_ip (user_ip),
            INDEX idx_device_type (device_type)
        ) $charset_collate;";
        
        // 搜索引擎配置表
        $engines_table = $wpdb->prefix . 'yinghe_search_engines';
        $engines_sql = "CREATE TABLE $engines_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            display_name varchar(100) NOT NULL,
            description text,
            search_url text NOT NULL,
            search_method enum('GET','POST') DEFAULT 'GET',
            encoding varchar(20) DEFAULT 'UTF-8',
            placeholder text,
            button_text varchar(50) DEFAULT '搜索',
            icon_url text,
            category enum('yingshi','ziliao','yinyue','yuedu','yule','gongju') NOT NULL DEFAULT 'yingshi',
            category_display varchar(50),
            is_active tinyint(1) NOT NULL DEFAULT 1,
            is_default tinyint(1) NOT NULL DEFAULT 0,
            sort_order int(11) NOT NULL DEFAULT 0,
            weight int(11) DEFAULT 1,
            usage_count int(11) DEFAULT 0,
            last_used datetime,
            response_time int(11),
            success_rate decimal(5,2),
            last_checked datetime,
            status enum('active','slow','error','maintenance') DEFAULT 'active',
            requires_proxy tinyint(1) DEFAULT 0,
            rate_limit int(11),
            api_key varchar(255),
            custom_headers json,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY uk_name_category (name, category),
            INDEX idx_category (category),
            INDEX idx_active (is_active),
            INDEX idx_sort_order (sort_order)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($visits_sql);
        dbDelta($engines_sql);
        
        // 插入默认搜索引擎数据
        $this->insert_default_search_engines();
    }
    
    /**
     * 插入默认搜索引擎数据
     */
    private function insert_default_search_engines() {
        global $wpdb;
        
        $engines_table = $wpdb->prefix . 'yinghe_search_engines';
        
        // 检查是否已有数据
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $engines_table");
        if ($count > 0) {
            return; // 已有数据，跳过
        }
        
        $default_engines = [
            [
                'name' => 'qiku',
                'display_name' => '奇库影视',
                'search_url' => 'https://qkys1.cc/vodsearch/%s%-------------.html',
                'placeholder' => '输入你想看的影片名',
                'category' => 'yingshi',
                'sort_order' => 1,
                'is_active' => 1,
                'is_default' => 1
            ],
            [
                'name' => '555movie',
                'display_name' => '555电影',
                'search_url' => 'https://www.55yy6.com/vodsearch/%s%-------------.html',
                'placeholder' => '输入你想看的影片名称',
                'category' => 'yingshi',
                'sort_order' => 2,
                'is_active' => 1,
                'is_default' => 0
            ],
            [
                'name' => 'douban',
                'display_name' => '豆瓣',
                'search_url' => 'https://search.douban.com/movie/subject_search?search_text=%s%',
                'placeholder' => '输入你想查询资料的影片、演员、导演、编剧的名称',
                'category' => 'ziliao',
                'sort_order' => 1,
                'is_active' => 1,
                'is_default' => 0
            ],
            [
                'name' => 'imdb',
                'display_name' => 'IMDb',
                'search_url' => 'https://www.imdb.com/find/?q=%s%',
                'placeholder' => '输入你想查询资料的影片、演员、导演、编剧的名称',
                'category' => 'ziliao',
                'sort_order' => 2,
                'is_active' => 1,
                'is_default' => 0
            ]
        ];
        
        foreach ($default_engines as $engine) {
            $wpdb->insert($engines_table, $engine);
        }
    }
    
    /**
     * 加载样式和脚本
     */
    public function enqueue_assets() {
        // 主题样式
        wp_enqueue_style(
            'yinghe-style',
            get_stylesheet_uri(),
            [],
            YINGHE_THEME_VERSION
        );
        
        // Bootstrap CSS
        wp_enqueue_style(
            'bootstrap',
            YINGHE_ASSETS_URI . '/vendor/bootstrap/bootstrap.min.css',
            [],
            '5.3.2'
        );
        
        // 图标字体
        wp_enqueue_style(
            'yinghe-icons',
            YINGHE_ASSETS_URI . '/fonts/icons.css',
            [],
            YINGHE_THEME_VERSION
        );
        
        // 主题脚本
        wp_enqueue_script(
            'yinghe-main',
            YINGHE_ASSETS_URI . '/js/main.min.js',
            ['jquery'],
            YINGHE_THEME_VERSION,
            true
        );
        
        // 本地化脚本
        wp_localize_script('yinghe-main', 'yingheConfig', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yinghe_ajax_nonce'),
            'apiUrl' => rest_url('yinghe/v1/'),
            'currentUser' => get_current_user_id(),
            'translations' => [
                'loading' => __('加载中...', 'yinghe'),
                'error' => __('发生错误', 'yinghe'),
                'success' => __('操作成功', 'yinghe'),
                'confirm' => __('确认操作', 'yinghe'),
                'cancel' => __('取消', 'yinghe'),
                'close' => __('关闭', 'yinghe'),
            ],
            'settings' => [
                'theme_mode' => get_theme_mod('default_theme_mode', 'light'),
                'animation_enabled' => get_theme_mod('enable_animations', true),
                'lazy_load_enabled' => get_theme_mod('enable_lazy_loading', true),
            ]
        ]);
    }
    
    /**
     * 注册小工具区域
     */
    public function widgets_init() {
        register_sidebar([
            'name' => __('侧边栏', 'yinghe'),
            'id' => 'sidebar-1',
            'description' => __('主侧边栏小工具区域', 'yinghe'),
            'before_widget' => '<div id="%1$s" class="widget %2$s">',
            'after_widget' => '</div>',
            'before_title' => '<h3 class="widget-title">',
            'after_title' => '</h3>',
        ]);
        
        register_sidebar([
            'name' => __('底部小工具区域', 'yinghe'),
            'id' => 'footer-widgets',
            'description' => __('显示在页面底部的小工具区域', 'yinghe'),
            'before_widget' => '<div id="%1$s" class="footer-widget widget %2$s">',
            'after_widget' => '</div>',
            'before_title' => '<h4 class="widget-title">',
            'after_title' => '</h4>',
        ]);
    }
    
    /**
     * 初始化核心类
     */
    private function init_core_classes() {
        // 组件加载器
        YingheComponentLoader::get_instance();
        
        // 资源管理器
        new YingheAssetManager();
        
        // AJAX处理器
        new YingheAjaxHandler();
        
        // 安全管理器
        new YingheSecurity();
        
        // 主题定制器
        new YingheCustomizer();
    }
    
    /**
     * 添加 AJAX 配置到页面头部
     */
    public function add_ajax_config() {
        $config = [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yinghe_ajax_nonce'),
            'isLoggedIn' => is_user_logged_in(),
            'themeUri' => get_template_directory_uri(),
            'homeUrl' => home_url('/'),
            'currentTheme' => $this->get_current_theme_mode(),
        ];
        
        echo "<script type='text/javascript'>\n";
        echo "var yingheConfig = " . json_encode($config) . ";\n";
        echo "</script>\n";
    }
    
    /**
     * 获取当前主题模式
     * 
     * @return string
     */
    private function get_current_theme_mode() {
        // 检查 Cookie
        if (isset($_COOKIE['io_night_mode'])) {
            return $_COOKIE['io_night_mode'] === '0' ? 'dark' : 'light';
        }
        
        // 检查用户设置
        if (is_user_logged_in()) {
            $user_theme = get_user_meta(get_current_user_id(), 'yinghe_theme_mode', true);
            if ($user_theme) {
                return $user_theme;
            }
        }
        
        // 使用默认设置
        return get_theme_mod('default_theme_mode', 'light');
    }
}

/**
 * 初始化主题
 */
function yinghe_init_theme() {
    return YingheTheme::get_instance();
}
add_action('init', 'yinghe_init_theme');

/**
 * 主题激活钩子
 */
function yinghe_theme_activation() {
    // 刷新重写规则
    flush_rewrite_rules();
    
    // 设置默认选项
    set_theme_mod('default_theme_mode', 'light');
    set_theme_mod('enable_animations', true);
    set_theme_mod('enable_lazy_loading', true);
    set_theme_mod('show_visit_count', true);
}
register_activation_hook(__FILE__, 'yinghe_theme_activation');

/**
 * 工具函数
 */

// 访问统计和记录函数已移至 includes/theme-helpers.php 文件中

/**
 * 获取搜索引擎列表
 */
function yinghe_get_search_engines($category = null, $active_only = true) {
    global $wpdb;
    
    $table = $wpdb->prefix . 'yinghe_search_engines';
    $where = [];
    $params = [];
    
    if ($category) {
        $where[] = "category = %s";
        $params[] = $category;
    }
    
    if ($active_only) {
        $where[] = "is_active = 1";
    }
    
    $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
    $sql = "SELECT * FROM $table $where_clause ORDER BY category, sort_order ASC";
    
    if (!empty($params)) {
        return $wpdb->get_results($wpdb->prepare($sql, $params));
    } else {
        return $wpdb->get_results($sql);
    }
}

/**
 * 获取网站分类树
 */
function yinghe_get_site_categories_tree($parent_id = 0) {
    $categories = get_terms([
        'taxonomy' => 'site_category',
        'hide_empty' => false,
        'parent' => $parent_id,
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ]);
    
    $tree = [];
    foreach ($categories as $category) {
        $category->children = yinghe_get_site_categories_tree($category->term_id);
        $tree[] = $category;
    }
    
    return $tree;
}

/**
 * 安全地输出网站URL
 */
function yinghe_esc_site_url($url) {
    return esc_url($url);
}

/**
 * 安全地输出网站标题
 */
function yinghe_esc_site_title($title) {
    return esc_html($title);
}

/**
 * 安全地输出网站描述
 */
function yinghe_esc_site_description($description) {
    return wp_kses_post($description);
}