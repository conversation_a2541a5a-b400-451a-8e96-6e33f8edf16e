# WordPress 主题开发项目

## 项目目标
将 /Users/<USER>/website_data/suxing/wp-content/themes/yinhedaohang/Pg_yinghezhinan.com 的 HTML 原型转换为 WordPress 主题，要求 100% 视觉和功能还原。

## 开发位置
直接在项目根目录开发，方便实时测试。

## 开发原则
1. **必须遵循** `docs/wordpress-component-based-dev.md` 的组件化开发模式
2. **参考** `docs/wordpress-dev-rules-for-claude-code.md` 的开发规范
3. **理解** `docs/wordpress-ai-dev-complete.md` 的架构理论

## 核心要求
- 100% 精确复刻原型的视觉和交互
- 使用组件化架构
- 保持原有的所有功能
- 遵循 WordPress 最佳实践

## 请自行分析
- 原型的技术栈和依赖
- 合适的组件划分
- 资源文件的组织方式
- 根据三个文档决定最佳实践

## ⚠️ 绝对禁止
- 修改任何CSS属性值
- 更改HTML标签结构
- 优化JavaScript代码
- 合并或压缩文件
- 使用WordPress自带的jQuery
- 更改类名或ID
- 添加新的样式规则