# WordPress 主题转换规格说明
*硬核指南 - WordPress 组件化主题开发规格*

## 📋 项目概览

### 目标
将 `Pg_yinghezhinan.com/` HTML 原型 100% 精确转换为 WordPress 主题，使用组件化开发模式。

### 设计原则
- **100% 视觉还原** - 保持原型的所有视觉效果和交互
- **组件化架构** - 遵循 `docs/wordpress-component-based-dev.md` 的模式
- **安全优先** - 实施 WordPress 安全最佳实践
- **性能优化** - 解决原型中的性能瓶颈
- **可维护性** - 采用模块化、可扩展的代码结构

---

## 🗄️ WordPress 数据结构映射

### 1. 自定义文章类型 (Custom Post Types)

#### Sites (网站收录)
```php
register_post_type('sites', [
    'label' => '网站收录',
    'public' => true,
    'supports' => ['title', 'editor', 'thumbnail', 'custom-fields'],
    'has_archive' => true,
    'rewrite' => ['slug' => 'sites'],
    'menu_icon' => 'dashicons-admin-links',
    'show_in_rest' => true,
]);
```

**字段映射:**
- `post_title` → 网站名称
- `post_content` → 网站描述
- `post_excerpt` → 短描述
- `_thumbnail_id` → 网站图标
- Meta Fields:
  - `site_url` → 目标网站 URL
  - `site_icon` → 网站图标 URL
  - `site_tags` → 网站标签 (数组)
  - `visit_count` → 访问统计
  - `display_mode` → 显示模式 (mini/default/max)
  - `external_url` → 外部跳转链接
  - `tooltip_text` → 悬停提示文字
  - `is_featured` → 是否推荐
  - `quality_tags` → 质量标签 (4K、高清等)

### 2. 分类系统 (Taxonomies)

#### Site Categories (网站分类)
```php
register_taxonomy('site_category', 'sites', [
    'label' => '网站分类',
    'hierarchical' => true,
    'public' => true,
    'show_in_rest' => true,
    'rewrite' => ['slug' => 'category'],
]);
```

**分类结构:**
```
影视 (term-167)
├── 在线看 (term-167-3)
├── 下载 (term-167-15)
├── 网盘 (term-167-14)
├── 字幕 (term-167-18)
├── APP (term-167-7)
└── 神器 (term-167-211)

二次元 (term-166)
├── 动漫 (term-166-13)
├── 漫画 (term-166-149)
├── 下载 (term-166-169)
└── 神器 (term-166-180)

音乐 (term-225)
├── 听歌 (term-225-156)
├── 无损音乐 (term-225-239)
├── 电台 (term-225-224)
└── 曲艺 (term-225-273)

阅读 (term-237)
├── 电子书 (term-237-238)
├── 小说 (term-237-190)
├── 听书 (term-237-240)
└── 报刊杂志 (term-237-271)

娱乐 (term-165)
├── 游戏 (term-165-170)
├── 壁纸 (term-165-181)
└── 电视直播 (term-165-194)

工具箱 (term-248)
├── AI助手 (term-248-249)
└── 在线工具 (term-248-250)

省钱助手 (term-100)
└── 每天帮你省一点点 (term-100)
```

#### Site Tags (网站标签)
```php
register_taxonomy('sitetag', 'sites', [
    'label' => '网站标签',
    'hierarchical' => false,
    'public' => true,
    'show_in_rest' => true,
    'rewrite' => ['slug' => 'sitetag'],
]);
```

**标签类型:**
- **推荐标签**: 硬核推荐、高画质、弹幕多、4K画质、高清线路多、无广告、可缓存
- **平台标签**: Android客户端、iOS客户端、Windows软件、macOS、Linux、网页端、TV客户端、车机客户端
- **下载标签**: 直链下载、迅雷下载、夸克网盘、百度网盘、阿里云盘、迅雷网盘、115网盘、天翼网盘、UC网盘
- **质量标签**: 4K、1080P、720P、Hi-Res、无损音乐、标准音质

### 3. 自定义数据表

#### 访问统计表
```sql
CREATE TABLE {$wpdb->prefix}site_visits (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    site_id bigint(20) unsigned NOT NULL,
    user_ip varchar(45) NOT NULL,
    user_agent text,
    visit_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    referer_url text,
    PRIMARY KEY (id),
    INDEX idx_site_id (site_id),
    INDEX idx_visit_time (visit_time)
);
```

#### 搜索配置表
```sql
CREATE TABLE {$wpdb->prefix}search_engines (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    name varchar(100) NOT NULL,
    search_url text NOT NULL,
    placeholder text,
    category enum('yingshi','ziliao') NOT NULL DEFAULT 'yingshi',
    is_active tinyint(1) NOT NULL DEFAULT 1,
    sort_order int(11) NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);
```

---

## 🧩 组件架构设计

### 1. 布局组件 (Layout Components)

#### SidebarNav 组件
**文件**: `components/layout/sidebar-nav.php`
```php
/**
 * Component: SidebarNav
 * Description: 固定侧边栏导航组件
 * Props: 
 *   - menu_items (array): 菜单项数据
 *   - logo_config (array): Logo配置
 *   - current_category (string): 当前激活分类
 *   - show_domain_info (bool): 是否显示域名信息
 */
```

**Props 结构:**
```php
$props = [
    'menu_items' => get_site_categories_tree(),
    'logo_config' => [
        'expanded' => get_theme_mod('logo_expanded'),
        'collapsed' => get_theme_mod('logo_collapsed'),
        'alt_text' => get_bloginfo('name')
    ],
    'current_category' => get_queried_object_id(),
    'show_domain_info' => get_theme_mod('show_domain_info', true),
    'domain_links' => get_option('yinghe_domain_links', [])
];
```

#### HeaderSystem 组件
**文件**: `components/layout/header-system.php`
```php
/**
 * Component: HeaderSystem
 * Description: 双重头部系统（导航 + 搜索）
 * Props:
 *   - show_mini_header (bool): 是否显示迷你头部
 *   - search_config (array): 搜索引擎配置
 *   - announcement_config (array): 公告配置
 */
```

#### MainContent 组件
**文件**: `components/layout/main-content.php`
```php
/**
 * Component: MainContent
 * Description: 主内容区域容器
 * Props:
 *   - container_class (string): 容器CSS类
 *   - max_width (string): 最大宽度
 */
```

### 2. 交互组件 (Interactive Components)

#### WebsiteCard 组件
**文件**: `components/content/website-card.php`
```php
/**
 * Component: WebsiteCard
 * Description: 网站卡片组件，支持多种显示模式
 * Props:
 *   - site_id (int): 网站文章ID
 *   - display_mode (string): 显示模式 mini|default|max
 *   - show_visit_count (bool): 是否显示访问统计
 *   - enable_tracking (bool): 是否启用访问跟踪
 *   - custom_class (string): 自定义CSS类
 */
```

**使用示例:**
```php
// 在模板中使用
render_website_card([
    'site_id' => get_the_ID(),
    'display_mode' => 'default',
    'show_visit_count' => true,
    'enable_tracking' => true,
    'custom_class' => 'featured-site'
]);
```

#### SearchInterface 组件
**文件**: `components/forms/search-interface.php`
```php
/**
 * Component: SearchInterface
 * Description: 多引擎搜索界面
 * Props:
 *   - search_engines (array): 搜索引擎列表
 *   - default_engine (string): 默认搜索引擎
 *   - enable_suggestions (bool): 是否启用搜索建议
 *   - placeholder_text (string): 占位符文本
 */
```

#### ModalSystem 组件
**文件**: `components/overlays/modal-system.php`
```php
/**
 * Component: ModalSystem
 * Description: 模态窗口系统，支持多标签内容
 * Props:
 *   - modal_id (string): 模态窗口ID
 *   - content_tabs (array): 标签页内容
 *   - auto_close (bool): 是否自动关闭
 *   - close_on_outside_click (bool): 点击外部是否关闭
 */
```

#### ThemeSwitcher 组件
**文件**: `components/utilities/theme-switcher.php`
```php
/**
 * Component: ThemeSwitcher
 * Description: 深色/浅色主题切换器
 * Props:
 *   - position (string): 位置 (fixed|relative)
 *   - default_theme (string): 默认主题
 *   - save_preference (bool): 是否保存用户偏好
 */
```

### 3. 功能组件 (Utility Components)

#### MiniSearch 组件
**文件**: `components/forms/mini-search.php`
```php
/**
 * Component: MiniSearch
 * Description: 迷你搜索组件，集成在网站卡片中
 * Props:
 *   - search_url (string): 搜索URL模板
 *   - placeholder (string): 占位符文本
 *   - button_text (string): 按钮文本
 */
```

#### FilterSystem 组件
**文件**: `components/navigation/filter-system.php`
```php
/**
 * Component: FilterSystem
 * Description: 网站筛选系统
 * Props:
 *   - filter_categories (array): 筛选分类
 *   - selected_filters (array): 已选择的筛选项
 *   - ajax_enabled (bool): 是否启用AJAX筛选
 */
```

#### VisitTracker 组件
**文件**: `components/analytics/visit-tracker.php`
```php
/**
 * Component: VisitTracker
 * Description: 访问统计跟踪组件
 * Props:
 *   - site_id (int): 网站ID
 *   - track_ip (bool): 是否记录IP
 *   - track_user_agent (bool): 是否记录User Agent
 */
```

---

## 🔒 安全架构设计

### 1. 输入验证与过滤

#### 数据清理类
```php
class YingheSecuritySanitizer {
    public static function sanitize_site_url($url) {
        $url = esc_url_raw($url);
        // 验证URL格式和安全性
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        return $url;
    }
    
    public static function sanitize_search_query($query) {
        $query = sanitize_text_field($query);
        // 移除潜在的恶意字符
        $query = preg_replace('/[<>"\']/', '', $query);
        return $query;
    }
}
```

### 2. AJAX 安全处理

#### 访问统计 AJAX 处理器
```php
class YingheAjaxHandler {
    public function __construct() {
        add_action('wp_ajax_record_site_visit', [$this, 'record_site_visit']);
        add_action('wp_ajax_nopriv_record_site_visit', [$this, 'record_site_visit']);
    }
    
    public function record_site_visit() {
        // 验证 nonce
        if (!wp_verify_nonce($_POST['nonce'], 'yinghe_visit_nonce')) {
            wp_send_json_error('安全验证失败');
        }
        
        // 验证站点ID
        $site_id = intval($_POST['site_id']);
        if (!get_post($site_id) || get_post_type($site_id) !== 'sites') {
            wp_send_json_error('无效的站点ID');
        }
        
        // 记录访问
        $this->log_visit($site_id);
        wp_send_json_success('访问记录成功');
    }
}
```

### 3. 输出转义

#### 模板安全输出函数
```php
function yinghe_esc_site_url($url) {
    return esc_url($url);
}

function yinghe_esc_site_title($title) {
    return esc_html($title);
}

function yinghe_esc_site_description($description) {
    return wp_kses_post($description);
}
```

---

## ⚡ 性能优化策略

### 1. 资源管理

#### 资源注册系统
```php
class YingheAssetManager {
    public function __construct() {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_theme_assets']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_component_assets']);
    }
    
    public function enqueue_theme_assets() {
        // 核心样式文件
        wp_enqueue_style(
            'yinghe-core', 
            get_template_directory_uri() . '/assets/css/core.min.css',
            [],
            filemtime(get_template_directory() . '/assets/css/core.min.css')
        );
        
        // 主题脚本
        wp_enqueue_script(
            'yinghe-main',
            get_template_directory_uri() . '/assets/js/main.min.js',
            ['jquery'],
            filemtime(get_template_directory() . '/assets/js/main.min.js'),
            true
        );
        
        // 本地化脚本
        wp_localize_script('yinghe-main', 'yingheAjax', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yinghe_ajax_nonce'),
            'translations' => [
                'loading' => __('加载中...', 'yinghe'),
                'error' => __('发生错误', 'yinghe'),
            ]
        ]);
    }
}
```

### 2. 缓存策略

#### 查询缓存系统
```php
class YingheQueryCache {
    private $cache_group = 'yinghe_queries';
    
    public function get_sites_by_category($category_id, $limit = 12) {
        $cache_key = "sites_cat_{$category_id}_limit_{$limit}";
        $sites = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $sites) {
            $sites = get_posts([
                'post_type' => 'sites',
                'numberposts' => $limit,
                'tax_query' => [
                    [
                        'taxonomy' => 'site_category',
                        'field' => 'term_id',
                        'terms' => $category_id,
                    ]
                ]
            ]);
            
            wp_cache_set($cache_key, $sites, $this->cache_group, HOUR_IN_SECONDS);
        }
        
        return $sites;
    }
}
```

### 3. 图片优化

#### 响应式图片处理
```php
function yinghe_responsive_site_icon($site_id, $sizes = ['32x32', '64x64']) {
    $icon_url = get_post_meta($site_id, 'site_icon', true);
    if (!$icon_url) {
        return get_template_directory_uri() . '/assets/images/default-icon.webp';
    }
    
    $srcset = [];
    foreach ($sizes as $size) {
        $srcset[] = $icon_url . ' ' . $size;
    }
    
    return [
        'src' => $icon_url,
        'srcset' => implode(', ', $srcset),
        'sizes' => '(max-width: 768px) 24px, 32px'
    ];
}
```

---

## 📱 响应式设计实现

### 1. 断点系统

#### CSS 断点配置
```scss
$breakpoints: (
    'mobile': 768px,
    'tablet': 992px,
    'desktop': 1200px,
    'wide': 1400px
);

@mixin respond-to($breakpoint) {
    @if map-has-key($breakpoints, $breakpoint) {
        @media (min-width: map-get($breakpoints, $breakpoint)) {
            @content;
        }
    }
}
```

### 2. 移动端优化

#### 移动端侧边栏组件
```php
/**
 * Component: MobileSidebar
 * Description: 移动端滑出式侧边栏
 * Props:
 *   - menu_items (array): 菜单项
 *   - overlay_close (bool): 点击遮罩层关闭
 *   - gesture_close (bool): 手势关闭
 */
```

---

## 🎨 主题定制系统

### 1. Customizer 配置

#### 主题选项注册
```php
class YingheCustomizer {
    public function __construct() {
        add_action('customize_register', [$this, 'register_customizer_options']);
    }
    
    public function register_customizer_options($wp_customize) {
        // 基础设置面板
        $wp_customize->add_panel('yinghe_basic', [
            'title' => '硬核指南基础设置',
            'priority' => 10,
        ]);
        
        // Logo 设置
        $wp_customize->add_section('yinghe_logo', [
            'title' => 'Logo 设置',
            'panel' => 'yinghe_basic',
        ]);
        
        $wp_customize->add_setting('logo_expanded', [
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ]);
        
        $wp_customize->add_control(new WP_Customize_Image_Control(
            $wp_customize,
            'logo_expanded',
            [
                'label' => '展开状态 Logo',
                'section' => 'yinghe_logo',
            ]
        ));
    }
}
```

### 2. 管理后台

#### 站点管理界面
```php
class YingheSiteManager {
    public function __construct() {
        add_action('init', [$this, 'add_meta_boxes']);
        add_action('save_post', [$this, 'save_site_meta']);
    }
    
    public function add_meta_boxes() {
        add_meta_box(
            'yinghe_site_details',
            '网站详情',
            [$this, 'render_site_meta_box'],
            'sites',
            'normal',
            'high'
        );
    }
    
    public function render_site_meta_box($post) {
        wp_nonce_field('yinghe_site_meta', 'yinghe_site_meta_nonce');
        
        $site_url = get_post_meta($post->ID, 'site_url', true);
        $site_icon = get_post_meta($post->ID, 'site_icon', true);
        $display_mode = get_post_meta($post->ID, 'display_mode', true);
        
        include get_template_directory() . '/admin/templates/site-meta-box.php';
    }
}
```

---

## 🚀 部署与维护

### 1. 文件结构

```
yinhedaohang/
├── style.css                 # 主题样式表
├── functions.php             # 主题函数
├── index.php                # 首页模板
├── single-sites.php          # 单个网站页面
├── taxonomy-site_category.php # 分类页面
├── assets/                   # 静态资源
│   ├── css/
│   ├── js/
│   └── images/
├── components/               # 组件目录
│   ├── layout/              # 布局组件
│   ├── content/             # 内容组件
│   ├── forms/               # 表单组件
│   ├── navigation/          # 导航组件
│   ├── overlays/            # 覆盖层组件
│   └── utilities/           # 工具组件
├── includes/                # 核心功能类
│   ├── class-theme-setup.php
│   ├── class-asset-manager.php
│   ├── class-ajax-handler.php
│   └── class-customizer.php
├── admin/                   # 管理后台
│   ├── templates/
│   └── class-site-manager.php
└── languages/               # 语言文件
```

### 2. 激活与初始化

#### 主题激活钩子
```php
class YingheThemeActivator {
    public static function activate() {
        // 创建必要的数据表
        self::create_tables();
        
        // 导入默认数据
        self::import_default_data();
        
        // 设置默认选项
        self::set_default_options();
        
        // 刷新重写规则
        flush_rewrite_rules();
    }
    
    private static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$wpdb->prefix}site_visits (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            site_id bigint(20) unsigned NOT NULL,
            user_ip varchar(45) NOT NULL,
            user_agent text,
            visit_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            referer_url text,
            PRIMARY KEY (id),
            INDEX idx_site_id (site_id),
            INDEX idx_visit_time (visit_time)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

register_activation_hook(__FILE__, ['YingheThemeActivator', 'activate']);
```

---

## ✅ 开发检查清单

### 功能实现
- [ ] 自定义文章类型和分类法注册
- [ ] 组件系统架构实现
- [ ] 安全验证和数据过滤
- [ ] AJAX 处理和nonce验证
- [ ] 响应式设计实现
- [ ] 性能优化和缓存
- [ ] 访问统计功能
- [ ] 搜索引擎集成
- [ ] 主题切换功能
- [ ] 管理后台界面

### 质量保证
- [ ] 代码符合 WordPress 编码标准
- [ ] 所有输入已验证和清理
- [ ] 所有输出已转义
- [ ] AJAX 请求有nonce保护
- [ ] 数据库查询使用预处理语句
- [ ] 错误处理机制完善
- [ ] 国际化支持完整
- [ ] 资源文件正确入队
- [ ] 缓存策略有效实施
- [ ] 移动端体验优化

---

## 📞 技术支持

本规格说明基于以下文档制定：
- `docs/wordpress-component-based-dev.md` - 组件化开发规范
- `docs/wordpress-dev-rules-for-claude-code.md` - WordPress 开发规则
- `docs/wordpress-ai-dev-complete.md` - 架构理论指南

开发过程中如有疑问，请参考相关文档或联系项目维护者。