﻿@font-face {
  font-family: "io"; /* Project id 4152982 */
  src: url('../font/font_4152982_f00lvbu68l.woff2') format('woff2'),
       url('../font/font_4152982_f00lvbu68l.woff') format('woff'),
       url('../font/font_4152982_f00lvbu68l.ttf') format('truetype');
}

.io {
  font-family: "io" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.io-star2:before {
  content: "\e869";
}

.io-xiazai-o:before {
  content: "\e65b";
}

.io-download-o:before {
  content: "\e673";
}

.io-more:before {
  content: "\e661";
}

.io-windows:before {
  content: "\e657";
}

.io-shang:before {
  content: "\e695";
}

.io-cheji:before {
  content: "\e654";
}

.io-mac:before {
  content: "\e689";
}

.io-wechat-o:before {
  content: "\e78f";
}

.io-wechat:before {
  content: "\e660";
}

.io-douyin:before {
  content: "\e683";
}

.io-follow:before {
  content: "\e655";
}

.io-clock1:before {
  content: "\e653";
}

.io-clock:before {
  content: "\e652";
}

.io-biji:before {
  content: "\e837";
}

.io-app:before {
  content: "\e651";
}

.io-sheng3:before {
  content: "\e620";
}

.io-sheng2:before {
  content: "\e650";
}

.io-sheng:before {
  content: "\e64e";
}

.io-read:before {
  content: "\e65c";
}

.io-read3:before {
  content: "\ea23";
}

.io-store:before {
  content: "\e64c";
}

.io-store3:before {
  content: "\e64f";
}

.io-play:before {
  content: "\e87e";
}

.io-play-o:before {
  content: "\e64b";
}

.io-share-o:before {
  content: "\e649";
}

.io-zan-o:before {
  content: "\e64a";
}

.io-pinglun-o:before {
  content: "\e64d";
}

.io-classification:before {
  content: "\e62a";
}

.io-loading:before {
  content: "\e648";
}

.io-adopt:before {
  content: "\e62d";
}

.io-tishi:before {
  content: "\e659";
}

.io-bulletin:before {
  content: "\e629";
}

.io-close:before {
  content: "\e627";
}

.io-taolun:before {
  content: "\e626";
}

.io-time:before {
  content: "\e6e7";
}

.io-arrow-right-o:before {
  content: "\e625";
}

.io-diandian:before {
  content: "\e670";
}

.io-edit:before {
  content: "\e8ea";
}

.io-arrow-bottom:before {
  content: "\e628";
}

.io-arrow-left-o:before {
  content: "\ea22";
}

.io-light:before {
  content: "\e694";
}

.io-refresh:before {
  content: "\e621";
}

.io-zan:before {
  content: "\e656";
}

.io-pinglun:before {
  content: "\e623";
}

.io-night:before {
  content: "\e6fa";
}

.io-view:before {
  content: "\e65d";
}

.io-search-b:before {
  content: "\e66d";
}

.io-warning:before {
  content: "\e701";
}

.io-anzhuo:before {
  content: "\e624";
}

.io-backtop:before {
  content: "\e647";
}

.io-share:before {
  content: "\e622";
}

.io-hash:before {
  content: "\e601";
}

.io-hot:before {
  content: "\e65a";
}

.io-dijia:before {
  content: "\e8ac";
}

.io-sim:before {
  content: "\ea21";
}

.io-fuli:before {
  content: "\e61e";
}

.io-fuli3:before {
  content: "\e61f";
}

.io-erweima:before {
  content: "\e7ae";
}

.io-yishixiao:before {
  content: "\e646";
}

.io-arrow1:before {
  content: "\e600";
}

.io-arrow:before {
  content: "\e645";
}

.io-fufei:before {
  content: "\e77f";
}

.io-pc:before {
  content: "\e882";
}

.io-yingwen:before {
  content: "\e693";
}

.io-fanti1:before {
  content: "\e70e";
}

.io-fanti:before {
  content: "\e6d8";
}

.io-image:before {
  content: "\e61d";
}

.io-image3:before {
  content: "\e644";
}

.io-acg3:before {
  content: "\e61b";
}

.io-acg:before {
  content: "\e61a";
}

.io-headphones3:before {
  content: "\e643";
}

.io-headphones:before {
  content: "\e61c";
}

.io-ios:before {
  content: "\e65f";
}

.io-android:before {
  content: "\e619";
}

.io-tv:before {
  content: "\e618";
}

.io-warn:before {
  content: "\e640";
}

.io-airdrop:before {
  content: "\e617";
}

.io-comments:before {
  content: "\e641";
}

.io-link1:before {
  content: "\e642";
}

.io-comment-dots1:before {
  content: "\e637";
}

.io-eye:before {
  content: "\e63d";
}

.io-sanjiao:before {
  content: "\e658";
}

.io-users:before {
  content: "\e616";
}

.io-search:before {
  content: "\e614";
}

.io-users-three:before {
  content: "\e615";
}

.io-home:before {
  content: "\e613";
}

.io-home1:before {
  content: "\e634";
}

.io-arrow-right:before {
  content: "\e60b";
}

.io-chevron-right:before {
  content: "\e603";
}

.io-comment:before {
  content: "\e60c";
}

.io-comment-dots:before {
  content: "\e60";
}

.io-desktop:before {
  content: "\e61";
}

.io-link:before {
  content: "\e63e";
}

.io-shield:before {
  content: "\e611";
}

.io-send:before {
  content: "\e612";
}

.io-comment1:before {
  content: "\e62e";
}

.io-desktop1:before {
  content: "\e62f";
}

.io-compass3:before {
  content: "\e63f";
}

.io-film3:before {
  content: "\e63";
}

.io-shield1:before {
  content: "\e63c";
}

.io-bookmark1:before {
  content: "\e62c";
}

.io-folder1:before {
  content: "\e63a";
}

.io-heart3:before {
  content: "\e633";
}

.io-tag1:before {
  content: "\e639";
}

.io-star1:before {
  content: "\e63b";
}

.io-bookmark:before {
  content: "\e602";
}

.io-compass:before {
  content: "\e604";
}

.io-film:before {
  content: "\e62";
}

.io-folder:before {
  content: "\e607";
}

.io-heart:before {
  content: "\e609";
}

.io-star:before {
  content: "\e60f";
}

.io-tag:before {
  content: "\e610";
}

.io-download:before {
  content: "\e605";
}

.io-globe:before {
  content: "\e606";
}

.io-inbox:before {
  content: "\e608";
}

.io-package:before {
  content: "\e60a";
}

.io-tablet:before {
  content: "\e60d";
}

.io-video:before {
  content: "\e60e";
}

.io-video3:before {
  content: "\e62b";
}

.io-download3:before {
  content: "\e630";
}

.io-globe1:before {
  content: "\e631";
}

.io-inbox3:before {
  content: "\e632";
}

.io-package3:before {
  content: "\e635";
}

.io-send3:before {
  content: "\e636";
}

.io-tablet3:before {
  content: "\e638";
}

